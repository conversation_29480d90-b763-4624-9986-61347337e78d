<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(isset($page) && $page->meta_title ? $page->meta_title : (isset($title) ? $title . ' - ' : '')); ?><?php echo e($globalSettings->application_name ?? config('app.name', 'Fair Price Ventures')); ?></title>

    <!-- Favicon -->
    <?php if($globalSettings->favicon ?? null): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset('storage/' . $globalSettings->favicon)); ?>">
    <?php endif; ?>

    <!-- SEO Meta Tags -->
    <?php if(isset($page) && $page->meta_description): ?>
        <meta name="description" content="<?php echo e($page->meta_description); ?>">
    <?php elseif($globalSettings->tagline ?? null): ?>
        <meta name="description" content="<?php echo e($globalSettings->tagline); ?>">
    <?php endif; ?>

    <?php if(isset($page) && $page->meta_keywords): ?>
        <meta name="keywords" content="<?php echo e($page->meta_keywords); ?>">
    <?php elseif($globalSettings->seo_keywords ?? null): ?>
        <meta name="keywords" content="<?php echo e($globalSettings->seo_keywords); ?>">
    <?php endif; ?>

    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>">
    <meta property="og:description" content="<?php echo e($globalSettings->tagline ?? 'Your trusted business partner'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo e(url('/')); ?>">
    <?php if($globalSettings->logo ?? null): ?>
        <meta property="og:image" content="<?php echo e(asset('storage/' . $globalSettings->logo)); ?>">
    <?php endif; ?>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:300,400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Top Bar -->
    <div class="bg-gradient-to-r from-green-800 via-green-700 to-green-600 text-white py-2 hidden md:block">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center text-sm">
                <!-- Contact Details -->
                <div class="flex items-center space-x-6">
                    <?php if($globalSettings->phone1 ?? null): ?>
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-2 text-green-200"></i>
                            <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-200 transition-colors duration-300" title="Contact us">
                                <?php echo e($globalSettings->phone1); ?>

                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($globalSettings->email1 ?? null): ?>
                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-green-200"></i>
                            <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-200 transition-colors duration-300" title="Contact us">
                                <?php echo e($globalSettings->email1); ?>

                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($globalSettings->address ?? null): ?>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-green-200"></i>
                            <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-200 transition-colors duration-300" title="Find us">
                                <?php echo e(Str::limit($globalSettings->address, 40)); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Social Media Links -->
                <div class="flex items-center space-x-4">
                    <?php if($globalSettings->facebook_link ?? null): ?>
                        <a href="<?php echo e($globalSettings->facebook_link); ?>" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                    <?php endif; ?>

                    <?php if($globalSettings->twitter_link ?? null): ?>
                        <a href="<?php echo e($globalSettings->twitter_link); ?>" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                    <?php endif; ?>

                    <?php if($globalSettings->linkedin_link ?? null): ?>
                        <a href="<?php echo e($globalSettings->linkedin_link); ?>" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    <?php endif; ?>

                    <?php if($globalSettings->instagram_link ?? null): ?>
                        <a href="<?php echo e($globalSettings->instagram_link); ?>" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    <?php endif; ?>

                    <?php if($globalSettings->youtube_link ?? null): ?>
                        <a href="<?php echo e($globalSettings->youtube_link); ?>" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Subscribe to our YouTube channel">
                            <i class="fab fa-youtube"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Default social links if none are set -->
                    <?php if(!($globalSettings->facebook_link || $globalSettings->twitter_link || $globalSettings->linkedin_link || $globalSettings->instagram_link || $globalSettings->youtube_link)): ?>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo - Left Aligned -->
                <div class="flex items-center">
                    <a href="<?php echo e(route('home')); ?>" class="flex-shrink-0 flex items-center">
                        <?php if($globalSettings->logo ?? null): ?>
                            <img src="<?php echo e(asset('storage/' . $globalSettings->logo)); ?>" alt="<?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>" class="h-12 w-auto mr-3">
                        <?php endif; ?>
                        
                    </a>
                </div>

                <!-- Navigation Links - Split Layout -->
                <div class="hidden md:flex items-center justify-between flex-1 ml-8">
                    <!-- Main Navigation Links -->
                    <div class="flex items-center space-x-2">
                        <?php echo $__env->make('partials.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- CTA and Auth Section -->
                    <div class="flex items-center space-x-4">
                        <!-- Contact CTA Button -->
                        <a href="<?php echo e(route('contact')); ?>" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg text-base font-semibold hover:from-green-700 hover:to-green-800 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <i class="fas fa-phone mr-2"></i>
                            Contact Us
                        </a>

                        <?php if(auth()->guard()->check()): ?>
                            <?php if(auth()->user()->isAdmin() || auth()->user()->isEditor()): ?>
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-md text-base font-semibold hover:bg-gray-700 transition-colors duration-300">
                                    Admin
                                </a>
                            <?php endif; ?>
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-medium transition-colors duration-300">
                                    Logout
                                </button>
                            </form>
                        <?php else: ?>
                            
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button x-data x-on:click="$dispatch('toggle-mobile-menu')" class="text-gray-700 hover:text-green-600 focus:outline-none focus:text-green-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div x-data="{ open: false }" x-on:toggle-mobile-menu.window="open = !open" x-show="open" x-transition class="md:hidden">
            <div class="px-4 pt-4 pb-4 space-y-2 sm:px-6 bg-white border-t">
                <?php echo $__env->make('partials.mobile-navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- Mobile Contact CTA -->
                <div class="pt-4 pb-2">
                    <a href="<?php echo e(route('contact')); ?>" class="block bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-4 rounded-lg text-base font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg text-center">
                        <i class="fas fa-phone mr-2"></i>
                        Contact Us
                    </a>
                </div>

                <?php if(auth()->guard()->check()): ?>
                    <?php if(auth()->user()->isAdmin() || auth()->user()->isEditor()): ?>
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="block bg-gray-600 text-white px-4 py-3 rounded-md text-base font-semibold hover:bg-gray-700 transition-colors duration-300">Admin</a>
                    <?php endif; ?>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="block w-full text-left text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Logout</button>
                    </form>
                <?php else: ?>
                    
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Page Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <?php if($globalSettings->logo ?? null): ?>
                            <img src="<?php echo e(asset('storage/' . $globalSettings->logo)); ?>" alt="<?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>" class="h-8 w-auto">
                        <?php endif; ?>
                        
                    </div>
                    <p class="text-gray-300 mb-4"><?php echo e($globalSettings->tagline ?? 'Your trusted partner in business growth and innovation. We provide exceptional consulting services to help your business thrive.'); ?></p>

                    <!-- Newsletter Signup -->
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold mb-2">Subscribe to our Newsletter</h4>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('newsletter-signup', ['source' => 'footer']);

$__html = app('livewire')->mount($__name, $__params, 'lw-432669701-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <?php echo $__env->make('partials.footer-navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                    <ul class="space-y-2 text-gray-300">
                        <?php if($globalSettings->address ?? null): ?>
                            <li>
                                <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-400 transition-colors duration-300" title="Find us">
                                    <?php echo e($globalSettings->address); ?>

                                </a>
                            </li>
                        <?php else: ?>
                            <li>
                                <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-400 transition-colors duration-300">
                                    123 Business Street<br>
                                    Suite 100<br>
                                    City, State 12345
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if($globalSettings->phone1 ?? null): ?>
                            <li class="mt-4">
                                <strong>Phone:</strong>
                                <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-400 transition-colors duration-300" title="Contact us">
                                    <?php echo e($globalSettings->phone1); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if($globalSettings->email1 ?? null): ?>
                            <li>
                                <strong>Email:</strong>
                                <a href="<?php echo e(route('contact')); ?>" class="hover:text-green-400 transition-colors duration-300" title="Contact us">
                                    <?php echo e($globalSettings->email1); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p class="text-gray-300"><?php echo e($globalSettings->copyright ?? '© ' . date('Y') . ' Fair Price Ventures Ltd. All rights reserved.'); ?></p>
            </div>
        </div>
    </footer>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Global Loading Overlay -->
    <?php if (isset($component)) { $__componentOriginal115e82920da0ed7c897ee494af74b9d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal115e82920da0ed7c897ee494af74b9d8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.loading-overlay','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('loading-overlay'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal115e82920da0ed7c897ee494af74b9d8)): ?>
<?php $attributes = $__attributesOriginal115e82920da0ed7c897ee494af74b9d8; ?>
<?php unset($__attributesOriginal115e82920da0ed7c897ee494af74b9d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal115e82920da0ed7c897ee494af74b9d8)): ?>
<?php $component = $__componentOriginal115e82920da0ed7c897ee494af74b9d8; ?>
<?php unset($__componentOriginal115e82920da0ed7c897ee494af74b9d8); ?>
<?php endif; ?>

    <!-- Flash Messages -->
    <?php if(session('success')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('info')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <?php echo e(session('info')); ?>

        </div>
    <?php endif; ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/layouts/public.blade.php ENDPATH**/ ?>