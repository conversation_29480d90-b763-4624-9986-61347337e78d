<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\GalleryImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class GalleryImageController extends Controller
{
    /**
     * Update gallery image details
     */
    public function update(Request $request, GalleryImage $galleryImage)
    {
        $validated = $request->validate([
            'title' => 'nullable|string|max:255',
            'caption' => 'nullable|string',
            'alt_text' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer',
        ]);

        $validated['is_featured'] = $request->has('is_featured');

        $galleryImage->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Image updated successfully.',
            'image' => $galleryImage
        ]);
    }

    /**
     * Delete a gallery image
     */
    public function destroy(GalleryImage $galleryImage)
    {
        // Delete image files
        if ($galleryImage->image_path) {
            Storage::disk('public')->delete($galleryImage->image_path);
        }
        if ($galleryImage->thumbnail_path) {
            Storage::disk('public')->delete($galleryImage->thumbnail_path);
        }

        $galleryImage->delete();

        return response()->json([
            'success' => true,
            'message' => 'Image deleted successfully.'
        ]);
    }

    /**
     * Update sort order of gallery images
     */
    public function updateOrder(Request $request, Gallery $gallery)
    {
        $request->validate([
            'images' => 'required|array',
            'images.*.id' => 'required|exists:gallery_images,id',
            'images.*.sort_order' => 'required|integer',
        ]);

        foreach ($request->images as $imageData) {
            GalleryImage::where('id', $imageData['id'])
                ->where('gallery_id', $gallery->id)
                ->update(['sort_order' => $imageData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Image order updated successfully.'
        ]);
    }

    /**
     * Set image as gallery cover
     */
    public function setCover(Gallery $gallery, GalleryImage $galleryImage)
    {
        // Verify the image belongs to this gallery
        if ($galleryImage->gallery_id !== $gallery->id) {
            return response()->json([
                'success' => false,
                'message' => 'Image does not belong to this gallery.'
            ], 400);
        }

        // Copy the image to covers directory
        $coverPath = 'galleries/covers/' . basename($galleryImage->image_path);
        Storage::disk('public')->copy($galleryImage->image_path, $coverPath);

        // Delete old cover image if exists
        if ($gallery->cover_image) {
            Storage::disk('public')->delete($gallery->cover_image);
        }

        // Update gallery cover
        $gallery->update(['cover_image' => $coverPath]);

        return response()->json([
            'success' => true,
            'message' => 'Cover image updated successfully.',
            'cover_url' => asset('storage/' . $coverPath)
        ]);
    }

    /**
     * Bulk actions for gallery images
     */
    public function bulkAction(Request $request, Gallery $gallery)
    {
        $request->validate([
            'action' => 'required|in:delete,feature,unfeature',
            'images' => 'required|array',
            'images.*' => 'exists:gallery_images,id'
        ]);

        $images = GalleryImage::whereIn('id', $request->images)
            ->where('gallery_id', $gallery->id);

        switch ($request->action) {
            case 'delete':
                foreach ($images->get() as $image) {
                    if ($image->image_path) {
                        Storage::disk('public')->delete($image->image_path);
                    }
                    if ($image->thumbnail_path) {
                        Storage::disk('public')->delete($image->thumbnail_path);
                    }
                }
                $images->delete();
                $message = 'Selected images deleted successfully.';
                break;
            case 'feature':
                $images->update(['is_featured' => true]);
                $message = 'Selected images featured successfully.';
                break;
            case 'unfeature':
                $images->update(['is_featured' => false]);
                $message = 'Selected images unfeatured successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }
}
