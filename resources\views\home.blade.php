@extends('layouts.public')

@push('styles')
<!-- Modern Fonts -->
<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700,800&display=swap" rel="stylesheet" />

<!-- Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

<style>
    body { font-family: 'Inter', sans-serif; }
    .gradient-bg { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
    .gradient-text { background: linear-gradient(135deg, #10b981 0%, #059669 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    .glass-effect { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.1); }
    .hover-scale { transition: transform 0.3s ease; }
    .hover-scale:hover { transform: scale(1.05); }
    .floating { animation: floating 3s ease-in-out infinite; }
    @keyframes floating { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
    .green-primary { color: #059669; }
    .bg-green-primary { background-color: #059669; }
    .border-green-primary { border-color: #059669; }
    .hover-bg-green-primary:hover { background-color: #047857; }
    .hover-text-green-primary:hover { color: #059669; }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative h-[500px] flex items-center justify-center overflow-hidden">
    <!-- Background Image -->
    @if($globalSettings->banner_image)
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-70"
             style="background-image: url('{{ asset('storage/' . $globalSettings->banner_image) }}');">
        </div>
    @endif

    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-green-900 to-green-800 opacity-75"></div>

    <!-- Beautiful SVG Background Pattern -->
    <x-background-patterns variant="hero" opacity="0.15" />

    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-72 h-72 bg-white mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div class="absolute top-0 right-4 w-72 h-72 bg-green-300 mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
        <div class="absolute -bottom-8 left-20 w-72 h-72 bg-emerald-300 mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="space-y-8" data-aos="fade-up" data-aos-duration="1000">
            <h1 class="text-5xl md:text-5xl font-bold text-white leading-tight">
                {{ $globalSettings->application_name ?? 'Fair Price Ventures' }}
            </h1>
            <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto">
                {{ $globalSettings->tagline ?? 'Your Trusted Business Partner for Innovative Solutions and Exceptional Service' }}
            </p>
            <p class="text-lg text-white/80 max-w-4xl mx-auto">
                {{ $globalSettings->catchphrase ?? 'Delivering exceptional value through innovative solutions and unmatched service excellence.' }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
                <a href="{{ route('services.index') }}" class="bg-white text-green-600 px-8 py-4 rounded-md font-semibold hover:bg-gray-100 transition-all duration-300 hover-scale shadow-lg">
                    Explore Our Services
                </a>
                <a href="{{ route('contact') }}" class="border-2 border-white text-white px-8 py-4 rounded-md font-semibold hover:bg-white hover:text-green-600 transition-all duration-300 hover-scale">
                    Get In Touch
                </a>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 floating">
            <div class="w-4 h-4 bg-white  opacity-60"></div>
        </div>
        <div class="absolute top-40 right-20 floating" style="animation-delay: 1s;">
            <div class="w-6 h-6 bg-white  opacity-40"></div>
        </div>
        <div class="absolute bottom-20 left-1/4 floating" style="animation-delay: 2s;">
            <div class="w-3 h-3 bg-white  opacity-50"></div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <a href="#about" class="text-white hover:text-gray-200 transition-colors duration-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </a>
    </div>
</section>

<!-- About Section -->
<section id="about" class="relative py-20 bg-white overflow-hidden">
    <!-- Subtle Background Pattern -->
    <x-background-patterns variant="subtle" opacity="0.08" />

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">About Us</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    {{ Str::limit($globalSettings->about_us, 200) }}... <a href="{{ route('pages.show', 'about') }}" class="text-green-600 hover:text-green-700 transition-colors duration-300">Read More</a>
            </p>

        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div data-aos="fade-right">
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Our Mission</h3>
                <p class="text-lg text-gray-600 mb-6">
                    {{ $globalSettings->mission ?? 'We are committed to delivering innovative, cost-effective solutions that exceed our clients\' expectations while fostering long-term partnerships built on trust, integrity, and mutual success.' }}
                </p>

                <h3 class="text-3xl font-bold text-gray-900 mb-6">Our Vision</h3>
                <p class="text-lg text-gray-600 mb-6">
                    {{ $globalSettings->vision ?? 'We are committed to delivering innovative, cost-effective solutions that exceed our clients\' expectations while fostering long-term partnerships built on trust, integrity, and mutual success.' }}
                </p>
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">500+</div>
                        <div class="text-gray-600">Happy Clients</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">10+</div>
                        <div class="text-gray-600">Years Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">1000+</div>
                        <div class="text-gray-600">Projects Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">24/7</div>
                        <div class="text-gray-600">Support</div>
                    </div>
                </div>
            </div>
            <div data-aos="fade-left">
                @if($globalSettings->header_image ?? null)
                    <img src="{{ asset('storage/' . $globalSettings->header_image) }}" alt="About Us" class="rounded-lg shadow-2xl hover-scale">
                @else
                    <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-lg shadow-2xl h-96 flex items-center justify-center">
                        <div class="text-white text-center">
                            <i class="fas fa-building text-6xl mb-4"></i>
                            <h4 class="text-2xl font-bold">{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}</h4>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
@if($featuredServices->count() > 0)
<section class="relative py-20 bg-gray-50 overflow-hidden">
    <!-- Default Background Pattern -->
    <x-background-patterns variant="default" opacity="0.06" />

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Our Featured Services</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover comprehensive business solutions tailored to meet your unique needs and drive your success.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredServices as $service)
            <div class="bg-white rounded-xl shadow-lg p-8 hover-scale transition-all duration-300" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                @if($service->featured_image)
                    <img src="{{ asset('storage/' . $service->featured_image) }}" alt="{{ $service->title }}" class="w-full h-48 object-cover rounded-lg mb-6">
                @endif

                @if($service->icon)
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl text-green-600">{{ $service->icon }}</span>
                    </div>
                @endif

                <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $service->title }}</h3>
                <p class="text-gray-600 mb-6">{{ $service->description }}</p>

                @if($service->price)
                    <p class="text-green-600 font-semibold mb-6">{{ $service->formatted_price }}</p>
                @endif

                <a href="{{ route('services.show', $service) }}" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                    Learn More →
                </a>
            </div>
            @endforeach
        </div>

        <div class="text-center mt-12">
            <a href="{{ route('services.index') }}" class="bg-green-600 text-white px-8 py-4 rounded-md font-semibold hover:bg-green-700 transition-all duration-300 hover-scale shadow-lg">
                View All Services
            </a>
        </div>
    </div>
</section>
@endif

<!-- Latest Blog Posts Section -->
{{-- @if($latestBlogPosts->count() > 0)
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Latest from Our Blog</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Stay updated with industry insights, company news, and expert perspectives on business growth.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($latestBlogPosts as $post)
            <article class="bg-white rounded-xl shadow-lg overflow-hidden hover-scale transition-all duration-300" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                @if($post->featured_image)
                    <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}" class="w-full h-48 object-cover">
                @endif

                <div class="p-6">
                    <div class="flex items-center text-sm text-gray-500 mb-3">
                        <span class="bg-green-600 text-white px-3 py-1  text-xs mr-3">
                            {{ $post->category->name }}
                        </span>
                        <span>{{ $post->formatted_published_date }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ $post->reading_time_text }}</span>
                    </div>

                    <h3 class="text-xl font-bold text-gray-900 mb-3">
                        <a href="{{ route('blog.show', $post) }}" class="hover:text-green-600 transition-colors duration-300">
                            {{ $post->title }}
                        </a>
                    </h3>

                    <p class="text-gray-600 mb-4">{{ $post->excerpt }}</p>

                    <a href="{{ route('blog.show', $post) }}" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                        Read More →
                    </a>
                </div>
            </article>
            @endforeach
        </div>

        <div class="text-center mt-12">
            <a href="{{ route('blog.index') }}" class="bg-green-600 text-white px-8 py-4  font-semibold hover:bg-green-700 transition-all duration-300 hover-scale shadow-lg">
                View All Posts
            </a>
        </div>
    </div>
</section>
@endif --}}

<!-- Featured Projects Section -->
@if($featuredProjects->count() > 0)
<section id="projects" class="relative py-20 bg-gray-50 overflow-hidden">
    <!-- Default Background Pattern -->
    <x-background-patterns variant="default" opacity="0.05" />

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Featured Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover some of our most successful projects and see how we've helped businesses achieve their goals.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredProjects as $index => $project)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover-scale" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($project->featured_image_url)
                        <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" class="w-full h-48 object-cover">
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                            <div class="text-white text-center">
                                <i class="fas fa-project-diagram text-4xl mb-2"></i>
                                <h4 class="text-lg font-bold">{{ $project->title }}</h4>
                            </div>
                        </div>
                    @endif

                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">{{ $project->title }}</h3>
                            {!! $project->status_badge !!}
                        </div>

                        @if($project->client)
                            <p class="text-green-600 font-semibold mb-2">{{ $project->client }}</p>
                        @endif

                        <p class="text-gray-600 mb-4">{{ $project->description }}</p>

                        @if($project->technologies && count($project->technologies) > 0)
                            <div class="flex flex-wrap gap-2 mb-4">
                                @foreach(array_slice($project->technologies, 0, 3) as $tech)
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium ">
                                        {{ $tech }}
                                    </span>
                                @endforeach
                                @if(count($project->technologies) > 3)
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium ">
                                        +{{ count($project->technologies) - 3 }} more
                                    </span>
                                @endif
                            </div>
                        @endif

                        <div class="flex justify-between items-center">
                            @if($project->project_url)
                                <a href="{{ $project->project_url }}" target="_blank" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                                    View Project →
                                </a>
                            @else
                                <span class="text-gray-400">Project Details</span>
                            @endif

                            @if($project->start_date)
                                <span class="text-sm text-gray-500">{{ $project->start_date->format('M Y') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="text-center mt-12">
            <a href="#" class="bg-green-600 text-white px-8 py-4  font-semibold hover:bg-green-700 transition-all duration-300 hover-scale shadow-lg">
                View All Projects
            </a>
        </div>
    </div>
</section>
@endif

<!-- Team Section -->
{{-- @if($executiveTeam->count() > 0 || $projectTeam->count() > 0)
<section id="team" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our experienced professionals are dedicated to delivering exceptional results for your business.
            </p>
        </div>

        <!-- Executive Team -->
        @if($executiveTeam->count() > 0)
        <div class="mb-16">
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Executive Management</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($executiveTeam as $index => $member)
                    <div class="text-center" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        @if($member->image_url)
                            <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-32 h-32  mx-auto mb-4 object-cover shadow-lg">
                        @else
                            <div class="w-32 h-32  mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                <span class="text-3xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                            </div>
                        @endif

                        <h4 class="text-xl font-bold text-gray-900 mb-2">{{ $member->name }}</h4>
                        <p class="text-green-600 font-semibold mb-3">{{ $member->position }}</p>

                        @if($member->bio)
                            <p class="text-gray-600 text-sm mb-4">{{ Str::limit($member->bio, 120) }}</p>
                        @endif

                        @if($member->linkedin || $member->twitter || $member->email)
                            <div class="flex justify-center space-x-3">
                                @if($member->email)
                                    <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                @endif
                                @if($member->linkedin)
                                    <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                        <i class="fab fa-linkedin"></i>
                                    </a>
                                @endif
                                @if($member->twitter)
                                    <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Project Team -->
        @if($projectTeam->count() > 0)
        <div>
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Project Management</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($projectTeam as $index => $member)
                    <div class="text-center" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        @if($member->image_url)
                            <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-24 h-24  mx-auto mb-4 object-cover shadow-lg">
                        @else
                            <div class="w-24 h-24  mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                <span class="text-xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                            </div>
                        @endif

                        <h4 class="text-lg font-bold text-gray-900 mb-1">{{ $member->name }}</h4>
                        <p class="text-green-600 font-semibold text-sm mb-2">{{ $member->position }}</p>

                        @if($member->linkedin || $member->twitter || $member->email)
                            <div class="flex justify-center space-x-2">
                                @if($member->email)
                                    <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300 text-sm">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                @endif
                                @if($member->linkedin)
                                    <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300 text-sm">
                                        <i class="fab fa-linkedin"></i>
                                    </a>
                                @endif
                                @if($member->twitter)
                                    <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300 text-sm">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</section>
@endif --}}

<!-- Partners & Clients Section -->
@if($featuredPartners->count() > 0 || $featuredClients->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Partners & Clients</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We're proud to work with amazing organizations and deliver exceptional results for our valued clients.
            </p>
        </div>

        <!-- Partners -->
        @if($featuredPartners->count() > 0)
        <div class="mb-16">
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Our Partners</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                @foreach($featuredPartners as $index => $partner)
                    <div class="flex items-center justify-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        @if($partner->logo_url)
                            <img src="{{ $partner->logo_url }}" alt="{{ $partner->name }}" class="max-w-full max-h-16 object-contain filter grayscale hover:grayscale-0 transition-all duration-300">
                        @else
                            <div class="text-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <span class="text-lg font-bold text-white">{{ substr($partner->name, 0, 1) }}</span>
                                </div>
                                <span class="text-xs font-medium text-gray-600">{{ $partner->name }}</span>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Clients -->
        @if($featuredClients->count() > 0)
        <div>
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Our Clients</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                @foreach($featuredClients as $index => $client)
                    <div class="flex items-center justify-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        @if($client->logo_url)
                            <img src="{{ $client->logo_url }}" alt="{{ $client->name }}" class="max-w-full max-h-16 object-contain filter grayscale hover:grayscale-0 transition-all duration-300">
                        @else
                            <div class="text-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <span class="text-lg font-bold text-white">{{ substr($client->name, 0, 1) }}</span>
                                </div>
                                <span class="text-xs font-medium text-gray-600">{{ $client->name }}</span>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</section>
@endif

<!-- Testimonials Section -->
@if($featuredTestimonials->count() > 0)
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Don't just take our word for it. Here's what our satisfied clients have to say about working with us.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredTestimonials as $index => $testimonial)
                <div class="bg-gray-50 rounded-lg p-8 shadow-sm hover:shadow-md transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    <div class="flex items-center mb-4">
                        {!! $testimonial->stars_html !!}
                    </div>

                    <blockquote class="text-gray-700 mb-6 italic">
                        "{{ $testimonial->testimonial }}"
                    </blockquote>

                    <div class="flex items-center">
                        @if($testimonial->client_image_url)
                            <img src="{{ $testimonial->client_image_url }}" alt="{{ $testimonial->client_name }}" class="w-12 h-12  object-cover mr-4">
                        @else
                            <div class="w-12 h-12  bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center mr-4">
                                <span class="text-lg font-bold text-white">{{ substr($testimonial->client_name, 0, 1) }}</span>
                            </div>
                        @endif

                        <div>
                            <div class="font-semibold text-gray-900">{{ $testimonial->client_name }}</div>
                            @if($testimonial->client_position || $testimonial->client_company)
                                <div class="text-sm text-gray-600">
                                    {{ $testimonial->client_position }}
                                    @if($testimonial->client_position && $testimonial->client_company) at @endif
                                    {{ $testimonial->client_company }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- View All Testimonials Button -->
        <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="300">
            <a href="{{ route('testimonials.index') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                View All Testimonials
            </a>
            <div class="mt-4">
                <a href="{{ route('testimonials.create') }}" class="text-green-600 hover:text-green-800 font-medium">
                    Share Your Experience →
                </a>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Call to Action Section -->
<section class="py-20 gradient-bg text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center" data-aos="fade-up">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Transform Your Business?</h2>
        <p class="text-xl mb-8 text-white/90 max-w-3xl mx-auto">
           We are here to serve you. Reach out to us for inquiries, partnerships, or to learn more about our services.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a href="{{ route('contact') }}" class="bg-white text-green-600 px-8 py-4 rounded-md font-semibold hover:bg-gray-100 transition-all duration-300 hover-scale shadow-lg">
                Contact Us Today
            </a>
            <a href="{{ route('services.index') }}" class="border-2 border-white text-white px-8 py-4 rounded-md font-semibold hover:bg-white hover:text-green-600 transition-all duration-300 hover-scale">
                Explore Services
            </a>
        </div>
    </div>
</section>

<!-- Newsletter Popup -->
@if($globalSettings->newsletter_popup_enabled)
    <div x-data="{
        show: false,
        init() {
            setTimeout(() => {
                this.show = true;
            }, {{ ($globalSettings->newsletter_popup_delay ?? 5) * 1000 }});
        }
    }"
    x-show="show"
    x-transition
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    style="display: none;">
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 relative">
            <button @click="show = false" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <div class="text-center">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">
                    {{ $globalSettings->newsletter_popup_title ?? 'Stay Updated!' }}
                </h3>
                <p class="text-gray-600 mb-6">
                    {{ $globalSettings->newsletter_popup_message ?? 'Subscribe to our newsletter to get the latest updates and exclusive offers!' }}
                </p>

                @livewire('newsletter-signup', ['source' => 'popup'])
            </div>
        </div>
    </div>
@endif
@endsection

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
@endpush
