@extends('installer.layout')

@section('title', 'Database Installation')
@section('step', '5')

@section('content')
<div class="bg-white rounded-lg shadow-lg p-8">
    <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <i class="fas fa-database text-blue-600 text-2xl"></i>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Database Installation</h1>
        <p class="text-gray-600">
            We'll now set up your database by running migrations and installing sample data.
        </p>
    </div>

    <div id="installation-progress" class="space-y-6">
        <!-- Connection Test -->
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div id="connection-icon" class="flex-shrink-0 mr-4">
                        <i class="fas fa-circle-notch fa-spin text-blue-500 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Testing Database Connection</h3>
                        <p class="text-sm text-gray-600">Verifying connection to your database...</p>
                    </div>
                </div>
                <div id="connection-status" class="hidden">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Testing...
                    </span>
                </div>
            </div>
        </div>

        <!-- Migrations -->
        <div class="bg-gray-50 rounded-lg p-6 opacity-50" id="migrations-step">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div id="migrations-icon" class="flex-shrink-0 mr-4">
                        <i class="fas fa-circle text-gray-400 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Running Database Migrations</h3>
                        <p class="text-sm text-gray-600">Creating database tables and structure...</p>
                    </div>
                </div>
                <div id="migrations-status" class="hidden">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Waiting...
                    </span>
                </div>
            </div>
        </div>

        <!-- Seeders -->
        <div class="bg-gray-50 rounded-lg p-6 opacity-50" id="seeders-step">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div id="seeders-icon" class="flex-shrink-0 mr-4">
                        <i class="fas fa-circle text-gray-400 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Installing Sample Data</h3>
                        <p class="text-sm text-gray-600">Adding default settings and sample content...</p>
                    </div>
                </div>
                <div id="seeders-status" class="hidden">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Waiting...
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Messages -->
    <div id="progress-messages" class="mt-8 space-y-2 hidden">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400 text-lg"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Installation Progress</h3>
                    <div id="progress-log" class="mt-2 text-sm text-blue-700 space-y-1">
                        <!-- Progress messages will be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Messages -->
    <div id="error-messages" class="mt-8 hidden">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400 text-lg"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Installation Error</h3>
                    <div id="error-log" class="mt-2 text-sm text-red-700">
                        <!-- Error messages will be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div id="success-message" class="mt-8 hidden">
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-lg"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">Database Installation Complete!</h3>
                    <p class="mt-2 text-sm text-green-700">
                        Your database has been successfully set up with all required tables and sample data.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="flex items-center justify-between mt-8">
        <a href="{{ route('installer.environment') }}" 
           class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors duration-300">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>

        <button id="start-installation" 
                type="button" 
                class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-300">
            <i class="fas fa-play mr-2"></i>
            Start Database Installation
        </button>

        <a id="continue-button" 
           href="{{ route('installer.admin') }}" 
           class="hidden inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-300">
            Continue
            <i class="fas fa-arrow-right ml-2"></i>
        </a>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startButton = document.getElementById('start-installation');
    const continueButton = document.getElementById('continue-button');
    const progressMessages = document.getElementById('progress-messages');
    const progressLog = document.getElementById('progress-log');
    const errorMessages = document.getElementById('error-messages');
    const errorLog = document.getElementById('error-log');
    const successMessage = document.getElementById('success-message');

    startButton.addEventListener('click', function() {
        startButton.disabled = true;
        startButton.innerHTML = '<i class="fas fa-circle-notch fa-spin mr-2"></i>Installing...';
        
        progressMessages.classList.remove('hidden');
        errorMessages.classList.add('hidden');
        successMessage.classList.add('hidden');
        
        addProgressMessage('Starting database installation...');
        
        // Start the installation process
        runDatabaseInstallation();
    });

    function addProgressMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.textContent = `• ${message}`;
        progressLog.appendChild(messageElement);
    }

    function showError(message) {
        errorLog.textContent = message;
        errorMessages.classList.remove('hidden');
        
        startButton.disabled = false;
        startButton.innerHTML = '<i class="fas fa-redo mr-2"></i>Retry Installation';
    }

    function updateStepStatus(stepId, iconId, statusId, status, icon, statusText, statusClass) {
        const step = document.getElementById(stepId);
        const iconElement = document.getElementById(iconId);
        const statusElement = document.getElementById(statusId);
        
        step.classList.remove('opacity-50');
        iconElement.className = `fas ${icon} text-xl`;
        statusElement.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}`;
        statusElement.textContent = statusText;
        statusElement.classList.remove('hidden');
        
        if (status === 'success') {
            iconElement.classList.add('text-green-500');
        } else if (status === 'error') {
            iconElement.classList.add('text-red-500');
        } else if (status === 'loading') {
            iconElement.classList.add('text-blue-500');
        }
    }

    async function runDatabaseInstallation() {
        try {
            // Test database connection first
            updateStepStatus('connection-step', 'connection-icon', 'connection-status', 'loading', 'fa-circle-notch fa-spin', 'Testing...', 'bg-blue-100 text-blue-800');
            addProgressMessage('Testing database connection...');
            
            // Simulate connection test (in real implementation, this would be an AJAX call)
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            updateStepStatus('connection-step', 'connection-icon', 'connection-status', 'success', 'fa-check-circle', 'Connected', 'bg-green-100 text-green-800');
            addProgressMessage('Database connection successful!');
            
            // Run migrations
            updateStepStatus('migrations-step', 'migrations-icon', 'migrations-status', 'loading', 'fa-circle-notch fa-spin', 'Running...', 'bg-blue-100 text-blue-800');
            addProgressMessage('Running database migrations...');
            
            const migrationResponse = await fetch('{{ route("installer.database.install") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });
            
            const migrationResult = await migrationResponse.json();
            
            if (migrationResult.success) {
                updateStepStatus('migrations-step', 'migrations-icon', 'migrations-status', 'success', 'fa-check-circle', 'Complete', 'bg-green-100 text-green-800');
                addProgressMessage('Database migrations completed successfully!');
                
                // Show success and continue button
                successMessage.classList.remove('hidden');
                continueButton.classList.remove('hidden');
                startButton.classList.add('hidden');
                
            } else {
                throw new Error(migrationResult.message || 'Database installation failed');
            }
            
        } catch (error) {
            console.error('Installation error:', error);
            showError(error.message || 'An unexpected error occurred during installation');
            
            // Update failed step
            updateStepStatus('migrations-step', 'migrations-icon', 'migrations-status', 'error', 'fa-times-circle', 'Failed', 'bg-red-100 text-red-800');
        }
    }
});
</script>
@endpush
@endsection
