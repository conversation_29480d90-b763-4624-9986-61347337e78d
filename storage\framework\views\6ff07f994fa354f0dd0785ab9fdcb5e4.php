<?php $__env->startSection('content'); ?>
<div class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Article Header -->
        <article class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <?php if($post->featured_image): ?>
                <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>" class="w-full h-64 md:h-96 object-cover">
            <?php endif; ?>
            
            <div class="p-8">
                <!-- Article Meta -->
                <div class="flex flex-wrap items-center text-sm text-gray-500 mb-6">
                    <a href="<?php echo e(route('blog.category', $post->category)); ?>" class="bg-<?php echo e($post->category->color); ?> text-white px-3 py-1 rounded text-xs mr-4 hover:opacity-80">
                        <?php echo e($post->category->name); ?>

                    </a>
                    <span class="mr-4"><?php echo e($post->formatted_published_date); ?></span>
                    <span class="mr-4"><?php echo e($post->reading_time_text); ?></span>
                    <span class="mr-4">By <?php echo e($post->user->name); ?></span>
                    <?php if($post->is_featured): ?>
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded">Featured</span>
                    <?php endif; ?>
                </div>
                
                <!-- Article Title -->
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6"><?php echo e($post->title); ?></h1>
                
                <!-- Article Excerpt -->
                <p class="text-xl text-gray-600 mb-8 leading-relaxed"><?php echo e($post->excerpt); ?></p>
                
                <!-- Tags -->
                <?php if($post->tags->count() > 0): ?>
                    <div class="flex flex-wrap gap-2 mb-8">
                        <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('blog.tag', $tag)); ?>" class="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded hover:bg-gray-200 transition duration-300">
                                #<?php echo e($tag->name); ?>

                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Article Content -->
                <div class="prose prose-lg max-w-none">
                    <?php echo $post->content; ?>

                </div>
                
                <!-- Article Footer -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <?php echo e($post->views_count); ?> views
                        </div>
                        
                        <!-- Social Share Buttons -->
                        <div class="flex space-x-4">
                            <a href="https://twitter.com/intent/tweet?text=<?php echo e(urlencode($post->title)); ?>&url=<?php echo e(urlencode(request()->url())); ?>" target="_blank" class="text-blue-500 hover:text-blue-700">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode(request()->url())); ?>" target="_blank" class="text-blue-600 hover:text-blue-800">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </article>

        <!-- Author Bio -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <div class="flex items-center">
                <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4">
                    <?php echo e(substr($post->user->name, 0, 1)); ?>

                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($post->user->name); ?></h3>
                    <p class="text-gray-600">Business Consultant & Author</p>
                </div>
            </div>
        </div>

        <!-- Related Posts -->
        <?php if($relatedPosts->count() > 0): ?>
        <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Articles</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <?php $__currentLoopData = $relatedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedPost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <article class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition duration-300">
                    <?php if($relatedPost->featured_image): ?>
                        <img src="<?php echo e(asset('storage/' . $relatedPost->featured_image)); ?>" alt="<?php echo e($relatedPost->title); ?>" class="w-full h-32 object-cover">
                    <?php endif; ?>
                    
                    <div class="p-4">
                        <div class="text-xs text-gray-500 mb-2"><?php echo e($relatedPost->formatted_published_date); ?></div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-2">
                            <a href="<?php echo e(route('blog.show', $relatedPost)); ?>" class="hover:text-green-600">
                                <?php echo e(Str::limit($relatedPost->title, 60)); ?>

                            </a>
                        </h3>
                        <p class="text-xs text-gray-600 mb-3"><?php echo e(Str::limit($relatedPost->excerpt, 80)); ?></p>
                        <a href="<?php echo e(route('blog.show', $relatedPost)); ?>" class="text-green-600 text-xs font-semibold hover:text-green-800">
                            Read More →
                        </a>
                    </div>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Back to Blog -->
        <div class="mt-8 text-center">
            <a href="<?php echo e(route('blog.index')); ?>" class="inline-flex items-center text-green-600 font-semibold hover:text-green-800">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Blog
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/blog/show.blade.php ENDPATH**/ ?>