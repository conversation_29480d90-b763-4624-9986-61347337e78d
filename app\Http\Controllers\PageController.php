<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\Team;
use Illuminate\Http\Request;

class PageController extends Controller
{
    public function show($slug)
    {
        $page = Page::where('slug', $slug)->published()->firstOrFail();

        // For About page, include team data
        $data = compact('page');
        if ($slug === 'about') {
            $data['executiveTeam'] = Team::getExecutiveTeam();
            $data['projectTeam'] = Team::getProjectTeam();
        }

        return view('pages.show', $data);
    }
}
