<?php

namespace App\Http\Controllers;

use App\Models\Partner;
use Illuminate\Http\Request;

class PartnerController extends Controller
{
    /**
     * Display partners page
     */
    public function partners()
    {
        $partners = Partner::active()
            ->partners()
            ->ordered()
            ->get();

        $featuredPartners = Partner::active()
            ->partners()
            ->featured()
            ->ordered()
            ->get();

        return view('partners.index', compact('partners', 'featuredPartners'));
    }

    /**
     * Display clients page
     */
    public function clients()
    {
        $clients = Partner::active()
            ->clients()
            ->ordered()
            ->get();

        $featuredClients = Partner::active()
            ->clients()
            ->featured()
            ->ordered()
            ->get();

        return view('clients.index', compact('clients', 'featuredClients'));
    }

    /**
     * Display a specific partner
     */
    public function showPartner(Partner $partner)
    {
        if (!$partner->is_active || $partner->type !== 'partner') {
            abort(404);
        }

        $relatedPartners = Partner::active()
            ->partners()
            ->where('id', '!=', $partner->id)
            ->ordered()
            ->limit(6)
            ->get();

        return view('partners.show', compact('partner', 'relatedPartners'));
    }

    /**
     * Display a specific client
     */
    public function showClient(Partner $client)
    {
        if (!$client->is_active || $client->type !== 'client') {
            abort(404);
        }

        $relatedClients = Partner::active()
            ->clients()
            ->where('id', '!=', $client->id)
            ->ordered()
            ->limit(6)
            ->get();

        return view('clients.show', compact('client', 'relatedClients'));
    }
}
