@props(['name', 'value' => '', 'required' => false, 'height' => '400px'])

@once
@push('styles')
<!-- Bootstrap CSS (required for Summernote) -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Summernote CSS -->
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.css" rel="stylesheet">
<style>
    .summernote-container {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    .summernote-container:focus-within {
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    .note-editor {
        border: none !important;
    }
    .note-toolbar {
        background: #f9fafb !important;
        border-bottom: 1px solid #e5e7eb !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
    }
    .note-editable {
        font-family: 'Poppins', Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        min-height: calc({{ $height }} - 100px) !important;
        padding: 12px 15px !important;
    }
    .note-btn {
        background: transparent !important;
        border: none !important;
        color: #374151 !important;
    }
    .note-btn:hover {
        background: #e5e7eb !important;
    }
    .note-btn.active {
        background: #10b981 !important;
        color: white !important;
    }
</style>
@endpush
@endonce

<div>
    <!-- Summernote textarea -->
    <textarea
        name="{{ $name }}"
        id="{{ $name }}"
        {{ $required ? 'required' : '' }}
        class="summernote"
    >{{ $value }}</textarea>
</div>

@once
@push('scripts')
<!-- jQuery (required for Summernote) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap JS (required for Summernote) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Summernote JS -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.js"></script>
@endpush
@endonce

@push('scripts')
<script>
$(document).ready(function() {
    $('#{{ $name }}').summernote({
        height: {{ str_replace('px', '', $height) }},
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['fontname', ['fontname']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'picture']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        placeholder: 'Start writing your content...',
        tabsize: 2,
        focus: false,
        callbacks: {
            onImageUpload: function(files) {
                uploadImage(files[0], this);
            }
        }
    });

    function uploadImage(file, editor) {
        // Check file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        $.ajax({
            url: '/admin/upload-image',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.location) {
                    $(editor).summernote('insertImage', data.location);
                } else {
                    alert('Failed to upload image: ' + (data.error || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error uploading image:', error);
                alert('Failed to upload image. Please try again.');
            }
        });
    }

    console.log('Summernote editor initialized successfully for: {{ $name }}');
});
</script>
@endpush
