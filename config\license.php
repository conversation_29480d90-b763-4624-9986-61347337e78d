<?php

return [
    /*
    |--------------------------------------------------------------------------
    | License Verification Settings
    |--------------------------------------------------------------------------
    |
    | These settings control how the application verifies licenses with
    | the remote license server.
    |
    */

    'verification_url' => env('LICENSE_VERIFICATION_URL', 'https://license.yourcompany.com/api/validate'),

    'verification_enabled' => env('LICENSE_VERIFICATION_ENABLED', true),

    'verification_interval_hours' => env('LICENSE_VERIFICATION_INTERVAL', 24),

    /*
    |--------------------------------------------------------------------------
    | Grace Period Settings
    |--------------------------------------------------------------------------
    |
    | When license verification fails due to network issues, the application
    | can continue running for a limited grace period.
    |
    */

    'grace_period_days' => env('LICENSE_GRACE_PERIOD_DAYS', 7),

    'grace_period_enabled' => env('LICENSE_GRACE_PERIOD_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | License Types
    |--------------------------------------------------------------------------
    |
    | Different license types and their capabilities.
    |
    */

    'types' => [
        'regular' => [
            'name' => 'Regular License',
            'description' => 'Standard license for single end product',
            'features' => [
                'single_domain',
                'basic_support',
                'updates_1_year',
            ],
        ],
        'extended' => [
            'name' => 'Extended License',
            'description' => 'Extended license for commercial use',
            'features' => [
                'unlimited_domains',
                'priority_support',
                'lifetime_updates',
                'white_label',
                'source_code',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | License Restrictions
    |--------------------------------------------------------------------------
    |
    | Settings that control license restrictions and validations.
    |
    */

    'domain_binding_enabled' => env('LICENSE_DOMAIN_BINDING', true),

    'ip_validation_enabled' => env('LICENSE_IP_VALIDATION', false),

    'max_activations_per_license' => env('LICENSE_MAX_ACTIVATIONS', 1),

    /*
    |--------------------------------------------------------------------------
    | Offline Mode
    |--------------------------------------------------------------------------
    |
    | Settings for when the application cannot connect to the license server.
    |
    */

    'offline_mode_enabled' => env('LICENSE_OFFLINE_MODE', true),

    'offline_grace_period_days' => env('LICENSE_OFFLINE_GRACE_DAYS', 30),

    /*
    |--------------------------------------------------------------------------
    | License Server Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for communicating with the license server.
    |
    */

    'server' => [
        'timeout' => env('LICENSE_SERVER_TIMEOUT', 30),
        'retry_attempts' => env('LICENSE_SERVER_RETRIES', 3),
        'retry_delay' => env('LICENSE_SERVER_RETRY_DELAY', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security-related license settings.
    |
    */

    'encryption_enabled' => env('LICENSE_ENCRYPTION', true),

    'signature_verification' => env('LICENSE_SIGNATURE_VERIFICATION', true),

    'anti_tampering' => env('LICENSE_ANTI_TAMPERING', true),

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for license-related notifications.
    |
    */

    'notifications' => [
        'expiry_warning_days' => [30, 14, 7, 3, 1],
        'grace_period_warnings' => true,
        'verification_failures' => true,
        'admin_email' => env('LICENSE_ADMIN_EMAIL'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Settings
    |--------------------------------------------------------------------------
    |
    | Settings for development and testing environments.
    |
    */

    'development_mode' => env('LICENSE_DEVELOPMENT_MODE', false),

    'bypass_verification_in_local' => env('LICENSE_BYPASS_LOCAL', true),

    'test_license_key' => env('LICENSE_TEST_KEY'),

    /*
    |--------------------------------------------------------------------------
    | CodeCanyon Integration
    |--------------------------------------------------------------------------
    |
    | Settings for CodeCanyon marketplace integration.
    |
    */

    'codecanyon' => [
        'item_id' => env('CODECANYON_ITEM_ID'),
        'author_username' => env('CODECANYON_AUTHOR'),
        'api_key' => env('CODECANYON_API_KEY'),
        'verification_endpoint' => 'https://api.envato.com/v3/market/author/sale',
    ],
];
