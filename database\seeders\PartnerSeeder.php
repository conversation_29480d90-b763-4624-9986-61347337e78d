<?php

namespace Database\Seeders;

use App\Models\Partner;
use Illuminate\Database\Seeder;

class PartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $partners = [
            [
                'name' => 'Jaiz Bank Plc',
                'type' => 'partner',
                'website' => 'https://jaizbankplc.com',
                'industry' => 'Banking',
                'sort_order' => 1,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'name' => 'Fidelity Bank Plc',
                'type' => 'partner',
                'website' => 'https://www.fidelitybank.ng/',
                'industry' => 'Banking',
                'sort_order' => 2,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'name' => 'Carinthia Gold Profile JVC Limited',
                'type' => 'partner',
                'website' => 'https://www.carinthiagoldprofile.com/',
                'industry' => 'Construction',
                'sort_order' => 3,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'name' => 'NNPC, Zonal office, Kaduna',
                'type' => 'client',
                'website' => 'https://nnpc.com',
                'industry' => 'Oil & Gas',
                'sort_order' => 1,
                'is_featured' => false,
                'is_active' => true,
            ],
        ];

        foreach ($partners as $partner) {
            Partner::firstOrCreate(
                ['name' => $partner['name']],
                $partner
            );
        }
    }
}
