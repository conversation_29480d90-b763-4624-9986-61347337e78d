<?php

namespace App\Http\Controllers;

use App\Models\Team;
use Illuminate\Http\Request;

class TeamController extends Controller
{
    public function index()
    {
        $executiveTeam = Team::getExecutiveTeam();
        $projectTeam = Team::getProjectTeam();

        return view('team.index', compact('executiveTeam', 'projectTeam'));
    }

    public function executive()
    {
        $executiveTeam = Team::getExecutiveTeam();

        return view('team.executive', compact('executiveTeam'));
    }

    public function project()
    {
        $projectTeam = Team::getProjectTeam();

        return view('team.project', compact('projectTeam'));
    }

    public function show(Team $team)
    {
        if (!$team->is_active) {
            abort(404);
        }

        return view('team.show', compact('team'));
    }
}
