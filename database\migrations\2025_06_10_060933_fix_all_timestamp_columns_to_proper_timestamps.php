<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $tables = ['partners', 'teams', 'projects', 'services'];

        foreach ($tables as $tableName) {
            echo "Fixing timestamps for table: $tableName\n";

            // Check if table exists
            if (!Schema::hasTable($tableName)) {
                echo "Table $tableName does not exist, skipping...\n";
                continue;
            }

            // Backup existing data
            $records = DB::table($tableName)->get();

            // Drop existing VARCHAR timestamp columns
            Schema::table($tableName, function (Blueprint $table) {
                $table->dropColumn(['created_at', 'updated_at']);
            });

            // Add proper timestamp columns
            Schema::table($tableName, function (Blueprint $table) {
                $table->timestamps();
            });

            // Restore data with proper timestamps
            foreach ($records as $record) {
                DB::table($tableName)
                    ->where('id', $record->id)
                    ->update([
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
            }

            echo "Fixed timestamps for table: $tableName\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
