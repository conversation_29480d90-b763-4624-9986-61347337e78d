@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Newsletter Subscriber</h1>
            <div class="flex space-x-2">
                <a href="{{ route('admin.newsletter-subscribers.edit', $newsletterSubscriber) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Subscriber
                </a>
                <a href="{{ route('admin.newsletter-subscribers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Subscribers
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Subscriber Card -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center text-white text-xl font-medium">
                            {{ $newsletterSubscriber->name ? substr($newsletterSubscriber->name, 0, 1) : substr($newsletterSubscriber->email, 0, 1) }}
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">
                                {{ $newsletterSubscriber->name ?: 'Anonymous Subscriber' }}
                            </h2>
                            <p class="text-gray-600">{{ $newsletterSubscriber->email }}</p>
                            <div class="flex items-center space-x-2 mt-2">
                                @if($newsletterSubscriber->is_active)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                @endif
                                @if($newsletterSubscriber->subscription_source)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        {{ ucfirst($newsletterSubscriber->subscription_source) }}
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription History -->
                <div class="mt-6 bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Timeline</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">Subscribed</div>
                                <div class="text-sm text-gray-500">{{ $newsletterSubscriber->subscribed_at->format('M d, Y g:i A') }}</div>
                                @if($newsletterSubscriber->subscription_source)
                                    <div class="text-xs text-gray-400">via {{ ucfirst($newsletterSubscriber->subscription_source) }}</div>
                                @endif
                            </div>
                        </div>
                        
                        @if($newsletterSubscriber->updated_at != $newsletterSubscriber->created_at)
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">Last Updated</div>
                                <div class="text-sm text-gray-500">{{ $newsletterSubscriber->updated_at->format('M d, Y g:i A') }}</div>
                                <div class="text-xs text-gray-400">{{ $newsletterSubscriber->updated_at->diffForHumans() }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Email Actions -->
                <div class="mt-6 bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Email Actions</h3>
                    <div class="space-y-3">
                        <a href="mailto:{{ $newsletterSubscriber->email }}?subject=Newsletter from {{ config('app.name') }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Send Email
                        </a>
                        
                        <button onclick="copyToClipboard('{{ $newsletterSubscriber->email }}')" 
                                class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-bold rounded ml-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            Copy Email
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Subscriber Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subscriber Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Email:</span>
                            <div class="text-gray-900 font-medium break-all">{{ $newsletterSubscriber->email }}</div>
                        </div>
                        @if($newsletterSubscriber->name)
                        <div>
                            <span class="text-gray-600">Name:</span>
                            <div class="text-gray-900 font-medium">{{ $newsletterSubscriber->name }}</div>
                        </div>
                        @endif
                        <div>
                            <span class="text-gray-600">Status:</span>
                            <div class="text-gray-900 font-medium">
                                {{ $newsletterSubscriber->is_active ? 'Active' : 'Inactive' }}
                            </div>
                        </div>
                        @if($newsletterSubscriber->subscription_source)
                        <div>
                            <span class="text-gray-600">Source:</span>
                            <div class="text-gray-900 font-medium">{{ ucfirst($newsletterSubscriber->subscription_source) }}</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Subscription Statistics -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Subscribed:</span>
                            <div class="text-gray-900">{{ $newsletterSubscriber->subscribed_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Duration:</span>
                            <div class="text-gray-900">{{ $newsletterSubscriber->subscribed_at->diffForHumans() }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $newsletterSubscriber->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.newsletter-subscribers.edit', $newsletterSubscriber) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Subscriber
                        </a>
                        
                        <form action="{{ route('admin.newsletter-subscribers.destroy', $newsletterSubscriber) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this subscriber? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Delete Subscriber
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Export</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.newsletter-subscribers.export', ['email' => $newsletterSubscriber->email]) }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Export as CSV
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show a temporary success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    });
}
</script>
@endsection
