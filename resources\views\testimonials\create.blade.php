@extends('layouts.public')

@section('title', 'Share Your Experience')
@section('meta_description', 'Share your experience working with us. Your testimonial helps other businesses understand the value we provide.')

@section('content')
<div class="bg-gray-50 py-16">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Share Your Experience</h1>
            <p class="text-xl text-gray-600">
                We'd love to hear about your experience working with us. Your testimonial helps other businesses understand the value we provide.
            </p>
        </div>

        <!-- Success Message -->
        @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">{{ session('success') }}</p>
                </div>
            </div>
        </div>
        @endif

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <form action="{{ route('testimonials.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Client Name -->
                    <div>
                        <label for="client_name" class="block text-sm font-medium text-gray-700 mb-2">Your Name *</label>
                        <input type="text" name="client_name" id="client_name" value="{{ old('client_name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="Enter your full name">
                        @error('client_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Client Position -->
                    <div>
                        <label for="client_position" class="block text-sm font-medium text-gray-700 mb-2">Your Position</label>
                        <input type="text" name="client_position" id="client_position" value="{{ old('client_position') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="e.g., CEO, Manager, Director">
                        @error('client_position')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Client Company -->
                    <div>
                        <label for="client_company" class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                        <input type="text" name="client_company" id="client_company" value="{{ old('client_company') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="Enter your company name">
                        @error('client_company')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Title -->
                    <div>
                        <label for="project_title" class="block text-sm font-medium text-gray-700 mb-2">Project/Service</label>
                        <input type="text" name="project_title" id="project_title" value="{{ old('project_title') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="What service or project did we work on?">
                        @error('project_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Rating -->
                    <div class="md:col-span-2">
                        <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">Rating *</label>
                        <div class="flex items-center space-x-2">
                            @for($i = 1; $i <= 5; $i++)
                                <label class="cursor-pointer">
                                    <input type="radio" name="rating" value="{{ $i }}" {{ old('rating') == $i ? 'checked' : '' }} class="sr-only rating-input">
                                    <svg class="w-8 h-8 text-gray-300 hover:text-yellow-400 transition-colors duration-200 rating-star"
                                         fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                </label>
                            @endfor
                        </div>
                        @error('rating')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Client Image -->
                    <div class="md:col-span-2">
                        <label for="client_image" class="block text-sm font-medium text-gray-700 mb-2">Your Photo (Optional)</label>
                        <input type="file" name="client_image" id="client_image" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        @error('client_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Upload a professional photo (JPG, PNG, GIF - Max 2MB)</p>
                    </div>

                    <!-- Testimonial -->
                    <div class="md:col-span-2">
                        <label for="testimonial" class="block text-sm font-medium text-gray-700 mb-2">Your Testimonial *</label>
                        <textarea name="testimonial" id="testimonial" rows="6" required maxlength="5000"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="Share your experience working with us. What did you like most? How did we help your business?">{{ old('testimonial') }}</textarea>
                        @error('testimonial')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Maximum 5000 characters</p>
                    </div>
                </div>

                <!-- Disclaimer -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                <strong>Please note:</strong> Your testimonial will be reviewed by our team before being published on our website. We may contact you to verify the authenticity of your testimonial.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('testimonials.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                        Cancel
                    </a>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                        Submit Testimonial
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Rating stars functionality
    const ratingInputs = document.querySelectorAll('.rating-input');
    const ratingStars = document.querySelectorAll('.rating-star');

    function updateStars(rating) {
        ratingStars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('text-gray-300');
                star.classList.add('text-yellow-400');
            } else {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            }
        });
    }

    ratingInputs.forEach((input, index) => {
        input.addEventListener('change', function() {
            updateStars(parseInt(this.value));
        });
    });

    // Initialize stars based on old input
    const checkedRating = document.querySelector('.rating-input:checked');
    if (checkedRating) {
        updateStars(parseInt(checkedRating.value));
    }

    // Character counter for testimonial
    const testimonialTextarea = document.getElementById('testimonial');
    const maxLength = 5000;

    function updateCharacterCount() {
        const remaining = maxLength - testimonialTextarea.value.length;
        const existingCounter = document.getElementById('char-counter');

        if (existingCounter) {
            existingCounter.remove();
        }

        const counter = document.createElement('p');
        counter.id = 'char-counter';
        counter.className = 'mt-1 text-sm text-gray-500';
        counter.textContent = `${remaining} characters remaining`;

        if (remaining < 50) {
            counter.className = 'mt-1 text-sm text-orange-500';
        }
        if (remaining < 0) {
            counter.className = 'mt-1 text-sm text-red-500';
        }

        testimonialTextarea.parentNode.appendChild(counter);
    }

    testimonialTextarea.addEventListener('input', updateCharacterCount);
    updateCharacterCount(); // Initial count
});
</script>
@endsection
