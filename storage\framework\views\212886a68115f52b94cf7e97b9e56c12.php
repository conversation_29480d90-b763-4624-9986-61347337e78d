<?php $__env->startSection('content'); ?>
<div class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Services</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We offer comprehensive consulting services to help your business grow, innovate, and succeed in today's competitive marketplace.
            </p>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                <?php if($service->featured_image): ?>
                    <img src="<?php echo e(asset('storage/' . $service->featured_image)); ?>" alt="<?php echo e($service->title); ?>" class="w-full h-48 object-cover">
                <?php endif; ?>
                
                <div class="p-6">
                    <?php if($service->icon): ?>
                        <div class="text-4xl mb-4"><?php echo e($service->icon); ?></div>
                    <?php endif; ?>
                    
                    <h3 class="text-xl font-semibold text-gray-900 mb-3"><?php echo e($service->title); ?></h3>
                    <p class="text-gray-600 mb-4"><?php echo e($service->description); ?></p>
                    
                    <?php if($service->price): ?>
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-2xl font-bold text-green-600"><?php echo e($service->formatted_price); ?></span>
                            <?php if($service->is_featured): ?>
                                <span class="bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded">Featured</span>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('services.show', $service)); ?>" class="inline-flex items-center text-green-600 font-semibold hover:text-green-800">
                        Learn More
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($services->hasPages()): ?>
            <div class="mt-12">
                <?php echo e($services->links()); ?>

            </div>
        <?php endif; ?>

        <!-- Call to Action -->
        <div class="mt-16 bg-green-600 rounded-lg p-8 text-center text-white">
            <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl mb-6 text-green-100">
                Contact us today to discuss how our services can help your business achieve its goals.
            </p>
            <a href="<?php echo e(route('contact')); ?>" class="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
                Contact Us Today
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/services/index.blade.php ENDPATH**/ ?>