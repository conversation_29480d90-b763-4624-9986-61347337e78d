<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($page) && $page->meta_title ? $page->meta_title : (isset($title) ? $title . ' - ' : '') }}{{ $globalSettings->application_name ?? config('app.name', 'Fair Price Ventures') }}</title>

    <!-- Favicon -->
    @if($globalSettings->favicon ?? null)
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . $globalSettings->favicon) }}">
    @endif

    <!-- SEO Meta Tags -->
    @if(isset($page) && $page->meta_description)
        <meta name="description" content="{{ $page->meta_description }}">
    @elseif($globalSettings->tagline ?? null)
        <meta name="description" content="{{ $globalSettings->tagline }}">
    @endif

    @if(isset($page) && $page->meta_keywords)
        <meta name="keywords" content="{{ $page->meta_keywords }}">
    @elseif($globalSettings->seo_keywords ?? null)
        <meta name="keywords" content="{{ $globalSettings->seo_keywords }}">
    @endif

    <!-- Open Graph -->
    <meta property="og:title" content="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}">
    <meta property="og:description" content="{{ $globalSettings->tagline ?? 'Your trusted business partner' }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url('/') }}">
    @if($globalSettings->logo ?? null)
        <meta property="og:image" content="{{ asset('storage/' . $globalSettings->logo) }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:300,400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    @stack('styles')
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Top Bar -->
    <div class="bg-gradient-to-r from-green-800 via-green-700 to-green-600 text-white py-2 hidden md:block">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center text-sm">
                <!-- Contact Details -->
                <div class="flex items-center space-x-6">
                    @if($globalSettings->phone1 ?? null)
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-2 text-green-200"></i>
                            <a href="{{ route('contact') }}" class="hover:text-green-200 transition-colors duration-300" title="Contact us">
                                {{ $globalSettings->phone1 }}
                            </a>
                        </div>
                    @endif

                    @if($globalSettings->email1 ?? null)
                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-green-200"></i>
                            <a href="{{ route('contact') }}" class="hover:text-green-200 transition-colors duration-300" title="Contact us">
                                {{ $globalSettings->email1 }}
                            </a>
                        </div>
                    @endif

                    @if($globalSettings->address ?? null)
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-green-200"></i>
                            <a href="{{ route('contact') }}" class="hover:text-green-200 transition-colors duration-300" title="Find us">
                                {{ Str::limit($globalSettings->address, 40) }}
                            </a>
                        </div>
                    @endif
                </div>

                <!-- Social Media Links -->
                <div class="flex items-center space-x-4">
                    @if($globalSettings->facebook_link ?? null)
                        <a href="{{ $globalSettings->facebook_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                    @endif

                    @if($globalSettings->twitter_link ?? null)
                        <a href="{{ $globalSettings->twitter_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                    @endif

                    @if($globalSettings->linkedin_link ?? null)
                        <a href="{{ $globalSettings->linkedin_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    @endif

                    @if($globalSettings->instagram_link ?? null)
                        <a href="{{ $globalSettings->instagram_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    @endif

                    @if($globalSettings->youtube_link ?? null)
                        <a href="{{ $globalSettings->youtube_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Subscribe to our YouTube channel">
                            <i class="fab fa-youtube"></i>
                        </a>
                    @endif

                    <!-- Default social links if none are set -->
                    @if(!($globalSettings->facebook_link || $globalSettings->twitter_link || $globalSettings->linkedin_link || $globalSettings->instagram_link || $globalSettings->youtube_link))
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo - Left Aligned -->
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="flex-shrink-0 flex items-center">
                        @if($globalSettings->logo ?? null)
                            <img src="{{ asset('storage/' . $globalSettings->logo) }}" alt="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}" class="h-12 w-auto mr-3">
                        @endif
                        {{-- <span class="text-2xl font-bold text-green-600">{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}</span> --}}
                    </a>
                </div>

                <!-- Navigation Links - Split Layout -->
                <div class="hidden md:flex items-center justify-between flex-1 ml-8">
                    <!-- Main Navigation Links -->
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('home') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('home') ? 'text-green-600' : '' }} transition-colors duration-300">
                            Home
                        </a>

                        <!-- About Dropdown -->
                        <div class="relative group">
                            <button class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('about*', 'team.*', 'testimonials.*') ? 'text-green-600' : '' }} flex items-center transition-colors duration-300">
                                About
                                <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                                <a href="{{ route('about') }}" class="block px-4 py-2 text-base text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-300">About Us</a>
                                <a href="{{ route('team.index') }}" class="block px-4 py-2 text-base text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-300">Our Team</a>
                                <a href="{{ route('testimonials.index') }}" class="block px-4 py-2 text-base text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-300">Testimonials</a>
                            </div>
                        </div>

                        <a href="{{ route('services.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('services.*') ? 'text-green-600' : '' }} transition-colors duration-300">
                            Services
                        </a>
                        <a href="{{ route('projects.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('projects.*') ? 'text-green-600' : '' }} transition-colors duration-300">
                            Projects
                        </a>
                        <a href="{{ route('events.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('events.*') ? 'text-green-600' : '' }} transition-colors duration-300">
                            Events
                        </a>
                        <a href="{{ route('partners.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('partners.*') ? 'text-green-600' : '' }} transition-colors duration-300">
                            Partners
                        </a>
                        <a href="{{ route('clients.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('clients.*') ? 'text-green-600' : '' }} transition-colors duration-300">
                            Clients
                        </a>

                        <a href="{{ route('faqs.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('faqs.*') ? 'text-green-600' : '' }} transition-colors duration-300">
                            FAQs
                        </a>
                    </div>

                    <!-- CTA and Auth Section -->
                    <div class="flex items-center space-x-4">
                        <!-- Contact CTA Button -->
                        <a href="{{ route('contact') }}" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg text-base font-semibold hover:from-green-700 hover:to-green-800 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                            <i class="fas fa-phone mr-2"></i>
                            Contact Us
                        </a>

                        @auth
                            @if(auth()->user()->isAdmin() || auth()->user()->isEditor())
                                <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md text-base font-semibold hover:bg-gray-700 transition-colors duration-300">
                                    Admin
                                </a>
                            @endif
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <button type="submit" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-medium transition-colors duration-300">
                                    Logout
                                </button>
                            </form>
                        @else
                            {{-- <a href="{{ route('login') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-medium transition-colors duration-300">
                                Login
                            </a> --}}
                        @endauth
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button x-data x-on:click="$dispatch('toggle-mobile-menu')" class="text-gray-700 hover:text-green-600 focus:outline-none focus:text-green-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div x-data="{ open: false }" x-on:toggle-mobile-menu.window="open = !open" x-show="open" x-transition class="md:hidden">
            <div class="px-4 pt-4 pb-4 space-y-2 sm:px-6 bg-white border-t">
                <a href="{{ route('home') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Home</a>
                <a href="{{ route('about') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">About Us</a>
                <a href="{{ route('team.index') }}" class="block text-gray-700 hover:text-green-600 px-8 py-3 rounded-md text-sm font-medium transition-colors duration-300">Our Team</a>
                <a href="{{ route('testimonials.index') }}" class="block text-gray-700 hover:text-green-600 px-8 py-3 rounded-md text-sm font-medium transition-colors duration-300">Testimonials</a>
                <a href="{{ route('services.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Services</a>
                <a href="{{ route('projects.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Projects</a>
                <a href="{{ route('partners.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Partners</a>
                <a href="{{ route('clients.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Clients</a>
                <a href="{{ route('faqs.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">FAQs</a>

                <!-- Mobile Contact CTA -->
                <div class="pt-4 pb-2">
                    <a href="{{ route('contact') }}" class="block bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-4 rounded-lg text-base font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg text-center">
                        <i class="fas fa-phone mr-2"></i>
                        Contact Us
                    </a>
                </div>

                @auth
                    @if(auth()->user()->isAdmin() || auth()->user()->isEditor())
                        <a href="{{ route('admin.dashboard') }}" class="block bg-gray-600 text-white px-4 py-3 rounded-md text-base font-semibold hover:bg-gray-700 transition-colors duration-300">Admin</a>
                    @endif
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="block w-full text-left text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Logout</button>
                    </form>
                @else
                    {{-- <a href="{{ route('login') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">Login</a> --}}
                @endauth
            </div>
        </div>
    </nav>

    <!-- Page Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        @if($globalSettings->logo ?? null)
                            <img src="{{ asset('storage/' . $globalSettings->logo) }}" alt="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}" class="h-8 w-auto">
                        @endif
                        {{-- <h3 class="text-2xl font-bold text-green-400">{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}</h3> --}}
                    </div>
                    <p class="text-gray-300 mb-4">{{ $globalSettings->tagline ?? 'Your trusted partner in business growth and innovation. We provide exceptional consulting services to help your business thrive.' }}</p>

                    <!-- Newsletter Signup -->
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold mb-2">Subscribe to our Newsletter</h4>
                        @livewire('newsletter-signup', ['source' => 'footer'])
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ route('home') }}" class="text-gray-300 hover:text-green-400">Home</a></li>
                        <li><a href="{{ route('about') }}" class="text-gray-300 hover:text-green-400">About</a></li>
                        <li><a href="{{ route('services.index') }}" class="text-gray-300 hover:text-green-400">Services</a></li>
                        <li><a href="{{ route('partners.index') }}" class="text-gray-300 hover:text-green-400">Partners</a></li>
                        <li><a href="{{ route('clients.index') }}" class="text-gray-300 hover:text-green-400">Clients</a></li>
                        <li><a href="{{ route('testimonials.index') }}" class="text-gray-300 hover:text-green-400">Testimonials</a></li>
                        <li><a href="{{ route('faqs.index') }}" class="text-gray-300 hover:text-green-400">FAQs</a></li>
                        {{-- <li><a href="{{ route('blog.index') }}" class="text-gray-300 hover:text-green-400">Blog</a></li> --}}
                        <li><a href="{{ route('contact') }}" class="text-gray-300 hover:text-green-400">Contact</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                    <ul class="space-y-2 text-gray-300">
                        @if($globalSettings->address ?? null)
                            <li>
                                <a href="{{ route('contact') }}" class="hover:text-green-400 transition-colors duration-300" title="Find us">
                                    {{ $globalSettings->address }}
                                </a>
                            </li>
                        @else
                            <li>
                                <a href="{{ route('contact') }}" class="hover:text-green-400 transition-colors duration-300">
                                    123 Business Street<br>
                                    Suite 100<br>
                                    City, State 12345
                                </a>
                            </li>
                        @endif
                        @if($globalSettings->phone1 ?? null)
                            <li class="mt-4">
                                <strong>Phone:</strong>
                                <a href="{{ route('contact') }}" class="hover:text-green-400 transition-colors duration-300" title="Contact us">
                                    {{ $globalSettings->phone1 }}
                                </a>
                            </li>
                        @endif
                        @if($globalSettings->email1 ?? null)
                            <li>
                                <strong>Email:</strong>
                                <a href="{{ route('contact') }}" class="hover:text-green-400 transition-colors duration-300" title="Contact us">
                                    {{ $globalSettings->email1 }}
                                </a>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p class="text-gray-300">{{ $globalSettings->copyright ?? '© ' . date('Y') . ' Fair Price Ventures Ltd. All rights reserved.' }}</p>
            </div>
        </div>
    </footer>

    @livewireScripts
    @stack('scripts')

    <!-- Global Loading Overlay -->
    <x-loading-overlay />

    <!-- Flash Messages -->
    @if(session('success'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('error') }}
        </div>
    @endif

    @if(session('info'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('info') }}
        </div>
    @endif
</body>
</html>
