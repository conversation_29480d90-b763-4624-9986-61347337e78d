<?php

namespace Database\Seeders;

use App\Models\Tag;
use Illuminate\Database\Seeder;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = [
            [
                'name' => 'Strategy',
                'slug' => 'strategy',
                'color' => '#3B82F6',
            ],
            [
                'name' => 'Innovation',
                'slug' => 'innovation',
                'color' => '#10B981',
            ],
            [
                'name' => 'Leadership',
                'slug' => 'leadership',
                'color' => '#F59E0B',
            ],
            [
                'name' => 'Digital',
                'slug' => 'digital',
                'color' => '#8B5CF6',
            ],
            [
                'name' => 'Growth',
                'slug' => 'growth',
                'color' => '#EF4444',
            ],
            [
                'name' => 'Analytics',
                'slug' => 'analytics',
                'color' => '#06B6D4',
            ],
        ];

        foreach ($tags as $tag) {
            Tag::firstOrCreate(
                ['slug' => $tag['slug']],
                $tag
            );
        }
    }
}
