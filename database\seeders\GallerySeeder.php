<?php

namespace Database\Seeders;

use App\Models\Gallery;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GallerySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample galleries
        $galleries = [
            [
                'title' => 'Corporate Events',
                'slug' => 'corporate-events',
                'description' => 'Professional corporate events and business gatherings showcasing our commitment to excellence.',
                'category' => 'Events',
                'is_published' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'meta_title' => 'Corporate Events Gallery - Professional Business Events',
                'meta_description' => 'Browse our collection of corporate events and business gatherings.',
                'meta_keywords' => 'corporate events, business, professional, meetings',
            ],
            [
                'title' => 'Team Building Activities',
                'slug' => 'team-building-activities',
                'description' => 'Fun and engaging team building activities that strengthen our workplace culture.',
                'category' => 'Team',
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 2,
                'meta_title' => 'Team Building Activities Gallery',
                'meta_description' => 'See our team building activities and workplace culture events.',
                'meta_keywords' => 'team building, activities, workplace culture, team',
            ],
            [
                'title' => 'Office Environment',
                'slug' => 'office-environment',
                'description' => 'A glimpse into our modern office space and work environment.',
                'category' => 'Office',
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 3,
                'meta_title' => 'Office Environment Gallery',
                'meta_description' => 'Tour our modern office space and work environment.',
                'meta_keywords' => 'office, workspace, environment, modern',
            ],
            [
                'title' => 'Community Outreach',
                'slug' => 'community-outreach',
                'description' => 'Our involvement in community service and social responsibility initiatives.',
                'category' => 'Community',
                'is_published' => true,
                'is_featured' => true,
                'sort_order' => 4,
                'meta_title' => 'Community Outreach Gallery',
                'meta_description' => 'See our community service and social responsibility initiatives.',
                'meta_keywords' => 'community, outreach, social responsibility, service',
            ],
            [
                'title' => 'Product Showcase',
                'slug' => 'product-showcase',
                'description' => 'Highlighting our products and services in action.',
                'category' => 'Products',
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 5,
                'meta_title' => 'Product Showcase Gallery',
                'meta_description' => 'Explore our products and services showcase.',
                'meta_keywords' => 'products, services, showcase, solutions',
            ],
        ];

        foreach ($galleries as $galleryData) {
            Gallery::create($galleryData);
        }

        $this->command->info('Sample galleries created successfully!');
    }
}
