<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gallery_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('gallery_id')->constrained()->onDelete('cascade');
            $table->string('title')->nullable();
            $table->text('caption')->nullable();
            $table->string('image_path'); // Path to the image file
            $table->string('thumbnail_path')->nullable(); // Path to thumbnail
            $table->string('alt_text')->nullable(); // Alt text for accessibility
            $table->integer('file_size')->nullable(); // File size in bytes
            $table->string('mime_type')->nullable(); // Image MIME type
            $table->integer('width')->nullable(); // Image width in pixels
            $table->integer('height')->nullable(); // Image height in pixels
            $table->integer('sort_order')->default(0);
            $table->boolean('is_featured')->default(false); // Featured image in gallery
            $table->timestamps();

            // Indexes
            $table->index(['gallery_id', 'sort_order']);
            $table->index(['gallery_id', 'is_featured']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gallery_images');
    }
};
