<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(isset($title) ? $title . ' - ' : ''); ?><?php echo e($globalSettings->application_name ?? config('app.name', 'Fair Price Ventures')); ?></title>

    <!-- Favicon -->
    <?php if($globalSettings->favicon ?? null): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset('storage/' . $globalSettings->favicon)); ?>">
    <?php endif; ?>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo e($globalSettings->tagline ?? 'Your trusted business partner'); ?>">
    <?php if($globalSettings->seo_keywords ?? null): ?>
        <meta name="keywords" content="<?php echo e($globalSettings->seo_keywords); ?>">
    <?php endif; ?>

    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>">
    <meta property="og:description" content="<?php echo e($globalSettings->tagline ?? 'Your trusted business partner'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo e(url('/')); ?>">
    <?php if($globalSettings->logo ?? null): ?>
        <meta property="og:image" content="<?php echo e(asset('storage/' . $globalSettings->logo)); ?>">
    <?php endif; ?>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:300,400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-sans antialiased bg-gradient-to-br from-green-50 via-white to-green-50 min-h-screen">
    <!-- Authentication Content -->
    <main class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Back to Website Link -->
            <div class="text-center">
                <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center text-green-600 hover:text-green-800 font-medium transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Website
                </a>
            </div>

            <!-- Authentication Card -->
            <div class="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-center">
                    <div class="flex justify-center mb-4">
                        <?php if($globalSettings->logo ?? null): ?>
                            <img src="<?php echo e(asset('storage/' . $globalSettings->logo)); ?>" alt="<?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>" class="h-16 w-auto">
                        <?php else: ?>
                            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-user-circle text-green-600 text-3xl"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h1 class="text-2xl font-bold text-white"><?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?></h1>
                    <p class="text-green-100 mt-2"><?php echo e(isset($subtitle) ? $subtitle : 'Welcome back'); ?></p>
                </div>

                <!-- Form Content -->
                <div class="px-8 py-8">
                    <?php echo e($slot); ?>

                </div>
            </div>

            <!-- Footer Links -->
            <div class="text-center">
                <p class="text-gray-600 text-sm">
                    © <?php echo e(date('Y')); ?> <?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>. All rights reserved.
                </p>
            </div>
        </div>
    </main>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Global Loading Overlay -->
    <?php if (isset($component)) { $__componentOriginal115e82920da0ed7c897ee494af74b9d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal115e82920da0ed7c897ee494af74b9d8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.loading-overlay','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('loading-overlay'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal115e82920da0ed7c897ee494af74b9d8)): ?>
<?php $attributes = $__attributesOriginal115e82920da0ed7c897ee494af74b9d8; ?>
<?php unset($__attributesOriginal115e82920da0ed7c897ee494af74b9d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal115e82920da0ed7c897ee494af74b9d8)): ?>
<?php $component = $__componentOriginal115e82920da0ed7c897ee494af74b9d8; ?>
<?php unset($__componentOriginal115e82920da0ed7c897ee494af74b9d8); ?>
<?php endif; ?>

    <!-- Flash Messages -->
    <?php if(session('success')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('info')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            <?php echo e(session('info')); ?>

        </div>
    <?php endif; ?>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/layouts/auth-minimal.blade.php ENDPATH**/ ?>