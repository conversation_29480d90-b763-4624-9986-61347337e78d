<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Testimonial extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_name',
        'client_position',
        'client_company',
        'testimonial',
        'client_image',
        'rating',
        'project_title',
        'sort_order',
        'is_featured',
        'is_active',
        'is_approved',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'is_approved' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeHighRated($query)
    {
        return $query->where('rating', '>=', 4);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    // Accessors
    public function getClientImageUrlAttribute()
    {
        if ($this->client_image && Storage::disk('public')->exists($this->client_image)) {
            return asset('storage/' . $this->client_image);
        }
        return null;
    }

    public function getStarsHtmlAttribute()
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '<i class="fas fa-star text-yellow-400"></i>';
            } else {
                $stars .= '<i class="far fa-star text-gray-300"></i>';
            }
        }
        return $stars;
    }

    public function getClientFullNameAttribute()
    {
        $name = $this->client_name;

        if ($this->client_position && $this->client_company) {
            $name .= ', ' . $this->client_position . ' at ' . $this->client_company;
        } elseif ($this->client_position) {
            $name .= ', ' . $this->client_position;
        } elseif ($this->client_company) {
            $name .= ', ' . $this->client_company;
        }

        return $name;
    }

    public function getExcerptAttribute()
    {
        return \Illuminate\Support\Str::limit($this->testimonial, 150);
    }

    // Static methods
    public static function getFeaturedTestimonials($limit = 3)
    {
        return static::active()->approved()->featured()->ordered()->limit($limit)->get();
    }

    public static function getHighRatedTestimonials($limit = 6)
    {
        return static::active()->approved()->highRated()->ordered()->limit($limit)->get();
    }

    public static function getRecentTestimonials($limit = 5)
    {
        return static::active()->approved()->ordered()->limit($limit)->get();
    }
}
