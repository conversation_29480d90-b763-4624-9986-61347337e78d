@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Contact Enquiry</h1>
            <div class="flex space-x-2">
                <form action="{{ route('admin.contact-enquiries.mark-read', $contactEnquiry) }}" method="POST" class="inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        {{ $contactEnquiry->is_read ? 'Mark as Unread' : 'Mark as Read' }}
                    </button>
                </form>
                <a href="{{ route('admin.contact-enquiries.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Enquiries
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Enquiry Status -->
                <div class="mb-6">
                    @if($contactEnquiry->is_read)
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
                            ✓ Read
                        </span>
                    @else
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
                            ● Unread
                        </span>
                    @endif
                </div>

                <!-- Subject -->
                <div class="mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ $contactEnquiry->subject }}</h2>
                </div>

                <!-- Message -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Message</h3>
                    <div class="prose prose-lg max-w-none">
                        {!! nl2br(e($contactEnquiry->message)) !!}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Contact Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Name:</span>
                            <div class="text-gray-900 font-medium">{{ $contactEnquiry->name }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Email:</span>
                            <div class="text-gray-900">
                                <a href="mailto:{{ $contactEnquiry->email }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $contactEnquiry->email }}
                                </a>
                            </div>
                        </div>
                        @if($contactEnquiry->phone)
                        <div>
                            <span class="text-gray-600">Phone:</span>
                            <div class="text-gray-900">
                                <a href="tel:{{ $contactEnquiry->phone }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $contactEnquiry->phone }}
                                </a>
                            </div>
                        </div>
                        @endif
                        @if($contactEnquiry->company)
                        <div>
                            <span class="text-gray-600">Company:</span>
                            <div class="text-gray-900">{{ $contactEnquiry->company }}</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Enquiry Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Enquiry Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Received:</span>
                            <div class="text-gray-900">{{ $contactEnquiry->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @if($contactEnquiry->is_read && $contactEnquiry->read_at)
                        <div>
                            <span class="text-gray-600">Read:</span>
                            <div class="text-gray-900">{{ $contactEnquiry->read_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @endif
                        <div>
                            <span class="text-gray-600">Source:</span>
                            <div class="text-gray-900">{{ $contactEnquiry->source ?? 'Website Contact Form' }}</div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="mailto:{{ $contactEnquiry->email }}?subject=Re: {{ $contactEnquiry->subject }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Reply via Email
                        </a>
                        
                        <form action="{{ route('admin.contact-enquiries.mark-read', $contactEnquiry) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                {{ $contactEnquiry->is_read ? 'Mark as Unread' : 'Mark as Read' }}
                            </button>
                        </form>

                        <form action="{{ route('admin.contact-enquiries.destroy', $contactEnquiry) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this enquiry? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Delete Enquiry
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
