<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix Services Table
        $this->fixServicesTable();

        // Fix Teams Table
        $this->fixTeamsTable();
    }

    private function fixServicesTable()
    {
        // Backup existing data
        $services = collect();
        if (Schema::hasTable('services')) {
            $services = DB::table('services')->get();
            Schema::drop('services');
        }

        // Create with proper structure
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->longText('content')->nullable();
            $table->string('icon')->nullable(); // Font Awesome icon class or image path
            $table->string('featured_image')->nullable();
            $table->decimal('price', 10, 2)->nullable(); // Starting price
            $table->string('price_type')->default('starting_at'); // starting_at, fixed, custom
            $table->json('features')->nullable(); // Array of service features
            $table->integer('sort_order')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_active']);
            $table->index(['is_featured']);
            $table->index(['slug']);
        });

        // Restore data with proper structure
        foreach ($services as $service) {
            if (isset($service->title) && $service->title) {
                DB::table('services')->insert([
                    'title' => $service->title,
                    'slug' => isset($service->slug) && $service->slug ? $service->slug : \Str::slug($service->title),
                    'description' => $service->description ?? '',
                    'content' => $service->content ?? null,
                    'icon' => $service->icon ?? null,
                    'featured_image' => $service->featured_image ?? null,
                    'price' => isset($service->price) && is_numeric($service->price) ? $service->price : null,
                    'price_type' => $service->price_type ?? 'starting_at',
                    'features' => isset($service->features) && $service->features ? json_encode([$service->features]) : null,
                    'sort_order' => $service->sort_order ?? 0,
                    'is_featured' => isset($service->is_featured) && $service->is_featured ? true : false,
                    'is_active' => isset($service->is_active) && $service->is_active ? true : false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function fixTeamsTable()
    {
        // Backup existing data
        $teams = collect();
        if (Schema::hasTable('teams')) {
            $teams = DB::table('teams')->get();
            Schema::drop('teams');
        }

        // Create with proper structure
        Schema::create('teams', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('position'); // Job title/position
            $table->text('bio')->nullable(); // Biography
            $table->string('avatar')->nullable(); // Profile image
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->json('social_links')->nullable(); // Array of social media links
            $table->json('skills')->nullable(); // Array of skills/expertise
            $table->integer('sort_order')->default(0);
            $table->boolean('is_featured')->default(false); // Show on homepage
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_active']);
            $table->index(['is_featured']);
            $table->index(['slug']);
        });

        // Restore data with proper structure
        foreach ($teams as $team) {
            if (isset($team->name) && $team->name) {
                DB::table('teams')->insert([
                    'name' => $team->name,
                    'slug' => isset($team->slug) && $team->slug ? $team->slug : \Str::slug($team->name),
                    'position' => $team->position ?? 'Team Member',
                    'bio' => $team->bio ?? null,
                    'avatar' => $team->avatar ?? null,
                    'email' => $team->email ?? null,
                    'phone' => $team->phone ?? null,
                    'social_links' => isset($team->social_links) && $team->social_links ? json_encode([$team->social_links]) : null,
                    'skills' => isset($team->skills) && $team->skills ? json_encode([$team->skills]) : null,
                    'sort_order' => $team->sort_order ?? 0,
                    'is_featured' => isset($team->is_featured) && $team->is_featured ? true : false,
                    'is_active' => isset($team->is_active) && $team->is_active ? true : false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
