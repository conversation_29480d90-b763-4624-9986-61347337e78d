@extends('layouts.public')

@section('content')
<!-- About Page Content -->
@if($page->slug === 'about')
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-green-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-16" data-aos="fade-up">
                <h1 class="text-center text-4xl md:text-6xl font-bold text-gray-900 mb-6">{{ $page->title }}</h1>
                @if($globalSettings->about_us)
                    <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed text-justify">
                        {{ $globalSettings->about_us }}
                    </p>
                @endif
            </div>

            @if($page->featured_image)
                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <img src="{{ asset('storage/' . $page->featured_image) }}" alt="{{ $page->title }}" class="w-full max-w-4xl mx-auto h-96 object-cover rounded-lg shadow-xl">
                </div>
            @endif
        </div>
    </section>

    <!-- Mission & Vision Section -->
    @if($globalSettings->mission || $globalSettings->vision)
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                @if($globalSettings->mission)
                <div class="text-center lg:text-left" data-aos="fade-right">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
                        <i class="fas fa-bullseye text-2xl text-green-600"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        {{ $globalSettings->mission }}
                    </p>
                </div>
                @endif

                @if($globalSettings->vision)
                <div class="text-center lg:text-left" data-aos="fade-left">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
                        <i class="fas fa-eye text-2xl text-green-600"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Our Vision</h2>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        {{ $globalSettings->vision }}
                    </p>
                </div>
                @endif
            </div>
        </div>
    </section>
    @endif

    <!-- Additional Page Content -->
    @if($page->content)
    <section class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose prose-lg max-w-none" data-aos="fade-up">
                {!! $page->content !!}
            </div>
        </div>
    </section>
    @endif

    <!-- Team Section -->
    @if((isset($executiveTeam) && $executiveTeam->count() > 0) || (isset($projectTeam) && $projectTeam->count() > 0))
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our experienced professionals are dedicated to delivering exceptional results and driving your success.
                </p>
            </div>

            <!-- Executive Team -->
            @if(isset($executiveTeam) && $executiveTeam->count() > 0)
            <div class="mb-20">
                <h3 class="text-3xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Executive Management</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($executiveTeam as $index => $member)
                        <div class="bg-white rounded-lg shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                            @if($member->image_url)
                                <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-32 h-32 rounded-full mx-auto mb-6 object-cover shadow-lg">
                            @else
                                <div class="w-32 h-32 rounded-full mx-auto mb-6 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                    <span class="text-4xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                                </div>
                            @endif

                            <h4 class="text-xl font-bold text-gray-900 mb-2">{{ $member->name }}</h4>
                            <p class="text-green-600 font-semibold mb-4">{{ $member->position }}</p>

                            @if($member->bio)
                                <p class="text-gray-600 mb-6 leading-relaxed">{{ $member->bio }}</p>
                            @endif

                            @if($member->linkedin || $member->twitter || $member->email)
                                <div class="flex justify-center space-x-4">
                                    @if($member->email)
                                        <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fas fa-envelope text-lg"></i>
                                        </a>
                                    @endif
                                    @if($member->linkedin)
                                        <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-linkedin text-lg"></i>
                                        </a>
                                    @endif
                                    @if($member->twitter)
                                        <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-twitter text-lg"></i>
                                        </a>
                                    @endif
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Project Team -->
            @if(isset($projectTeam) && $projectTeam->count() > 0)
            <div>
                <h3 class="text-3xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Project Management</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    @foreach($projectTeam as $index => $member)
                        <div class="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                            @if($member->image_url)
                                <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-24 h-24 rounded-full mx-auto mb-4 object-cover shadow-lg">
                            @else
                                <div class="w-24 h-24 rounded-full mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                    <span class="text-xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                                </div>
                            @endif

                            <h4 class="text-lg font-bold text-gray-900 mb-2">{{ $member->name }}</h4>
                            <p class="text-green-600 font-semibold text-sm mb-4">{{ $member->position }}</p>

                            @if($member->linkedin || $member->twitter || $member->email)
                                <div class="flex justify-center space-x-3">
                                    @if($member->email)
                                        <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                    @endif
                                    @if($member->linkedin)
                                        <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-linkedin"></i>
                                        </a>
                                    @endif
                                    @if($member->twitter)
                                        <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    @endif
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </section>
    @endif

@else
    <!-- Default Page Layout -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $page->title }}</h1>
        </div>

        <!-- Page Content -->
        <div class="prose prose-lg max-w-none">
            @if($page->featured_image)
                <img src="{{ asset('storage/' . $page->featured_image) }}" alt="{{ $page->title }}" class="w-full h-64 object-cover rounded-lg mb-8">
            @endif

            {!! $page->content !!}
        </div>

        <!-- Contact Page Specific Content -->
        @if($page->slug === 'contact')
            <!-- Contact Information Section -->
            <div class="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Details -->
                <div class="space-y-8">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Get in Touch</h2>
                        <p class="text-lg text-gray-600 mb-8">
                            We'd love to hear from you. Send us a message and we'll respond as soon as possible.
                        </p>
                    </div>

                    <!-- Contact Information Cards -->
                    <div class="space-y-6">
                        @if($globalSettings->address)
                            <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-map-marker-alt text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Our Address</h3>
                                    <p class="text-gray-700">{{ $globalSettings->address }}</p>
                                </div>
                            </div>
                        @endif

                        @if($globalSettings->phone1 || $globalSettings->phone2)
                            <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-phone text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Phone Numbers</h3>
                                    <div class="space-y-1">
                                        @if($globalSettings->phone1)
                                            <p class="text-gray-700">
                                                <a href="tel:{{ $globalSettings->phone1 }}" class="hover:text-green-600 transition-colors duration-200">
                                                    {{ $globalSettings->phone1 }}
                                                </a>
                                            </p>
                                        @endif
                                        @if($globalSettings->phone2)
                                            <p class="text-gray-700">
                                                <a href="tel:{{ $globalSettings->phone2 }}" class="hover:text-green-600 transition-colors duration-200">
                                                    {{ $globalSettings->phone2 }}
                                                </a>
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($globalSettings->email1 || $globalSettings->email2)
                            <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Addresses</h3>
                                    <div class="space-y-1">
                                        @if($globalSettings->email1)
                                            <p class="text-gray-700">
                                                <a href="mailto:{{ $globalSettings->email1 }}" class="hover:text-green-600 transition-colors duration-200">
                                                    {{ $globalSettings->email1 }}
                                                </a>
                                            </p>
                                        @endif
                                        @if($globalSettings->email2)
                                            <p class="text-gray-700">
                                                <a href="mailto:{{ $globalSettings->email2 }}" class="hover:text-green-600 transition-colors duration-200">
                                                    {{ $globalSettings->email2 }}
                                                </a>
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Business Hours -->
                        <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <i class="fas fa-clock text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Business Hours</h3>
                                <div class="space-y-1 text-gray-700">
                                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                                    <p>Saturday: 10:00 AM - 4:00 PM</p>
                                    <p>Sunday: Closed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Send us a Message</h2>
                    @livewire('contact-form')
                </div>
            </div>

            <!-- Map Section -->
            @if($globalSettings->map)
                <div class="mt-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Find Us</h2>
                    <div class="bg-gray-100 rounded-lg overflow-hidden shadow-lg">
                        <div class="aspect-w-16 aspect-h-9" style="height: 400px;">
                            {!! $globalSettings->map !!}
                        </div>
                    </div>
                </div>
            @endif
        @endif
    </div>
@endif
@endsection

@if($page->slug === 'about')
@push('styles')
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
@endpush

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
@endpush
@endif
