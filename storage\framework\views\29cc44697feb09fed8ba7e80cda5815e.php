<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(isset($title) ? $title . ' - ' : ''); ?>Admin Panel - <?php echo e($globalSettings->application_name ?? config('app.name', 'Fair Price Ventures')); ?></title>

    <!-- Favicon -->
    <?php if($globalSettings->favicon ?? null): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset('storage/' . $globalSettings->favicon)); ?>">
    <?php endif; ?>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:300,400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Custom Admin Styles -->
    <style>
        /* Ensure proper navigation alignment */
        .admin-nav {
            display: flex;
            align-items: center;
            height: 64px;
        }

        .admin-nav .nav-item {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 0.25rem;
        }

        .admin-nav .nav-item button,
        .admin-nav .nav-item a {
            display: flex;
            align-items: center;
            height: 100%;
            padding-top: 0.25rem;
            padding-bottom: 0.25rem;
        }

        /* Dropdown positioning */
        .dropdown-menu {
            top: 100%;
            left: 0;
            margin-top: 0.5rem;
        }

        /* Ensure consistent button heights */
        .admin-nav button {
            min-height: 2.5rem;
            display: flex;
            align-items: center;
        }

        /* Fix dropdown z-index */
        .dropdown-menu {
            z-index: 1000;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-100">
    <div class="min-h-screen" x-data="{ mobileMenuOpen: false }">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <!-- Logo -->
                        <div class="flex-shrink-0 flex items-center">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex items-center space-x-2">
                                <?php if($globalSettings->logo ?? null): ?>
                                    <img src="<?php echo e(asset('storage/' . $globalSettings->logo)); ?>" alt="<?php echo e($globalSettings->application_name ?? 'Admin Panel'); ?>" class="h-8 w-auto">
                                <?php else: ?>
                                    <span class="text-xl font-bold text-green-600"><?php echo e($globalSettings->application_name ?? 'Admin Panel'); ?></span>
                                <?php endif; ?>
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <div class="hidden space-x-6 sm:-my-px sm:ml-10 sm:flex admin-nav">
                            <div class="nav-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->routeIs('admin.dashboard') ? 'border-green-500 text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:border-green-700 transition duration-150 ease-in-out">
                                    Dashboard
                                </a>
                            </div>

                            <!-- Content Management Dropdown -->
                            <div class="relative nav-item" x-data="{ open: false }">
                                <button @click="open = !open" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->routeIs('admin.pages.*', 'admin.services.*', 'admin.projects.*', 'admin.events.*', 'admin.galleries.*') ? 'border-green-500 text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:border-green-700 transition duration-150 ease-in-out">
                                    Content
                                    <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="dropdown-menu absolute left-0 z-50 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
                                    <a href="<?php echo e(route('admin.pages.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Pages</a>
                                    <a href="<?php echo e(route('admin.services.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Services</a>
                                    <a href="<?php echo e(route('admin.projects.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Projects</a>
                                    <a href="<?php echo e(route('admin.events.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Events</a>
                                    <a href="<?php echo e(route('admin.galleries.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Gallery</a>
                                </div>
                            </div>

                            <!-- About Dropdown -->
                            <div class="relative nav-item" x-data="{ open: false }">
                                <button @click="open = !open" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->routeIs('admin.teams.*', 'admin.testimonials.*', 'admin.partners.*', 'admin.faqs.*') ? 'border-green-500 text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:border-green-700 transition duration-150 ease-in-out">
                                    About
                                    <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="dropdown-menu absolute left-0 z-50 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
                                    <a href="<?php echo e(route('admin.teams.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Team</a>
                                    <a href="<?php echo e(route('admin.testimonials.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Testimonials</a>
                                    <a href="<?php echo e(route('admin.faqs.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">FAQs</a>
                                    <a href="<?php echo e(route('admin.partners.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Partners & Clients</a>
                                </div>
                            </div>

                            <!-- Blog Management Dropdown -->
                            <div class="relative nav-item" x-data="{ open: false }">
                                <button @click="open = !open" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->routeIs('admin.blog-posts.*', 'admin.categories.*', 'admin.tags.*') ? 'border-green-500 text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:border-green-700 transition duration-150 ease-in-out">
                                    Blog
                                    <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="dropdown-menu absolute left-0 z-50 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
                                    <a href="<?php echo e(route('admin.blog-posts.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Blog Posts</a>
                                    <a href="<?php echo e(route('admin.categories.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Categories</a>
                                    <a href="<?php echo e(route('admin.tags.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Tags</a>
                                </div>
                            </div>

                            <!-- Management Dropdown -->
                            <div class="relative nav-item" x-data="{ open: false }">
                                <button @click="open = !open" class="inline-flex items-center px-1 pt-1 border-b-2 <?php echo e(request()->routeIs('admin.contact-enquiries.*', 'admin.newsletter-subscribers.*', 'admin.users.*', 'admin.settings.*', 'admin.menus.*') ? 'border-green-500 text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> text-sm font-medium leading-5 focus:outline-none focus:border-green-700 transition duration-150 ease-in-out">
                                    Management
                                    <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="dropdown-menu absolute left-0 z-50 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
                                    <a href="<?php echo e(route('admin.contact-enquiries.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Contact Enquiries</a>
                                    <a href="<?php echo e(route('admin.newsletter-subscribers.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Newsletter</a>
                                    <a href="<?php echo e(route('admin.users.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Users</a>
                                    <a href="<?php echo e(route('admin.menus.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Menu Management</a>
                                    <div class="border-t border-gray-100 my-1"></div>
                                    <a href="<?php echo e(route('admin.settings.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Settings</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Dropdown -->
                    <div class="hidden sm:flex sm:items-center sm:ml-6">
                        <a href="<?php echo e(route('home')); ?>" target="_blank" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            View Site
                        </a>

                        <div class="ml-3 relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                                <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium hover:bg-green-700">
                                    <?php echo e(substr(auth()->user()->name, 0, 1)); ?>

                                </div>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                <div class="py-1">
                                    <div class="px-4 py-2 text-xs text-gray-500 border-b border-gray-100">
                                        <?php echo e(auth()->user()->name); ?>

                                    </div>
                                    <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">Profile</a>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out">
                                            Log Out
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hamburger -->
                    <div class="-mr-2 flex items-center sm:hidden">
                        <button @click="mobileMenuOpen = !mobileMenuOpen" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out">
                            <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div x-show="mobileMenuOpen" @click.away="mobileMenuOpen = false" x-transition class="sm:hidden">
                <div class="pt-2 pb-3 space-y-1">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="block pl-3 pr-4 py-2 border-l-4 <?php echo e(request()->routeIs('admin.dashboard') ? 'border-green-500 text-green-700 bg-green-50' : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300'); ?> text-base font-medium transition duration-150 ease-in-out">
                        Dashboard
                    </a>

                    <!-- Content Section -->
                    <div class="border-t border-gray-200 pt-2">
                        <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Content</div>
                        <a href="<?php echo e(route('admin.pages.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Pages
                        </a>
                        <a href="<?php echo e(route('admin.services.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Services
                        </a>
                        <a href="<?php echo e(route('admin.projects.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Projects
                        </a>
                        <a href="<?php echo e(route('admin.events.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Events
                        </a>
                        <a href="<?php echo e(route('admin.galleries.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Gallery
                        </a>
                    </div>

                    <!-- About Section -->
                    <div class="border-t border-gray-200 pt-2">
                        <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">About</div>
                        <a href="<?php echo e(route('admin.teams.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Team
                        </a>
                        <a href="<?php echo e(route('admin.testimonials.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Testimonials
                        </a>
                        <a href="<?php echo e(route('admin.faqs.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            FAQs
                        </a>
                        <a href="<?php echo e(route('admin.partners.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Partners & Clients
                        </a>
                    </div>

                    <!-- Blog Section -->
                    <div class="border-t border-gray-200 pt-2">
                        <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Blog</div>
                        <a href="<?php echo e(route('admin.blog-posts.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Blog Posts
                        </a>
                        <a href="<?php echo e(route('admin.categories.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Categories
                        </a>
                        <a href="<?php echo e(route('admin.tags.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Tags
                        </a>
                    </div>

                    <!-- Management Section -->
                    <div class="border-t border-gray-200 pt-2">
                        <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Management</div>
                        <a href="<?php echo e(route('admin.contact-enquiries.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Contact Enquiries
                        </a>
                        <a href="<?php echo e(route('admin.newsletter-subscribers.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Newsletter
                        </a>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Users
                        </a>
                        <a href="<?php echo e(route('admin.menus.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Menu Management
                        </a>
                        <a href="<?php echo e(route('admin.settings.index')); ?>" class="block pl-6 pr-4 py-2 text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition duration-150 ease-in-out">
                            Settings
                        </a>
                    </div>
                </div>

                <!-- Mobile User Menu -->
                <div class="pt-4 pb-3 border-t border-gray-200">
                    <div class="flex items-center px-4">
                        <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                            <?php echo e(substr(auth()->user()->name, 0, 1)); ?>

                        </div>
                        <div class="ml-3">
                            <div class="text-base font-medium text-gray-800"><?php echo e(auth()->user()->name); ?></div>
                            <div class="text-sm font-medium text-gray-500"><?php echo e(auth()->user()->email); ?></div>
                        </div>
                    </div>
                    <div class="mt-3 space-y-1">
                        <a href="<?php echo e(route('home')); ?>" target="_blank" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition duration-150 ease-in-out">
                            View Site
                        </a>
                        <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition duration-150 ease-in-out">
                            Profile
                        </a>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition duration-150 ease-in-out">
                                Log Out
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Heading -->
        <?php if(isset($header)): ?>
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <?php echo e($header); ?>

                </div>
            </header>
        <?php endif; ?>

        <!-- Page Content -->
        <main class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Flash Messages -->
                <?php if(session('success')): ?>
                    <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
                        <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                        <span @click="show = false" class="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer">
                            <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                        <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                        <span @click="show = false" class="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer">
                            <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                <?php endif; ?>

                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </main>
    </div>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <!-- Admin Tables JavaScript -->
    <script src="<?php echo e(asset('js/admin-tables.js')); ?>"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Global Loading Overlay -->
    <?php if (isset($component)) { $__componentOriginal115e82920da0ed7c897ee494af74b9d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal115e82920da0ed7c897ee494af74b9d8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.loading-overlay','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('loading-overlay'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal115e82920da0ed7c897ee494af74b9d8)): ?>
<?php $attributes = $__attributesOriginal115e82920da0ed7c897ee494af74b9d8; ?>
<?php unset($__attributesOriginal115e82920da0ed7c897ee494af74b9d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal115e82920da0ed7c897ee494af74b9d8)): ?>
<?php $component = $__componentOriginal115e82920da0ed7c897ee494af74b9d8; ?>
<?php unset($__componentOriginal115e82920da0ed7c897ee494af74b9d8); ?>
<?php endif; ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/layouts/admin.blade.php ENDPATH**/ ?>