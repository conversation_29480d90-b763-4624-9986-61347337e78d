<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('general_settings', function (Blueprint $table) {
            $table->id();

            // Basic Site Information
            $table->string('application_name')->nullable();
            $table->string('tagline')->nullable();
            $table->text('catchphrase')->nullable();
            $table->text('vision')->nullable();
            $table->text('mission')->nullable();
            $table->text('copyright')->nullable();
            $table->text('about_us')->nullable();

            // Images and Branding
            $table->string('favicon')->nullable();
            $table->string('logo')->nullable();
            $table->string('header_image')->nullable();
            $table->string('banner_image')->nullable();

            // Contact Information
            $table->string('phone1')->nullable();
            $table->string('phone2')->nullable();
            $table->string('email1')->nullable();
            $table->string('email2')->nullable();
            $table->text('address')->nullable();
            $table->text('map')->nullable();

            // System Configuration
            $table->string('timezone')->default('UTC');
            $table->string('currency', 10)->default('NGN');
            $table->string('default_language', 10)->default('en');

            // Email Configuration
            $table->string('email_from')->nullable();
            $table->string('smtp_host')->nullable();
            $table->integer('smtp_port')->nullable();
            $table->string('smtp_user')->nullable();
            $table->string('smtp_pass')->nullable();

            // Social Media Links
            $table->string('facebook_link')->default('#');
            $table->string('twitter_link')->default('#');
            $table->string('google_link')->default('#');
            $table->string('youtube_link')->default('#');
            $table->string('linkedin_link')->default('#');
            $table->string('instagram_link')->default('#');
            $table->string('whatsapp_link')->default('#');

            // Security & Integrations
            $table->string('recaptcha_site_key')->nullable();
            $table->string('recaptcha_secret_key')->nullable();
            $table->string('recaptcha_lang')->default('en');

            // SMS Configuration
            $table->string('sms_senderid')->nullable();
            $table->string('sms_username')->nullable();
            $table->string('sms_password')->nullable();

            // Push Notifications
            $table->text('push_public_key')->nullable();
            $table->text('push_private_key')->nullable();

            // SEO
            $table->text('seo_keywords')->nullable();

            // Newsletter Popup Settings
            $table->boolean('newsletter_popup_enabled')->default(false);
            $table->integer('newsletter_popup_delay')->default(5); // seconds
            $table->string('newsletter_popup_title')->nullable();
            $table->text('newsletter_popup_message')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('general_settings');
    }
};
