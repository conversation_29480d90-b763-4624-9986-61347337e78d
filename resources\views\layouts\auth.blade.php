<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($title) ? $title . ' - ' : '' }}{{ $globalSettings->application_name ?? config('app.name', 'Fair Price Ventures') }}</title>

    <!-- Favicon -->
    @if($globalSettings->favicon ?? null)
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . $globalSettings->favicon) }}">
    @endif

    <!-- SEO Meta Tags -->
    <meta name="description" content="{{ $globalSettings->tagline ?? 'Your trusted business partner' }}">
    @if($globalSettings->seo_keywords ?? null)
        <meta name="keywords" content="{{ $globalSettings->seo_keywords }}">
    @endif

    <!-- Open Graph -->
    <meta property="og:title" content="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}">
    <meta property="og:description" content="{{ $globalSettings->tagline ?? 'Your trusted business partner' }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url('/') }}">
    @if($globalSettings->logo ?? null)
        <meta property="og:image" content="{{ asset('storage/' . $globalSettings->logo) }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:300,400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    @stack('styles')
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Top Bar -->
    <div class="bg-gradient-to-r from-green-800 via-green-700 to-green-600 text-white py-2 hidden md:block">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center text-sm">
                <!-- Contact Details -->
                <div class="flex items-center space-x-6">
                    @if($globalSettings->phone1 ?? null)
                        <div class="flex items-center">
                            <i class="fas fa-phone mr-2 text-green-200"></i>
                            <a href="tel:{{ $globalSettings->phone1 }}" class="hover:text-green-200 transition-colors duration-300">
                                {{ $globalSettings->phone1 }}
                            </a>
                        </div>
                    @endif

                    @if($globalSettings->email1 ?? null)
                        <div class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-green-200"></i>
                            <a href="mailto:{{ $globalSettings->email1 }}" class="hover:text-green-200 transition-colors duration-300">
                                {{ $globalSettings->email1 }}
                            </a>
                        </div>
                    @endif

                    @if($globalSettings->address ?? null)
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-green-200"></i>
                            <span>{{ Str::limit($globalSettings->address, 40) }}</span>
                        </div>
                    @endif
                </div>

                <!-- Social Media Links -->
                <div class="flex items-center space-x-4">
                    @if($globalSettings->facebook_link ?? null)
                        <a href="{{ $globalSettings->facebook_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                    @endif

                    @if($globalSettings->twitter_link ?? null)
                        <a href="{{ $globalSettings->twitter_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                    @endif

                    @if($globalSettings->linkedin_link ?? null)
                        <a href="{{ $globalSettings->linkedin_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    @endif

                    @if($globalSettings->instagram_link ?? null)
                        <a href="{{ $globalSettings->instagram_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    @endif

                    @if($globalSettings->youtube_link ?? null)
                        <a href="{{ $globalSettings->youtube_link }}" target="_blank" class="hover:text-green-200 transition-colors duration-300" title="Subscribe to our YouTube channel">
                            <i class="fab fa-youtube"></i>
                        </a>
                    @endif

                    <!-- Default social links if none are set -->
                    @if(!($globalSettings->facebook_link || $globalSettings->twitter_link || $globalSettings->linkedin_link || $globalSettings->instagram_link || $globalSettings->youtube_link))
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Follow us on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="hover:text-green-200 transition-colors duration-300" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo - Left Aligned -->
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="flex-shrink-0 flex items-center">
                        @if($globalSettings->logo ?? null)
                            <img src="{{ asset('storage/' . $globalSettings->logo) }}" alt="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}" class="h-12 w-auto mr-3">
                        @endif
                    </a>
                </div>

                <!-- Navigation Links - Right Aligned -->
                <div class="hidden md:flex items-center space-x-2">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-green-600 px-4 py-2 rounded-md text-lg font-semibold transition-colors duration-300">
                        Home
                    </a>
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-green-600 px-4 py-2 rounded-md text-lg font-semibold transition-colors duration-300">
                        About
                    </a>
                    <a href="{{ route('services.index') }}" class="text-gray-700 hover:text-green-600 px-4 py-2 rounded-md text-lg font-semibold transition-colors duration-300">
                        Services
                    </a>
                    <a href="{{ route('partners.index') }}" class="text-gray-700 hover:text-green-600 px-4 py-2 rounded-md text-lg font-semibold transition-colors duration-300">
                        Partners
                    </a>
                    <a href="{{ route('clients.index') }}" class="text-gray-700 hover:text-green-600 px-4 py-2 rounded-md text-lg font-semibold transition-colors duration-300">
                        Clients
                    </a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-green-600 px-4 py-2 rounded-md text-lg font-semibold transition-colors duration-300">
                        Contact
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button x-data x-on:click="$dispatch('toggle-mobile-menu')" class="text-gray-700 hover:text-green-600 focus:outline-none focus:text-green-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div x-data="{ open: false }" x-on:toggle-mobile-menu.window="open = !open" x-show="open" x-transition class="md:hidden">
            <div class="px-4 pt-4 pb-4 space-y-2 sm:px-6 bg-white border-t">
                <a href="{{ route('home') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">Home</a>
                <a href="{{ route('about') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">About</a>
                <a href="{{ route('services.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">Services</a>
                <a href="{{ route('partners.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">Partners</a>
                <a href="{{ route('clients.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">Clients</a>
                <a href="{{ route('contact') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-lg font-semibold transition-colors duration-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Authentication Content -->
    <main class="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50 py-12">
        <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Authentication Card -->
            <div class="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-center">
                    <div class="flex justify-center mb-4">
                        @if($globalSettings->logo ?? null)
                            <img src="{{ asset('storage/' . $globalSettings->logo) }}" alt="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}" class="h-16 w-auto">
                        @else
                            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-user-circle text-green-600 text-3xl"></i>
                            </div>
                        @endif
                    </div>
                    <h1 class="text-2xl font-bold text-white">{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}</h1>
                    <p class="text-green-100 mt-2">{{ isset($subtitle) ? $subtitle : 'Welcome back' }}</p>
                </div>

                <!-- Form Content -->
                <div class="px-8 py-8">
                    {{ $slot }}
                </div>
            </div>

            <!-- Footer Links -->
            <div class="text-center mt-8">
                <p class="text-gray-600 text-sm">
                    © {{ date('Y') }} {{ $globalSettings->application_name ?? 'Fair Price Ventures' }}. All rights reserved.
                </p>
            </div>
        </div>
    </main>

    @livewireScripts
    @stack('scripts')

    <!-- Global Loading Overlay -->
    <x-loading-overlay />

    <!-- Flash Messages -->
    @if(session('success'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('error') }}
        </div>
    @endif

    @if(session('info'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('info') }}
        </div>
    @endif

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</body>
</html>
