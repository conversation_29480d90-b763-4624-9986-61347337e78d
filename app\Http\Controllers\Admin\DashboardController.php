<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\ContactEnquiry;
use App\Models\NewsletterSubscriber;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_blog_posts' => BlogPost::count(),
            'published_blog_posts' => BlogPost::published()->count(),
            'total_newsletter_subscribers' => NewsletterSubscriber::active()->count(),
            'total_contact_enquiries' => ContactEnquiry::count(),
            'unread_enquiries' => ContactEnquiry::unread()->count(),
            'total_users' => User::count(),
        ];

        $recent_enquiries = ContactEnquiry::latest()->limit(5)->get();
        $recent_blog_posts = BlogPost::with('user', 'category')->latest()->limit(5)->get();
        $recent_subscribers = NewsletterSubscriber::latest()->limit(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_enquiries', 'recent_blog_posts', 'recent_subscribers'));
    }
}
