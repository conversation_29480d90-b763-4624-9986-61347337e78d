@extends('layouts.public')

@section('content')
<div class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Service Header -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            @if($service->featured_image)
                <img src="{{ asset('storage/' . $service->featured_image) }}" alt="{{ $service->title }}" class="w-full h-64 object-cover">
            @endif

            <div class="p-8">
                <div class="flex items-center mb-4">
                    @if($service->icon)
                        <div class="text-5xl mr-4">{{ $service->icon }}</div>
                    @endif
                    <div>
                        <h1 class="text-4xl font-bold text-gray-900 mb-2">{{ $service->title }}</h1>
                        @if($service->is_featured)
                            <span class="bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded">Featured Service</span>
                        @endif
                    </div>
                </div>

                <p class="text-xl text-gray-600 mb-6">{{ $service->description }}</p>

                @if($service->price)
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Investment</h3>
                                <p class="text-3xl font-bold text-green-600">{{ $service->formatted_price }}</p>
                            </div>
                            <a href="{{ route('contact') }}" class="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition duration-300">
                                Get Started
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Service Content -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <div class="prose prose-lg max-w-none">
                {!! $service->content !!}
            </div>
        </div>

        <!-- Contact Section -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Interested in This Service?</h2>
            <p class="text-gray-600 mb-6">
                Contact us to learn more about how this service can benefit your business. We'll be happy to discuss your specific needs and provide a customized solution.
            </p>
            @livewire('contact-form')
        </div>

        <!-- Service FAQs -->
        @if($serviceFaqs->count() > 0)
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <div class="space-y-4">
                @foreach($serviceFaqs as $faq)
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-green-500 faq-toggle"
                            data-target="faq-{{ $faq->id }}">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">{{ $faq->question }}</h3>
                            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 faq-icon"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </button>
                    <div id="faq-{{ $faq->id }}" class="faq-content hidden px-6 pb-4">
                        <div class="text-gray-700 prose prose-sm max-w-none">
                            {!! $faq->answer !!}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <div class="mt-6 text-center">
                <a href="{{ route('faqs.index') }}" class="text-green-600 hover:text-green-800 font-medium">
                    View All FAQs →
                </a>
            </div>
        </div>
        @endif

        <!-- Related Services -->
        @if($relatedServices->count() > 0)
        <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($relatedServices as $relatedService)
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-300">
                    @if($relatedService->icon)
                        <div class="text-3xl mb-3">{{ $relatedService->icon }}</div>
                    @endif

                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $relatedService->title }}</h3>
                    <p class="text-gray-600 text-sm mb-3">{{ Str::limit($relatedService->description, 100) }}</p>

                    @if($relatedService->price)
                        <p class="text-green-600 font-semibold text-sm mb-3">{{ $relatedService->formatted_price }}</p>
                    @endif

                    <a href="{{ route('services.show', $relatedService) }}" class="text-green-600 font-semibold text-sm hover:text-green-800">
                        Learn More →
                    </a>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');

    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
});
</script>
@endsection
