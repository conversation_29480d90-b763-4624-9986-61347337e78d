@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Testimonial Details</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.testimonials.edit', $testimonial) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Edit
            </a>
            <a href="{{ route('admin.testimonials.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Testimonials
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Client Photo -->
                <div class="lg:col-span-1">
                    @if($testimonial->client_image_url)
                        <img src="{{ $testimonial->client_image_url }}" alt="{{ $testimonial->client_name }}" class="w-full max-w-sm mx-auto rounded-lg shadow-md">
                    @else
                        <div class="w-full max-w-sm mx-auto bg-gray-200 rounded-lg shadow-md flex items-center justify-center h-64">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-gray-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                                    <span class="text-2xl font-bold text-white">{{ substr($testimonial->client_name, 0, 1) }}</span>
                                </div>
                                <p class="text-gray-500">No photo uploaded</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Testimonial Info -->
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <!-- Basic Info -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $testimonial->client_name }}</h2>
                            @if($testimonial->client_position || $testimonial->client_company)
                                <p class="text-lg text-gray-600 mb-4">
                                    {{ $testimonial->client_position }}
                                    @if($testimonial->client_position && $testimonial->client_company) at @endif
                                    {{ $testimonial->client_company }}
                                </p>
                            @endif
                            
                            <div class="flex flex-wrap gap-4 mb-4">
                                <div class="flex items-center">
                                    {!! $testimonial->stars_html !!}
                                    <span class="ml-2 text-sm text-gray-600">({{ $testimonial->rating }}/5)</span>
                                </div>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $testimonial->is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $testimonial->is_featured ? 'Featured' : 'Regular' }}
                                </span>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $testimonial->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $testimonial->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <!-- Project Info -->
                        @if($testimonial->project_title)
                        <div>
                            <h3 class="text-sm font-semibold text-gray-900 mb-1">Related Project</h3>
                            <p class="text-gray-700">{{ $testimonial->project_title }}</p>
                        </div>
                        @endif

                        <!-- Testimonial Content -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Testimonial</h3>
                            <div class="bg-gray-50 rounded-lg p-6 border-l-4 border-green-500">
                                <blockquote class="text-gray-700 leading-relaxed italic text-lg">
                                    "{{ $testimonial->testimonial }}"
                                </blockquote>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                    <span class="font-medium">Rating:</span> {{ $testimonial->rating }}/5 stars
                                </div>
                                <div>
                                    <span class="font-medium">Sort Order:</span> {{ $testimonial->sort_order }}
                                </div>
                                <div>
                                    <span class="font-medium">Created:</span> {{ $testimonial->created_at->format('M j, Y g:i A') }}
                                </div>
                                <div>
                                    <span class="font-medium">Updated:</span> {{ $testimonial->updated_at->format('M j, Y g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-gray-50 px-6 py-4 border-t">
            <div class="flex justify-between items-center">
                <div class="flex space-x-2">
                    <form action="{{ route('admin.testimonials.toggle-featured', $testimonial) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                            {{ $testimonial->is_featured ? 'Unfeature' : 'Feature' }}
                        </button>
                    </form>
                    
                    <form action="{{ route('admin.testimonials.toggle-status', $testimonial) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            {{ $testimonial->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>
                </div>
                
                <form action="{{ route('admin.testimonials.destroy', $testimonial) }}" method="POST" class="inline" 
                      onsubmit="return confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Delete Testimonial
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
