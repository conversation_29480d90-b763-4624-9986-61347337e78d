<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'client_name' => '<PERSON><PERSON>',
                'client_position' => 'CEO',
                'client_company' => 'NNPC',
                'testimonial' => 'Exceptional Service',
                'rating' => 5,
                'sort_order' => 1,
                'is_featured' => true,
                'is_active' => true,
                'is_approved' => true,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::firstOrCreate(
                ['client_name' => $testimonial['client_name'], 'client_company' => $testimonial['client_company']],
                $testimonial
            );
        }
    }
}
