<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Business Strategy',
                'slug' => 'business-strategy',
                'description' => 'Articles about business strategy and planning',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Technology',
                'slug' => 'technology',
                'description' => 'Technology trends and insights',
                'color' => '#10b981',
                'sort_order' => 2,
            ],
            [
                'name' => 'Finance',
                'slug' => 'finance',
                'description' => 'Financial advice and market insights',
                'color' => '#F59E0B',
                'sort_order' => 3,
            ],
            [
                'name' => 'Industry News',
                'slug' => 'industry-news',
                'description' => 'Latest industry news and updates',
                'color' => '#EF4444',
                'sort_order' => 4,
            ],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
