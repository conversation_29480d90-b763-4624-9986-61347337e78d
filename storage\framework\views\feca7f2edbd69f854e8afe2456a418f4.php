
<?php if(isset($mainMenu) && $mainMenu && $mainMenu->count() > 0): ?>
    <?php $__currentLoopData = $mainMenu; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($item->children->count() > 0): ?>
            
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" @click.away="open = false"
                        class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold transition-colors duration-300 flex items-center">
                    <?php if($item->icon): ?>
                        <i class="<?php echo e($item->icon); ?> mr-2"></i>
                    <?php endif; ?>
                    <?php echo e($item->title); ?>

                    <i class="fas fa-chevron-down ml-1 text-sm"></i>
                </button>

                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                    <div class="py-1" role="menu">
                        <?php $__currentLoopData = $item->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                try {
                                    $childUrl = $child->full_url;
                                } catch (\Exception $e) {
                                    $childUrl = '#';
                                }
                            ?>
                            <a href="<?php echo e($childUrl); ?>" target="<?php echo e($child->target ?? '_self'); ?>"
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out <?php echo e($child->css_class ?? ''); ?>"
                               role="menuitem">
                                <?php if($child->icon): ?>
                                    <i class="<?php echo e($child->icon); ?> mr-2"></i>
                                <?php endif; ?>
                                <?php echo e($child->title); ?>

                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php else: ?>
            
            <?php
                try {
                    $itemUrl = $item->full_url;
                } catch (\Exception $e) {
                    $itemUrl = '#';
                }
            ?>
            <a href="<?php echo e($itemUrl); ?>" target="<?php echo e($item->target ?? '_self'); ?>"
               class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold transition-colors duration-300 <?php echo e($item->css_class ?? ''); ?>">
                <?php if($item->icon): ?>
                    <i class="<?php echo e($item->icon); ?> mr-2"></i>
                <?php endif; ?>
                <?php echo e($item->title); ?>

            </a>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    
    <a href="<?php echo e(route('home')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('home') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        Home
    </a>
    <a href="<?php echo e(route('about')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('about') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        About
    </a>
    <a href="<?php echo e(route('services.index')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('services.*') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        Services
    </a>
    <a href="<?php echo e(route('projects.index')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('projects.*') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        Projects
    </a>
    <a href="<?php echo e(route('events.index')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('events.*') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        Events
    </a>
    <a href="<?php echo e(route('gallery.index')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('gallery.*') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        Gallery
    </a>
    <a href="<?php echo e(route('contact')); ?>" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold <?php echo e(request()->routeIs('contact') ? 'text-green-600' : ''); ?> transition-colors duration-300">
        Contact
    </a>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/partials/navigation.blade.php ENDPATH**/ ?>