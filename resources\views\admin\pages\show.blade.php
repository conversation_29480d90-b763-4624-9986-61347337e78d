@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">{{ $page->title }}</h1>
            <div class="flex space-x-2">
                <a href="{{ route('pages.show', $page->slug) }}" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    View Live Page
                </a>
                <a href="{{ route('admin.pages.edit', $page) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Page
                </a>
                <a href="{{ route('admin.pages.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Pages
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Featured Image -->
                @if($page->featured_image)
                    <div class="mb-6">
                        <img src="{{ asset('storage/' . $page->featured_image) }}" alt="{{ $page->title }}" class="w-full h-64 object-cover rounded-lg">
                    </div>
                @endif

                <!-- Content -->
                <div class="prose prose-lg max-w-none">
                    {!! $page->content !!}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Page Status -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Page Status</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Status:</span>
                            @if($page->is_published)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Published
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    Draft
                                </span>
                            @endif
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Sort Order:</span>
                            <span class="text-sm text-gray-900">{{ $page->sort_order }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Slug:</span>
                            <span class="text-sm text-gray-900 font-mono">{{ $page->slug }}</span>
                        </div>
                    </div>
                </div>

                <!-- Page Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Page Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Created:</span>
                            <div class="text-gray-900">{{ $page->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $page->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">URL:</span>
                            <div class="text-blue-600 hover:text-blue-800">
                                <a href="{{ route('pages.show', $page->slug) }}" target="_blank">/{{ $page->slug }}</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Information -->
                @if($page->meta_title || $page->meta_description || $page->meta_keywords)
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Information</h3>
                    <div class="space-y-3 text-sm">
                        @if($page->meta_title)
                        <div>
                            <span class="text-gray-600">Meta Title:</span>
                            <div class="text-gray-900">{{ $page->meta_title }}</div>
                        </div>
                        @endif
                        
                        @if($page->meta_description)
                        <div>
                            <span class="text-gray-600">Meta Description:</span>
                            <div class="text-gray-900">{{ $page->meta_description }}</div>
                        </div>
                        @endif
                        
                        @if($page->meta_keywords)
                        <div>
                            <span class="text-gray-600">Meta Keywords:</span>
                            <div class="text-gray-900">{{ $page->meta_keywords }}</div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.pages.edit', $page) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Page
                        </a>
                        @if($page->slug !== 'home')
                            <form action="{{ route('admin.pages.destroy', $page) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this page? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    Delete Page
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
