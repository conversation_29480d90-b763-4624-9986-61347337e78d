/**
 * Admin Tables AJAX Pagination and Per-Page Management
 * Handles pagination and records per page without page reloads
 */

class AdminTableManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupPaginationHandlers();
        this.setupPerPageHandlers();
        this.setupFilterHandlers();
    }

    /**
     * Setup AJAX pagination handlers
     */
    setupPaginationHandlers() {
        // Handle pagination links
        document.addEventListener('click', (e) => {
            const paginationLink = e.target.closest('.pagination a');
            if (paginationLink && !paginationLink.classList.contains('disabled')) {
                e.preventDefault();
                this.loadPage(paginationLink.href);
            }
        });
    }

    /**
     * Setup per-page selection handlers
     */
    setupPerPageHandlers() {
        // Handle per-page dropdown changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.per-page-select')) {
                e.preventDefault();
                this.changePerPage(e.target.value);
            }
        });
    }

    /**
     * Setup filter handlers
     */
    setupFilterHandlers() {
        // Handle filter form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.filter-form')) {
                e.preventDefault();
                this.applyFilters(e.target);
            }
        });

        // Handle filter input changes (with debounce)
        let filterTimeout;
        document.addEventListener('input', (e) => {
            if (e.target.matches('.filter-input')) {
                clearTimeout(filterTimeout);
                filterTimeout = setTimeout(() => {
                    this.applyFilters(e.target.closest('form'));
                }, 500);
            }
        });
    }

    /**
     * Load a specific page via AJAX
     */
    async loadPage(url) {
        try {
            this.showLoading();
            
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html'
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const html = await response.text();
            this.updateTableContent(html);
            this.updateUrl(url);
            
        } catch (error) {
            console.error('Error loading page:', error);
            this.showError('Failed to load page. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Change records per page
     */
    async changePerPage(perPage) {
        try {
            this.showLoading();
            
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('per_page', perPage);
            currentUrl.searchParams.delete('page'); // Reset to first page
            
            await this.loadPage(currentUrl.toString());
            
        } catch (error) {
            console.error('Error changing per page:', error);
            this.showError('Failed to update records per page. Please try again.');
        }
    }

    /**
     * Apply filters
     */
    async applyFilters(form) {
        try {
            this.showLoading();
            
            const formData = new FormData(form);
            const currentUrl = new URL(window.location);
            
            // Clear existing filter parameters
            const filterParams = ['search', 'status', 'category', 'service', 'type', 'department'];
            filterParams.forEach(param => currentUrl.searchParams.delete(param));
            
            // Add new filter parameters
            for (const [key, value] of formData.entries()) {
                if (value) {
                    currentUrl.searchParams.set(key, value);
                }
            }
            
            currentUrl.searchParams.delete('page'); // Reset to first page
            
            await this.loadPage(currentUrl.toString());
            
        } catch (error) {
            console.error('Error applying filters:', error);
            this.showError('Failed to apply filters. Please try again.');
        }
    }

    /**
     * Update table content with new HTML
     */
    updateTableContent(html) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        // Find the main content container
        const newContent = doc.querySelector('.table-container, .admin-content, main');
        const currentContent = document.querySelector('.table-container, .admin-content, main');
        
        if (newContent && currentContent) {
            currentContent.innerHTML = newContent.innerHTML;
        } else {
            // Fallback: update the entire body content
            const newBody = doc.querySelector('body');
            if (newBody) {
                document.body.innerHTML = newBody.innerHTML;
            }
        }
        
        // Reinitialize any JavaScript components if needed
        this.reinitializeComponents();
    }

    /**
     * Update browser URL without page reload
     */
    updateUrl(url) {
        if (window.history && window.history.pushState) {
            window.history.pushState({}, '', url);
        }
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        // Remove existing loading indicators
        this.hideLoading();
        
        // Create loading overlay
        const loading = document.createElement('div');
        loading.className = 'admin-table-loading';
        loading.innerHTML = `
            <div class="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 shadow-lg">
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                        <span class="text-gray-700">Loading...</span>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(loading);
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        const loading = document.querySelector('.admin-table-loading');
        if (loading) {
            loading.remove();
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create error notification
        const error = document.createElement('div');
        error.className = 'admin-table-error';
        error.innerHTML = `
            <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            </div>
        `;
        
        document.body.appendChild(error);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            error.remove();
        }, 5000);
    }

    /**
     * Reinitialize components after content update
     */
    reinitializeComponents() {
        // Reinitialize any tooltips, dropdowns, etc.
        // This can be extended based on what components are used
        
        // Example: Reinitialize tooltips if using a tooltip library
        // if (window.tippy) {
        //     tippy('[data-tippy-content]');
        // }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminTableManager();
});

// Handle browser back/forward buttons
window.addEventListener('popstate', () => {
    window.location.reload();
});
