@extends('layouts.public')

@section('title', $event->meta_title ?: $event->title)
@section('meta_description', $event->meta_description ?: $event->description)
@section('meta_keywords', $event->meta_keywords)

@section('content')
<!-- Event Hero Section -->
<section class="relative">
    @if($event->featured_image)
        <div class="h-96 bg-cover bg-center relative" style="background-image: url('{{ asset('storage/' . $event->featured_image) }}')">
            <div class="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
    @else
        <div class="h-96 bg-gradient-to-r from-green-600 to-green-800 relative">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>
    @endif
    
    <div class="absolute inset-0 flex items-center">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div class="text-white">
                <div class="flex items-center text-sm mb-4">
                    <a href="{{ route('events.index') }}" class="hover:text-green-300 transition-colors">Events</a>
                    <i class="fas fa-chevron-right mx-2"></i>
                    <span>{{ $event->title }}</span>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold mb-4">{{ $event->title }}</h1>
                
                <div class="flex flex-wrap items-center gap-6 text-lg">
                    <div class="flex items-center">
                        <i class="fas fa-calendar mr-2"></i>
                        {{ $event->formatted_event_date }}
                    </div>
                    
                    @if($event->location)
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            {{ $event->location }}
                        </div>
                    @endif
                    
                    @if($event->price)
                        <div class="flex items-center">
                            <i class="fas fa-tag mr-2"></i>
                            ${{ number_format($event->price, 2) }}
                        </div>
                    @else
                        <div class="flex items-center">
                            <i class="fas fa-tag mr-2"></i>
                            Free Event
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Event Content -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Event Status -->
                @if($event->status == 'cancelled')
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <strong>This event has been cancelled.</strong>
                        </div>
                    </div>
                @elseif($event->is_past)
                    <div class="bg-gray-100 border border-gray-400 text-gray-700 px-4 py-3 rounded mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <strong>This event has already taken place.</strong>
                        </div>
                    </div>
                @elseif($event->max_attendees && $event->is_full)
                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-users mr-2"></i>
                            <strong>This event is fully booked.</strong>
                        </div>
                    </div>
                @endif

                <!-- Event Description -->
                @if($event->description)
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Event</h2>
                        <p class="text-lg text-gray-700 leading-relaxed">{{ $event->description }}</p>
                    </div>
                @endif

                <!-- Event Content -->
                @if($event->content)
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Event Details</h2>
                        <div class="prose prose-lg max-w-none text-gray-700">
                            {!! $event->content !!}
                        </div>
                    </div>
                @endif

                <!-- Organizer Information -->
                @if($event->organizer_info && count(array_filter($event->organizer_info)))
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Organizer</h2>
                        <div class="bg-gray-50 rounded-lg p-6">
                            @if($event->organizer_info['name'] ?? null)
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $event->organizer_info['name'] }}</h3>
                            @endif
                            
                            <div class="space-y-2">
                                @if($event->organizer_info['email'] ?? null)
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-envelope mr-3 w-4"></i>
                                        <a href="mailto:{{ $event->organizer_info['email'] }}" class="hover:text-green-600 transition-colors">
                                            {{ $event->organizer_info['email'] }}
                                        </a>
                                    </div>
                                @endif
                                
                                @if($event->organizer_info['phone'] ?? null)
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-phone mr-3 w-4"></i>
                                        <a href="tel:{{ $event->organizer_info['phone'] }}" class="hover:text-green-600 transition-colors">
                                            {{ $event->organizer_info['phone'] }}
                                        </a>
                                    </div>
                                @endif
                                
                                @if($event->organizer_info['website'] ?? null)
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-globe mr-3 w-4"></i>
                                        <a href="{{ $event->organizer_info['website'] }}" target="_blank" class="hover:text-green-600 transition-colors">
                                            Visit Website <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Share Section -->
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Share This Event</h2>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                           target="_blank" 
                           class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f mr-2"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($event->title) }}" 
                           target="_blank" 
                           class="bg-blue-400 text-white px-4 py-2 rounded-md hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter mr-2"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" 
                           target="_blank" 
                           class="bg-blue-800 text-white px-4 py-2 rounded-md hover:bg-blue-900 transition-colors">
                            <i class="fab fa-linkedin-in mr-2"></i>LinkedIn
                        </a>
                        <button onclick="copyToClipboard('{{ request()->url() }}')" 
                                class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                            <i class="fas fa-link mr-2"></i>Copy Link
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Event Info Card -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8 sticky top-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Event Information</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-calendar text-green-600 mr-3 mt-1"></i>
                            <div>
                                <div class="font-medium text-gray-900">Date & Time</div>
                                <div class="text-gray-600">{{ $event->formatted_event_date }}</div>
                                @if($event->event_end_date)
                                    <div class="text-gray-600">to {{ $event->event_end_date->format('M d, Y g:i A') }}</div>
                                @endif
                            </div>
                        </div>
                        
                        @if($event->location)
                            <div class="flex items-start">
                                <i class="fas fa-map-marker-alt text-green-600 mr-3 mt-1"></i>
                                <div>
                                    <div class="font-medium text-gray-900">Location</div>
                                    <div class="text-gray-600">{{ $event->location }}</div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="flex items-start">
                            <i class="fas fa-tag text-green-600 mr-3 mt-1"></i>
                            <div>
                                <div class="font-medium text-gray-900">Price</div>
                                <div class="text-gray-600">
                                    @if($event->price)
                                        ${{ number_format($event->price, 2) }}
                                    @else
                                        Free
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        @if($event->max_attendees)
                            <div class="flex items-start">
                                <i class="fas fa-users text-green-600 mr-3 mt-1"></i>
                                <div>
                                    <div class="font-medium text-gray-900">Capacity</div>
                                    <div class="text-gray-600">
                                        {{ $event->current_attendees }} / {{ $event->max_attendees }} attendees
                                        @if($event->available_spots)
                                            <br><span class="text-green-600">{{ $event->available_spots }} spots available</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    @if($event->event_url && $event->is_upcoming && !$event->is_full)
                        <div class="mt-6">
                            <a href="{{ $event->event_url }}" target="_blank" 
                               class="w-full bg-green-600 text-white text-center py-3 px-4 rounded-md hover:bg-green-700 transition-colors block">
                                <i class="fas fa-external-link-alt mr-2"></i>Register Now
                            </a>
                        </div>
                    @endif
                </div>

                <!-- Upcoming Events -->
                @if($upcomingEvents->count() > 0)
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Upcoming Events</h3>
                        <div class="space-y-4">
                            @foreach($upcomingEvents as $upcomingEvent)
                                <div class="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                                    <h4 class="font-medium text-gray-900 mb-1">
                                        <a href="{{ route('events.show', $upcomingEvent) }}" class="hover:text-green-600 transition-colors">
                                            {{ Str::limit($upcomingEvent->title, 40) }}
                                        </a>
                                    </h4>
                                    <div class="text-sm text-gray-600">
                                        <i class="fas fa-calendar mr-1"></i>
                                        {{ $upcomingEvent->formatted_event_date_short }}
                                    </div>
                                    @if($upcomingEvent->location)
                                        <div class="text-sm text-gray-600">
                                            <i class="fas fa-map-marker-alt mr-1"></i>
                                            {{ Str::limit($upcomingEvent->location, 30) }}
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-4">
                            <a href="{{ route('events.index') }}" class="text-green-600 hover:text-green-700 font-medium">
                                View All Events <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Related Events -->
@if($relatedEvents->count() > 0)
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Related Events</h2>
            <p class="text-lg text-gray-600">You might also be interested in these events</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($relatedEvents as $relatedEvent)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    @if($relatedEvent->featured_image)
                        <div class="h-48 bg-cover bg-center" style="background-image: url('{{ asset('storage/' . $relatedEvent->featured_image) }}')"></div>
                    @else
                        <div class="h-48 bg-gradient-to-r from-gray-400 to-gray-600 flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-white text-3xl"></i>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 mb-2">
                            <i class="fas fa-calendar mr-2"></i>
                            {{ $relatedEvent->formatted_event_date_short }}
                        </div>
                        
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <a href="{{ route('events.show', $relatedEvent) }}" class="hover:text-green-600 transition-colors">
                                {{ $relatedEvent->title }}
                            </a>
                        </h3>
                        
                        @if($relatedEvent->description)
                            <p class="text-gray-600 mb-4">{{ Str::limit($relatedEvent->description, 100) }}</p>
                        @endif
                        
                        <a href="{{ route('events.show', $relatedEvent) }}" class="text-green-600 hover:text-green-700 font-medium">
                            Learn More <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
        button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
        button.classList.add('bg-green-600', 'hover:bg-green-700');
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-gray-600', 'hover:bg-gray-700');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endpush
@endsection
