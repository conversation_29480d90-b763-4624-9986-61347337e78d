@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Edit Event: {{ $event->title }}</h1>
            <div class="flex gap-3">
                <a href="{{ route('admin.events.show', $event) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    👁️ View Event
                </a>
                <a href="{{ route('admin.events.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Back to Events
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Event Details</h3>
                    </div>
                    <div class="p-6">
                    <form id="event-form" method="POST" action="{{ route('admin.events.update', $event) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Title -->
                        <div class="form-group">
                            <label for="title">Event Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $event->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                   id="slug" name="slug" value="{{ old('slug', $event->slug) }}">
                            <small class="form-text text-muted">Leave empty to auto-generate from title</small>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description">Short Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $event->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div class="form-group">
                            <label for="content">Full Content</label>
                            <textarea class="form-control tinymce @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="10">{{ old('content', $event->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Event Dates -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="event_date">Event Start Date & Time <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control @error('event_date') is-invalid @enderror" 
                                           id="event_date" name="event_date" 
                                           value="{{ old('event_date', $event->event_date ? $event->event_date->format('Y-m-d\TH:i') : '') }}" required>
                                    @error('event_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="event_end_date">Event End Date & Time</label>
                                    <input type="datetime-local" class="form-control @error('event_end_date') is-invalid @enderror" 
                                           id="event_end_date" name="event_end_date" 
                                           value="{{ old('event_end_date', $event->event_end_date ? $event->event_end_date->format('Y-m-d\TH:i') : '') }}">
                                    @error('event_end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                   id="location" name="location" value="{{ old('location', $event->location) }}">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Event URL -->
                        <div class="form-group">
                            <label for="event_url">External Event URL</label>
                            <input type="url" class="form-control @error('event_url') is-invalid @enderror" 
                                   id="event_url" name="event_url" value="{{ old('event_url', $event->event_url) }}">
                            <small class="form-text text-muted">Link to external event page or registration</small>
                            @error('event_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Price and Attendees -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="price">Event Price</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" 
                                               id="price" name="price" value="{{ old('price', $event->price) }}">
                                    </div>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="max_attendees">Maximum Attendees</label>
                                    <input type="number" class="form-control @error('max_attendees') is-invalid @enderror" 
                                           id="max_attendees" name="max_attendees" value="{{ old('max_attendees', $event->max_attendees) }}">
                                    @error('max_attendees')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="current_attendees">Current Attendees</label>
                                    <input type="number" class="form-control @error('current_attendees') is-invalid @enderror" 
                                           id="current_attendees" name="current_attendees" value="{{ old('current_attendees', $event->current_attendees) }}">
                                    @error('current_attendees')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Organizer Information -->
                        <div class="form-group">
                            <label>Organizer Information</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" name="organizer_info[name]" 
                                           placeholder="Organizer Name" value="{{ old('organizer_info.name', $event->organizer_info['name'] ?? '') }}">
                                </div>
                                <div class="col-md-6">
                                    <input type="email" class="form-control" name="organizer_info[email]" 
                                           placeholder="Organizer Email" value="{{ old('organizer_info.email', $event->organizer_info['email'] ?? '') }}">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <input type="tel" class="form-control" name="organizer_info[phone]" 
                                           placeholder="Organizer Phone" value="{{ old('organizer_info.phone', $event->organizer_info['phone'] ?? '') }}">
                                </div>
                                <div class="col-md-6">
                                    <input type="url" class="form-control" name="organizer_info[website]" 
                                           placeholder="Organizer Website" value="{{ old('organizer_info.website', $event->organizer_info['website'] ?? '') }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Event
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Publishing Options -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Publishing Options</h6>
                </div>
                <div class="card-body">
                    <!-- Status -->
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" form="event-form">
                            <option value="draft" {{ old('status', $event->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="published" {{ old('status', $event->status) == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="cancelled" {{ old('status', $event->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Featured -->
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="is_featured" 
                                   name="is_featured" value="1" {{ old('is_featured', $event->is_featured) ? 'checked' : '' }} form="event-form">
                            <label class="custom-control-label" for="is_featured">Featured Event</label>
                        </div>
                    </div>

                    <!-- Sort Order -->
                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $event->sort_order) }}" form="event-form">
                        @error('sort_order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                    @if($event->featured_image)
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeImage()">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    @endif
                </div>
                <div class="card-body">
                    @if($event->featured_image)
                        <div id="current-image" class="mb-3">
                            <img src="{{ asset('storage/' . $event->featured_image) }}" alt="{{ $event->title }}" class="img-fluid rounded">
                        </div>
                    @endif
                    
                    <div class="form-group">
                        <input type="file" class="form-control-file @error('featured_image') is-invalid @enderror" 
                               id="featured_image" name="featured_image" accept="image/*" form="event-form">
                        <small class="form-text text-muted">Recommended size: 800x600px</small>
                        @error('featured_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div id="image-preview" class="mt-3" style="display: none;">
                        <img id="preview-img" src="" alt="Preview" class="img-fluid rounded">
                    </div>
                </div>
            </div>

            <!-- SEO Meta -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">SEO Meta</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="meta_title">Meta Title</label>
                        <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                               id="meta_title" name="meta_title" value="{{ old('meta_title', $event->meta_title) }}" form="event-form">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="meta_description">Meta Description</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                  id="meta_description" name="meta_description" rows="3" form="event-form">{{ old('meta_description', $event->meta_description) }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="meta_keywords">Meta Keywords</label>
                        <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror" 
                               id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords', $event->meta_keywords) }}" form="event-form">
                        <small class="form-text text-muted">Separate keywords with commas</small>
                        @error('meta_keywords')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from title
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    
    titleInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.value === slugInput.defaultValue) {
            const slug = this.value.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
        }
    });
    
    // Image preview
    const imageInput = document.getElementById('featured_image');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    const currentImage = document.getElementById('current-image');
    
    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
                if (currentImage) {
                    currentImage.style.display = 'none';
                }
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
            if (currentImage) {
                currentImage.style.display = 'block';
            }
        }
    });
});

function removeImage() {
    if (confirm('Are you sure you want to remove the featured image?')) {
        fetch('{{ route("admin.events.remove-image", $event) }}', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('current-image').remove();
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing image');
        });
    }
}
</script>
@endpush
@endsection
