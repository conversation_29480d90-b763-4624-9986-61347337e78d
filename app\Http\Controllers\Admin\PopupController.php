<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Popup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PopupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $popups = Popup::orderBy('created_at', 'desc')->paginate(15);
        return view('admin.popups.index', compact('popups'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $triggerTypes = Popup::getTriggerTypes();
        $availablePages = Popup::getAvailablePages();
        return view('admin.popups.create', compact('triggerTypes', 'availablePages'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cta_text' => 'nullable|string|max:100',
            'cta_url' => 'nullable|url|max:255',
            'trigger_type' => 'required|in:on_load,on_scroll,on_delay',
            'trigger_value' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'display_pages' => 'nullable|array',
            'display_frequency' => 'required|integer|min:1',
        ]);

        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('popups', 'public');
        }

        Popup::create($validated);

        return redirect()->route('admin.popups.index')->with('success', 'Popup created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Popup $popup)
    {
        return view('admin.popups.show', compact('popup'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Popup $popup)
    {
        $triggerTypes = Popup::getTriggerTypes();
        $availablePages = Popup::getAvailablePages();
        return view('admin.popups.edit', compact('popup', 'triggerTypes', 'availablePages'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Popup $popup)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cta_text' => 'nullable|string|max:100',
            'cta_url' => 'nullable|url|max:255',
            'trigger_type' => 'required|in:on_load,on_scroll,on_delay',
            'trigger_value' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'display_pages' => 'nullable|array',
            'display_frequency' => 'required|integer|min:1',
        ]);

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($popup->image) {
                Storage::disk('public')->delete($popup->image);
            }
            $validated['image'] = $request->file('image')->store('popups', 'public');
        }

        $popup->update($validated);

        return redirect()->route('admin.popups.index')->with('success', 'Popup updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Popup $popup)
    {
        // Delete image if exists
        if ($popup->image) {
            Storage::disk('public')->delete($popup->image);
        }

        $popup->delete();

        return redirect()->route('admin.popups.index')->with('success', 'Popup deleted successfully.');
    }

    /**
     * Toggle popup status
     */
    public function toggleStatus(Popup $popup)
    {
        $popup->update(['is_active' => !$popup->is_active]);

        $status = $popup->is_active ? 'activated' : 'deactivated';
        return redirect()->route('admin.popups.index')->with('success', "Popup {$status} successfully.");
    }
}
