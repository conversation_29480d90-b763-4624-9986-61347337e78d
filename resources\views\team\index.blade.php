@extends('layouts.public')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Our Team</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Meet the dedicated professionals who drive our success and deliver exceptional results for our clients. Our diverse team brings together expertise, innovation, and passion.
            </p>
        </div>

        <!-- Team Navigation -->
        <div class="flex justify-center space-x-4 mb-8" data-aos="fade-up" data-aos-delay="200">
            <a href="{{ route('team.executive') }}" class="bg-green-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-green-700 transition-all duration-300">
                Executive Team
            </a>
            <a href="{{ route('team.project') }}" class="bg-white text-green-600 border-2 border-green-600 px-6 py-3 rounded-full font-semibold hover:bg-green-600 hover:text-white transition-all duration-300">
                Project Team
            </a>
        </div>
    </div>
</section>

<!-- Executive Team Section -->
@if($executiveTeam->count() > 0)
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Executive Management</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our leadership team brings decades of combined experience in business strategy, operations, and innovation.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($executiveTeam as $index => $member)
                <div class="bg-white rounded-lg shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($member->image_url)
                        <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-32 h-32 rounded-full mx-auto mb-6 object-cover shadow-lg">
                    @else
                        <div class="w-32 h-32 rounded-full mx-auto mb-6 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                            <span class="text-4xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                        </div>
                    @endif
                    
                    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $member->name }}</h3>
                    <p class="text-green-600 font-semibold mb-4">{{ $member->position }}</p>
                    
                    @if($member->bio)
                        <p class="text-gray-600 mb-6 leading-relaxed">{{ $member->bio }}</p>
                    @endif
                    
                    @if($member->linkedin || $member->twitter || $member->email)
                        <div class="flex justify-center space-x-4 mb-4">
                            @if($member->email)
                                <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fas fa-envelope text-lg"></i>
                                </a>
                            @endif
                            @if($member->linkedin)
                                <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fab fa-linkedin text-lg"></i>
                                </a>
                            @endif
                            @if($member->twitter)
                                <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fab fa-twitter text-lg"></i>
                                </a>
                            @endif
                        </div>
                    @endif
                    
                    <a href="{{ route('team.show', $member) }}" class="inline-block bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold hover:bg-green-700 transition-colors duration-300">
                        View Profile
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Project Team Section -->
@if($projectTeam->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Project Management</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our skilled project managers and specialists ensure seamless execution and delivery of every project.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($projectTeam as $index => $member)
                <div class="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($member->image_url)
                        <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-24 h-24 rounded-full mx-auto mb-4 object-cover shadow-lg">
                    @else
                        <div class="w-24 h-24 rounded-full mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                            <span class="text-xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                        </div>
                    @endif
                    
                    <h3 class="text-lg font-bold text-gray-900 mb-2">{{ $member->name }}</h3>
                    <p class="text-green-600 font-semibold text-sm mb-4">{{ $member->position }}</p>
                    
                    @if($member->linkedin || $member->twitter || $member->email)
                        <div class="flex justify-center space-x-3 mb-4">
                            @if($member->email)
                                <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            @endif
                            @if($member->linkedin)
                                <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                            @endif
                            @if($member->twitter)
                                <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            @endif
                        </div>
                    @endif
                    
                    <a href="{{ route('team.show', $member) }}" class="inline-block bg-green-600 text-white px-3 py-1 rounded-full text-xs font-semibold hover:bg-green-700 transition-colors duration-300">
                        View Profile
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Call to Action -->
<section class="py-20 bg-green-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div data-aos="fade-up">
            <h2 class="text-4xl font-bold text-white mb-4">Ready to Work With Us?</h2>
            <p class="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
                Our team is ready to help you achieve your business goals. Get in touch to discuss your next project.
            </p>
            <a href="{{ route('contact') }}" class="bg-white text-green-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 hover-scale shadow-lg">
                Contact Our Team
            </a>
        </div>
    </div>
</section>
@endsection

@push('styles')
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
@endpush

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
@endpush
