<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\GalleryImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class GalleryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Gallery::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'unpublished') {
                $query->where('is_published', false);
            }
        }

        // Filter by featured
        if ($request->filled('featured') && $request->featured == '1') {
            $query->featured();
        }

        $galleries = $query->withCount('images')->ordered()->paginate(12);
        $categories = Gallery::getAvailableCategories();

        return view('admin.galleries.index', compact('galleries', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Gallery::getAvailableCategories();
        return view('admin.galleries.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:galleries,slug',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:255',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'sort_order' => 'nullable|integer',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB per image
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('galleries/covers', 'public');
        }

        // Set default values
        $validated['is_featured'] = $request->has('is_featured');
        $validated['is_published'] = $request->has('is_published');
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $gallery = Gallery::create($validated);

        // Handle multiple image uploads
        if ($request->hasFile('images')) {
            $this->uploadGalleryImages($gallery, $request->file('images'));
        }

        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Gallery $gallery)
    {
        $gallery->load(['images' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        return view('admin.galleries.show', compact('gallery'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gallery $gallery)
    {
        $categories = Gallery::getAvailableCategories();
        $gallery->load(['images' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        return view('admin.galleries.edit', compact('gallery', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Gallery $gallery)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:galleries,slug,' . $gallery->id,
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:255',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'sort_order' => 'nullable|integer',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB per image
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            // Delete old cover image
            if ($gallery->cover_image) {
                Storage::disk('public')->delete($gallery->cover_image);
            }
            $validated['cover_image'] = $request->file('cover_image')->store('galleries/covers', 'public');
        }

        // Set default values
        $validated['is_featured'] = $request->has('is_featured');
        $validated['is_published'] = $request->has('is_published');
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $gallery->update($validated);

        // Handle additional image uploads
        if ($request->hasFile('images')) {
            $this->uploadGalleryImages($gallery, $request->file('images'));
        }

        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gallery $gallery)
    {
        // Delete cover image
        if ($gallery->cover_image) {
            Storage::disk('public')->delete($gallery->cover_image);
        }

        // Delete all gallery images
        foreach ($gallery->images as $image) {
            if ($image->image_path) {
                Storage::disk('public')->delete($image->image_path);
            }
            if ($image->thumbnail_path) {
                Storage::disk('public')->delete($image->thumbnail_path);
            }
        }

        $gallery->delete();

        return redirect()->route('admin.galleries.index')
            ->with('success', 'Gallery deleted successfully.');
    }

    /**
     * Remove the cover image from a gallery
     */
    public function removeCoverImage(Gallery $gallery)
    {
        if ($gallery->cover_image) {
            Storage::disk('public')->delete($gallery->cover_image);
            $gallery->update(['cover_image' => null]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Upload multiple images to a gallery
     */
    private function uploadGalleryImages(Gallery $gallery, array $images)
    {
        $maxSortOrder = $gallery->images()->max('sort_order') ?? 0;

        foreach ($images as $index => $image) {
            $imagePath = $image->store('galleries/' . $gallery->id, 'public');

            // Get image dimensions
            $fullPath = storage_path('app/public/' . $imagePath);
            $imageSize = getimagesize($fullPath);

            // Create thumbnail
            $thumbnailPath = $this->createThumbnail($imagePath);

            GalleryImage::create([
                'gallery_id' => $gallery->id,
                'title' => pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME),
                'image_path' => $imagePath,
                'thumbnail_path' => $thumbnailPath,
                'alt_text' => pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME),
                'file_size' => $image->getSize(),
                'mime_type' => $image->getMimeType(),
                'width' => $imageSize[0] ?? null,
                'height' => $imageSize[1] ?? null,
                'sort_order' => $maxSortOrder + $index + 1,
            ]);
        }
    }

    /**
     * Create thumbnail for an image
     */
    private function createThumbnail($imagePath)
    {
        try {
            $fullPath = storage_path('app/public/' . $imagePath);
            $pathInfo = pathinfo($imagePath);
            $thumbnailDir = 'galleries/thumbs';
            $thumbnailPath = $thumbnailDir . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
            $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

            // Create thumbnail directory if it doesn't exist
            if (!Storage::disk('public')->exists($thumbnailDir)) {
                Storage::disk('public')->makeDirectory($thumbnailDir);
            }

            // Create thumbnail using basic PHP (since Intervention Image might not be available)
            $this->resizeImage($fullPath, $thumbnailFullPath, 300, 300);

            return $thumbnailPath;
        } catch (\Exception $e) {
            // If thumbnail creation fails, return null
            return null;
        }
    }

    /**
     * Simple image resize function
     */
    private function resizeImage($source, $destination, $maxWidth, $maxHeight)
    {
        list($origWidth, $origHeight, $type) = getimagesize($source);

        // Calculate new dimensions
        $ratio = min($maxWidth / $origWidth, $maxHeight / $origHeight);
        $newWidth = round($origWidth * $ratio);
        $newHeight = round($origHeight * $ratio);

        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);

        // Load source image
        switch ($type) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($source);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($source);
                imagealphablending($newImage, false);
                imagesavealpha($newImage, true);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($source);
                break;
            default:
                return false;
        }

        // Resize
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $origWidth, $origHeight);

        // Save
        switch ($type) {
            case IMAGETYPE_JPEG:
                imagejpeg($newImage, $destination, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($newImage, $destination);
                break;
            case IMAGETYPE_GIF:
                imagegif($newImage, $destination);
                break;
        }

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($newImage);

        return true;
    }

    /**
     * Bulk actions for galleries
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,publish,unpublish,feature,unfeature',
            'galleries' => 'required|array',
            'galleries.*' => 'exists:galleries,id'
        ]);

        $galleries = Gallery::whereIn('id', $request->galleries);

        switch ($request->action) {
            case 'delete':
                foreach ($galleries->get() as $gallery) {
                    if ($gallery->cover_image) {
                        Storage::disk('public')->delete($gallery->cover_image);
                    }
                    foreach ($gallery->images as $image) {
                        if ($image->image_path) {
                            Storage::disk('public')->delete($image->image_path);
                        }
                        if ($image->thumbnail_path) {
                            Storage::disk('public')->delete($image->thumbnail_path);
                        }
                    }
                }
                $galleries->delete();
                $message = 'Selected galleries deleted successfully.';
                break;
            case 'publish':
                $galleries->update(['is_published' => true]);
                $message = 'Selected galleries published successfully.';
                break;
            case 'unpublish':
                $galleries->update(['is_published' => false]);
                $message = 'Selected galleries unpublished successfully.';
                break;
            case 'feature':
                $galleries->update(['is_featured' => true]);
                $message = 'Selected galleries featured successfully.';
                break;
            case 'unfeature':
                $galleries->update(['is_featured' => false]);
                $message = 'Selected galleries unfeatured successfully.';
                break;
        }

        return redirect()->route('admin.galleries.index')
            ->with('success', $message);
    }
}
