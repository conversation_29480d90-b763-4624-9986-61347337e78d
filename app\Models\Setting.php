<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'validation_rules',
        'options',
        'is_public',
        'is_encrypted',
        'sort_order',
    ];

    protected $casts = [
        'validation_rules' => 'array',
        'options' => 'array',
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("setting.{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return $setting->getValue();
        });
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, string $type = 'string'): void
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
            ]
        );

        Cache::forget("setting.{$key}");
    }

    /**
     * Get multiple settings by group
     */
    public static function getGroup(string $group): array
    {
        return Cache::remember("settings.group.{$group}", 3600, function () use ($group) {
            $settings = static::where('group', $group)
                ->orderBy('sort_order')
                ->get();

            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key] = $setting->getValue();
            }

            return $result;
        });
    }

    /**
     * Get all public settings
     */
    public static function getPublic(): array
    {
        return Cache::remember('settings.public', 3600, function () {
            $settings = static::where('is_public', true)->get();

            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key] = $setting->getValue();
            }

            return $result;
        });
    }

    /**
     * Get the typed value of the setting
     */
    public function getValue()
    {
        $value = $this->value;

        // Decrypt if encrypted
        if ($this->is_encrypted && $value) {
            try {
                $value = Crypt::decryptString($value);
            } catch (\Exception $e) {
                return null;
            }
        }

        // Cast to appropriate type
        switch ($this->type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
            case 'array':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Set the value with proper type handling
     */
    public function setValue($value): void
    {
        // Convert value based on type
        switch ($this->type) {
            case 'boolean':
                $value = $value ? '1' : '0';
                break;
            case 'json':
            case 'array':
                $value = json_encode($value);
                break;
            default:
                $value = (string) $value;
        }

        // Encrypt if needed
        if ($this->is_encrypted) {
            $value = Crypt::encryptString($value);
        }

        $this->value = $value;
        $this->save();

        // Clear cache
        Cache::forget("setting.{$this->key}");
        Cache::forget("settings.group.{$this->group}");
        Cache::forget('settings.public');
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        Cache::flush();
    }

    /**
     * Get settings for admin panel
     */
    public static function getForAdmin(string $group = null): array
    {
        $query = static::orderBy('group')->orderBy('sort_order');

        if ($group) {
            $query->where('group', $group);
        }

        return $query->get()->groupBy('group')->toArray();
    }

    /**
     * Get available setting groups
     */
    public static function getGroups(): array
    {
        return static::distinct('group')->pluck('group')->toArray();
    }
}
