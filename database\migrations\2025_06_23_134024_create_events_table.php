<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->longText('content')->nullable();
            $table->dateTime('event_date');
            $table->dateTime('event_end_date')->nullable();
            $table->string('location')->nullable();
            $table->string('featured_image')->nullable();
            $table->enum('status', ['draft', 'published', 'cancelled'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('event_url')->nullable(); // External event URL if applicable
            $table->decimal('price', 10, 2)->nullable(); // Event price
            $table->integer('max_attendees')->nullable(); // Maximum number of attendees
            $table->integer('current_attendees')->default(0); // Current number of attendees
            $table->json('organizer_info')->nullable(); // Organizer contact information
            $table->timestamps();

            // Indexes
            $table->index(['status', 'event_date']);
            $table->index(['is_featured', 'event_date']);
            $table->index('event_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
