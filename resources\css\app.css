/* Import Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Fair Price Ventures */
.prose {
    @apply text-gray-700;
}

.prose h1 {
    @apply text-3xl font-bold text-gray-900 mb-6;
}

.prose h2 {
    @apply text-2xl font-bold text-gray-900 mb-4 mt-8;
}

.prose h3 {
    @apply text-xl font-semibold text-gray-900 mb-3 mt-6;
}

.prose p {
    @apply mb-4 leading-relaxed;
}

.prose ul {
    @apply mb-4 pl-6;
}

.prose li {
    @apply mb-2 list-disc;
}

.prose a {
    @apply text-green-600 hover:text-green-800 underline;
}

/* Authentication Form Enhancements */
.auth-form-container {
    @apply bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden;
}

.auth-form-header {
    @apply bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-center;
}

.auth-form-content {
    @apply px-8 py-8;
}

/* Enhanced Input Styling */
input[type="email"]:focus,
input[type="password"]:focus,
input[type="text"]:focus {
    @apply ring-2 ring-green-500 ring-opacity-20 border-green-500;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-in {
    animation: slideInFromTop 0.4s ease-out;
}

/* Button hover effects */
.btn-primary {
    @apply transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98];
}

/* Form field focus effects */
.form-field {
    @apply transition-all duration-200;
}

.form-field:focus-within {
    @apply transform scale-[1.01];
}

/* Custom gradient backgrounds */
.bg-gradient-green {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

/* Enhanced shadows */
.shadow-green {
    box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.1), 0 10px 10px -5px rgba(16, 185, 129, 0.04);
}

/* Glassmorphism effect */
.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
}

/* Loading spinner */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Custom shadows */
.shadow-green {
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
