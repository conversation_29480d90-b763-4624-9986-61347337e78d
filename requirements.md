
# Fair Price Ventures Ltd. — Website & Admin CMS Requirements

**Project Overview:**  
Build a modern, responsive Laravel + Livewire website for Fair Price Ventures Ltd., with a backend CMS for admins to manage website content, blogs, newsletters, popups, and contact enquiries.

## 🏗️ Tech Stack
- Laravel 10+
- Laravel Livewire
- Alpine.js
- Tailwind CSS
- MySQL or PostgreSQL
- Auth scaffolding (Laravereeze / Jetstream)
- Filament or Voyager (optional, for CMS scaffolding)
- Deployment-ready (Docker or Forge, optional)

## 🌐 Public Website Features
### 1️⃣ Home / Landing Page
- Hero section with tagline + CTA buttons
- Featured services or products grid
- Newsletter signup popup (configurable in admin)
- Testimonials or client logos
- Latest 3 blog posts preview
- Contact form (Livewire-powered)

### 2️⃣ About Page
- Company story, mission, values
- Team profiles (optional)

### 3️⃣ Services / Solutions Page
- List of services
- Individual service detail pages

### 4️⃣ Blog
- Blog list view with pagination, search, filter by category/tag
- Single post page with optional comments
- SEO-friendly URLs and meta tags

### 5️⃣ Contact Page
- Contact form (Livewire-powered)
- Google Maps embed (optional)
- Company details + contact info

### 6️⃣ Popup Management
- Display newsletter or promotion popups on the landing page
- Configurable: on load, on scroll, delayed trigger

## 🛠️ Admin CMS Features
### 🔐 Authentication
- Admin login system
- Role-based permissions (Admin, Editor)

### 📊 Dashboard
- Overview stats: total blog posts, newsletter signups, enquiries
- Latest activity log

### 📝 Pages Management
- CRUD for main pages (Home, About, Services)
- WYSIWYG editor (Trix/TinyMCE)

### 📰 Blog Management
- CRUD for blog posts
- Categories + tags management
- Featured image upload
- SEO meta fields

### ✉️ Newsletter Management
- View subscribers
- Export emails (CSV)
- Integration with Mailgun/SendGrid (optional)

### 📢 Popup Management
- Create/manage popup content (text, image, CTA)
- Define display rules (on load, on scroll, on delay)

### 📬 Contact Enquiries
- View submissions from contact form
- Mark as read/unread
- Export to CSV

### 👥 User Management
- CRUD admin users
- Assign roles + permissions

## ⚙️ Backend Functional Requirements
- Laravel migrations + seeders for all entities
- Eloquent models + relationships
- Livewire components for interactive sections (forms, popups)
- Validation rules for all forms
- Email notifications for contact form submissions (to admin)

## 🎨 UI/UX Requirements
- Modern, clean, responsive design
- Color palette aligned with company logo (greens, neutral tones)
- Tailwind CSS styling
- Smooth animations for popups + interactions (Livewire/Alpine)
- Accessible (WCAG-compliant)
- Optional: Dark mode toggle

## 🗃️ Database Entities
- Users (admin)
- Pages
- Services
- Blog posts
- Categories
- Tags
- Newsletter subscribers
- Popups
- Contact enquiries

## 📦 Deliverables
✅ Complete Laravel + Livewire project  
✅ Admin CMS panel with user guide  
✅ Frontend & backend fully functional  
✅ Deployment scripts (optional)  
✅ Source code in Git repository

## 🛡️ Security & Best Practices
- CSRF + XSS protection
- Secure password storage (bcrypt)
- Rate limiting for forms
- Proper file upload validation
- Regular Laravel security updates

## 🚀 Optional Integrations
- Mailgun/SendGrid for newsletters
- Google Analytics or Plausible Analytics
- Disqus or custom comments system on blog
