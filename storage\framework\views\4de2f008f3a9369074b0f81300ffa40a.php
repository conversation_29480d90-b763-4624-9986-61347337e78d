<?php $__env->startSection('title', 'System Requirements'); ?>
<?php $__env->startSection('step', '2'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-white rounded-lg shadow-lg p-8">
    <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <i class="fas fa-check-circle text-blue-600 text-2xl"></i>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">System Requirements Check</h1>
        <p class="text-gray-600">
            We're checking if your server meets the minimum requirements to run this application.
        </p>
    </div>

    <div class="space-y-4 mb-8">
        <?php
            $allPassed = true;
            $requiredFailed = false;
        ?>

        <?php $__currentLoopData = $requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                if ($requirement['required'] && !$requirement['status']) {
                    $allPassed = false;
                    $requiredFailed = true;
                }
            ?>
            
            <div class="flex items-center justify-between p-4 border rounded-lg <?php echo e($requirement['status'] ? 'border-green-200 bg-green-50' : ($requirement['required'] ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50')); ?>">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        <?php if($requirement['status']): ?>
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        <?php else: ?>
                            <?php if($requirement['required']): ?>
                                <i class="fas fa-times-circle text-red-500 text-xl"></i>
                            <?php else: ?>
                                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="font-medium <?php echo e($requirement['status'] ? 'text-green-800' : ($requirement['required'] ? 'text-red-800' : 'text-yellow-800')); ?>">
                            <?php echo e($requirement['name']); ?>

                        </h3>
                        <?php if(isset($requirement['current'])): ?>
                            <p class="text-sm <?php echo e($requirement['status'] ? 'text-green-600' : ($requirement['required'] ? 'text-red-600' : 'text-yellow-600')); ?>">
                                Current: <?php echo e($requirement['current']); ?>

                            </p>
                        <?php endif; ?>
                        <?php if(isset($requirement['note'])): ?>
                            <p class="text-sm <?php echo e($requirement['status'] ? 'text-green-600' : ($requirement['required'] ? 'text-red-600' : 'text-yellow-600')); ?>">
                                <?php echo e($requirement['note']); ?>

                            </p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <?php if($requirement['required']): ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($requirement['status'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                            Required
                        </span>
                    <?php else: ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($requirement['status'] ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                            Optional
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <?php if($requiredFailed): ?>
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-red-800 mb-2">Requirements Not Met</h3>
                    <div class="text-red-700">
                        <p class="mb-2">
                            Some required system requirements are not met. Please contact your hosting provider 
                            or system administrator to resolve these issues before proceeding.
                        </p>
                        <p class="text-sm">
                            <strong>Common solutions:</strong>
                        </p>
                        <ul class="text-sm list-disc list-inside mt-1 space-y-1">
                            <li>Update PHP to version 8.1 or higher</li>
                            <li>Install missing PHP extensions through your hosting control panel</li>
                            <li>Contact your hosting provider for assistance</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php elseif($allPassed): ?>
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-green-800 mb-2">All Requirements Met!</h3>
                    <p class="text-green-700">
                        Congratulations! Your server meets all the requirements to run this application. 
                        You can proceed to the next step.
                    </p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-yellow-800 mb-2">Optional Requirements Missing</h3>
                    <p class="text-yellow-700">
                        All required components are available, but some optional features may not work properly. 
                        You can proceed with the installation, but consider installing the missing extensions 
                        for full functionality.
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="flex items-center justify-between">
        <a href="<?php echo e(route('installer.welcome')); ?>" 
           class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors duration-300">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>

        <?php if(!$requiredFailed): ?>
            <a href="<?php echo e(route('installer.permissions')); ?>" 
               class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-300">
                Continue
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        <?php else: ?>
            <button type="button" 
                    onclick="window.location.reload()" 
                    class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-300">
                <i class="fas fa-sync-alt mr-2"></i>
                Recheck Requirements
            </button>
        <?php endif; ?>
    </div>

    <div class="mt-8 text-center">
        <p class="text-sm text-gray-500">
            Having trouble? Check our 
            <a href="#" class="text-green-600 hover:text-green-700 font-medium">server requirements guide</a> 
            for detailed instructions.
        </p>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/installer/requirements.blade.php ENDPATH**/ ?>