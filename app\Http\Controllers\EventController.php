<?php

namespace App\Http\Controllers;

use App\Models\Event;
use Illuminate\Http\Request;

class EventController extends Controller
{
    /**
     * Display a listing of events.
     */
    public function index(Request $request)
    {
        $query = Event::published();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Filter by upcoming/past
        if ($request->filled('filter')) {
            if ($request->filter === 'upcoming') {
                $query->upcoming();
            } elseif ($request->filter === 'past') {
                $query->past();
            }
        }

        // Filter by featured
        if ($request->filled('featured') && $request->featured == '1') {
            $query->featured();
        }

        // Get featured events for the hero section
        $featuredEvents = Event::published()
            ->featured()
            ->upcoming()
            ->ordered()
            ->limit(3)
            ->get();

        // Get upcoming events
        $upcomingEvents = Event::published()
            ->upcoming()
            ->ordered()
            ->limit(6)
            ->get();

        // Get all events with pagination
        $events = $query->ordered()->paginate(12);

        return view('events.index', compact('events', 'featuredEvents', 'upcomingEvents'));
    }

    /**
     * Display the specified event.
     */
    public function show(Event $event)
    {
        // Check if event is published
        if ($event->status !== 'published') {
            abort(404);
        }

        // Get related events
        $relatedEvents = Event::published()
            ->where('id', '!=', $event->id)
            ->upcoming()
            ->limit(3)
            ->get();

        // Get upcoming events for sidebar
        $upcomingEvents = Event::published()
            ->upcoming()
            ->where('id', '!=', $event->id)
            ->ordered()
            ->limit(5)
            ->get();

        return view('events.show', compact('event', 'relatedEvents', 'upcomingEvents'));
    }

    /**
     * Display upcoming events.
     */
    public function upcoming()
    {
        $events = Event::published()
            ->upcoming()
            ->ordered()
            ->paginate(12);

        $featuredEvents = Event::published()
            ->featured()
            ->upcoming()
            ->ordered()
            ->limit(3)
            ->get();

        return view('events.upcoming', compact('events', 'featuredEvents'));
    }

    /**
     * Display past events.
     */
    public function past()
    {
        $events = Event::published()
            ->past()
            ->orderBy('event_date', 'desc')
            ->paginate(12);

        return view('events.past', compact('events'));
    }

    /**
     * Display featured events.
     */
    public function featured()
    {
        $events = Event::published()
            ->featured()
            ->ordered()
            ->paginate(12);

        return view('events.featured', compact('events'));
    }

    /**
     * Search events (AJAX endpoint).
     */
    public function search(Request $request)
    {
        $query = Event::published();

        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        $events = $query->ordered()->limit(10)->get();

        return response()->json([
            'events' => $events->map(function ($event) {
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'slug' => $event->slug,
                    'description' => $event->description,
                    'event_date' => $event->formatted_event_date,
                    'location' => $event->location,
                    'featured_image_url' => $event->featured_image_url,
                    'url' => route('events.show', $event),
                ];
            })
        ]);
    }
}
