<?php

namespace App\Livewire;

use App\Models\NewsletterSubscriber;
use Livewire\Attributes\Validate;
use Livewire\Component;

class NewsletterSignup extends Component
{
    #[Validate('required|email|max:255')]
    public $email = '';

    #[Validate('nullable|string|max:255')]
    public $name = '';

    public $source = 'popup';
    public $subscribed = false;

    public function subscribe()
    {
        $this->validate();

        // Check if already subscribed
        if (NewsletterSubscriber::isSubscribed($this->email)) {
            session()->flash('info', 'You are already subscribed to our newsletter!');
            return;
        }

        NewsletterSubscriber::create([
            'email' => $this->email,
            'name' => $this->name,
            'subscription_source' => $this->source,
        ]);

        $this->subscribed = true;
        $this->reset(['email', 'name']);

        session()->flash('success', 'Thank you for subscribing to our newsletter!');
    }

    public function render()
    {
        return view('livewire.newsletter-signup');
    }
}
