@extends('installer.layout')

@section('title', 'Welcome')
@section('step', '1')

@section('content')
<div class="bg-white rounded-lg shadow-lg p-8">
    <div class="text-center mb-8">
        <div class="mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
            <i class="fas fa-rocket text-green-600 text-3xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Welcome to Laravel SaaS Platform</h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Thank you for choosing our platform! This installation wizard will guide you through the setup process 
            to get your website up and running in just a few minutes.
        </p>
    </div>

    <div class="grid md:grid-cols-2 gap-8 mb-8">
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-bolt text-blue-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Quick Setup</h3>
            </div>
            <p class="text-gray-600">
                Our automated installer will configure your database, create admin user, and set up your environment 
                in just a few clicks.
            </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-palette text-purple-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Fully Customizable</h3>
            </div>
            <p class="text-gray-600">
                Customize every aspect of your website with our powerful theme system, page builder, 
                and extensive configuration options.
            </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-users text-green-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Multi-Tenant Ready</h3>
            </div>
            <p class="text-gray-600">
                Built-in multi-tenancy support allows you to manage multiple websites from a single installation 
                with isolated data and customizations.
            </p>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-shield-alt text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Secure & Licensed</h3>
            </div>
            <p class="text-gray-600">
                Enterprise-grade security features and license validation ensure your installation is 
                protected and compliant.
            </p>
        </div>
    </div>

    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-yellow-800 mb-2">Before You Begin</h3>
                <div class="text-yellow-700">
                    <p class="mb-2">Please ensure you have the following ready:</p>
                    <ul class="list-disc list-inside space-y-1">
                        <li>MySQL database credentials</li>
                        <li>SMTP email configuration (optional)</li>
                        <li>Valid license key and purchase code</li>
                        <li>Domain name where this will be installed</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-blue-800 mb-2">What This Installer Will Do</h3>
                <div class="text-blue-700">
                    <ol class="list-decimal list-inside space-y-1">
                        <li>Check system requirements and file permissions</li>
                        <li>Configure your environment and database connection</li>
                        <li>Run database migrations and install sample data</li>
                        <li>Create your administrator account</li>
                        <li>Validate your license and activate the platform</li>
                        <li>Complete the installation and redirect you to the admin panel</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center">
        <a href="{{ route('installer.requirements') }}" 
           class="inline-flex items-center px-8 py-3 bg-green-600 text-white text-lg font-semibold rounded-lg hover:bg-green-700 transition-colors duration-300 shadow-lg hover:shadow-xl">
            <i class="fas fa-arrow-right mr-2"></i>
            Start Installation
        </a>
    </div>

    <div class="mt-8 text-center">
        <p class="text-sm text-gray-500">
            Need help? Check our 
            <a href="#" class="text-green-600 hover:text-green-700 font-medium">documentation</a> 
            or 
            <a href="#" class="text-green-600 hover:text-green-700 font-medium">contact support</a>.
        </p>
    </div>
</div>
@endsection
