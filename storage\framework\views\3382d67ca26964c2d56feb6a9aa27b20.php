
<?php if(isset($mainMenu) && $mainMenu && $mainMenu->count() > 0): ?>
    <?php $__currentLoopData = $mainMenu; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($item->children->count() > 0): ?>
            
            <div x-data="{ open: false }">
                <button @click="open = !open"
                        class="w-full text-left text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300 flex items-center justify-between">
                    <span class="flex items-center">
                        <?php if($item->icon): ?>
                            <i class="<?php echo e($item->icon); ?> mr-2"></i>
                        <?php endif; ?>
                        <?php echo e($item->title); ?>

                    </span>
                    <i class="fas fa-chevron-down text-sm transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                </button>

                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 -translate-y-1" x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 -translate-y-1"
                     class="pl-6 space-y-1">
                    <?php $__currentLoopData = $item->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            try {
                                $childUrl = $child->full_url;
                            } catch (\Exception $e) {
                                $childUrl = '#';
                            }
                        ?>
                        <a href="<?php echo e($childUrl); ?>" target="<?php echo e($child->target ?? '_self'); ?>"
                           class="block text-gray-600 hover:text-green-600 px-4 py-2 rounded-md text-sm transition-colors duration-300 <?php echo e($child->css_class ?? ''); ?>">
                            <?php if($child->icon): ?>
                                <i class="<?php echo e($child->icon); ?> mr-2"></i>
                            <?php endif; ?>
                            <?php echo e($child->title); ?>

                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php else: ?>
            
            <?php
                try {
                    $itemUrl = $item->full_url;
                } catch (\Exception $e) {
                    $itemUrl = '#';
                }
            ?>
            <a href="<?php echo e($itemUrl); ?>" target="<?php echo e($item->target ?? '_self'); ?>"
               class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300 <?php echo e($item->css_class ?? ''); ?>">
                <?php if($item->icon): ?>
                    <i class="<?php echo e($item->icon); ?> mr-2"></i>
                <?php endif; ?>
                <?php echo e($item->title); ?>

            </a>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    
    <a href="<?php echo e(route('home')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Home</a>
    <a href="<?php echo e(route('about')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">About</a>
    <a href="<?php echo e(route('services.index')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Services</a>
    <a href="<?php echo e(route('projects.index')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Projects</a>
    <a href="<?php echo e(route('events.index')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Events</a>
    <a href="<?php echo e(route('gallery.index')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Gallery</a>
    <a href="<?php echo e(route('contact')); ?>" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Contact</a>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/partials/mobile-navigation.blade.php ENDPATH**/ ?>