<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Backup existing data
        $partners = DB::table('partners')->get();

        // Drop and recreate the table with proper structure
        Schema::dropIfExists('partners');

        Schema::create('partners', function (Blueprint $table) {
            $table->id();
            $table->string('name', 500);
            $table->enum('type', ['partner', 'client'])->default('partner');
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->string('website', 500)->nullable();
            $table->string('industry', 500)->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Restore data with proper structure
        foreach ($partners as $partner) {
            if ($partner->id) { // Only restore records with valid IDs
                DB::table('partners')->insert([
                    'id' => $partner->id,
                    'name' => $partner->name,
                    'type' => $partner->type,
                    'description' => $partner->description ?: '',
                    'logo' => $partner->logo,
                    'website' => $partner->website,
                    'industry' => $partner->industry,
                    'sort_order' => $partner->sort_order,
                    'is_featured' => $partner->is_featured ? true : false,
                    'is_active' => $partner->is_active ? true : false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // Add the Central Bank record properly
        DB::table('partners')->insert([
            'name' => 'Central Bank of Nigeria, Abuja',
            'type' => 'client',
            'description' => 'Central Bank of Nigeria',
            'logo' => 'partners/QiqdBxnDa8dmbSexd9SaFU6ozxMRoibcqo3wVdMI.png',
            'website' => 'https://cbn.gov.ng',
            'industry' => 'Banking',
            'sort_order' => 5,
            'is_featured' => false,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
