<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name', 'value' => '', 'required' => false, 'height' => '400px']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name', 'value' => '', 'required' => false, 'height' => '400px']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if (! $__env->hasRenderedOnce('46d2d40f-cb81-42ce-a840-d4dc2211902a')): $__env->markAsRenderedOnce('46d2d40f-cb81-42ce-a840-d4dc2211902a'); ?>
<?php $__env->startPush('styles'); ?>
<!-- Bootstrap CSS (required for Summernote) -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Summernote CSS -->
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.css" rel="stylesheet">
<style>
    .summernote-container {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    .summernote-container:focus-within {
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    .note-editor {
        border: none !important;
    }
    .note-toolbar {
        background: #f9fafb !important;
        border-bottom: 1px solid #e5e7eb !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
    }
    .note-editable {
        font-family: 'Poppins', Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        min-height: calc(<?php echo e($height); ?> - 100px) !important;
        padding: 12px 15px !important;
    }
    .note-btn {
        background: transparent !important;
        border: none !important;
        color: #374151 !important;
    }
    .note-btn:hover {
        background: #e5e7eb !important;
    }
    .note-btn.active {
        background: #10b981 !important;
        color: white !important;
    }
</style>
<?php $__env->stopPush(); ?>
<?php endif; ?>

<div>
    <!-- Summernote textarea -->
    <textarea
        name="<?php echo e($name); ?>"
        id="<?php echo e($name); ?>"
        <?php echo e($required ? 'required' : ''); ?>

        class="summernote"
    ><?php echo e($value); ?></textarea>
</div>

<?php if (! $__env->hasRenderedOnce('0b4ad1ea-799b-499c-b003-1b4a6f00d7fc')): $__env->markAsRenderedOnce('0b4ad1ea-799b-499c-b003-1b4a6f00d7fc'); ?>
<?php $__env->startPush('scripts'); ?>
<!-- jQuery (required for Summernote) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap JS (required for Summernote) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Summernote JS -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.js"></script>
<?php $__env->stopPush(); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#<?php echo e($name); ?>').summernote({
        height: <?php echo e(str_replace('px', '', $height)); ?>,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['fontname', ['fontname']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'picture']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        placeholder: 'Start writing your content...',
        tabsize: 2,
        focus: false,
        callbacks: {
            onImageUpload: function(files) {
                uploadImage(files[0], this);
            }
        }
    });

    function uploadImage(file, editor) {
        // Check file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        $.ajax({
            url: '/admin/upload-image',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
                if (data.location) {
                    $(editor).summernote('insertImage', data.location);
                } else {
                    alert('Failed to upload image: ' + (data.error || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error uploading image:', error);
                alert('Failed to upload image. Please try again.');
            }
        });
    }

    console.log('Summernote editor initialized successfully for: <?php echo e($name); ?>');
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/components/rich-text-editor.blade.php ENDPATH**/ ?>