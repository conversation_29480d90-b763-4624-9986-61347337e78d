<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Menu Management</h1>
        <div>
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addMenuItemModal">
                <i class="fas fa-plus"></i> Add Menu Item
            </button>
        </div>
    </div>

    <!-- Menu Location Tabs -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <ul class="nav nav-tabs card-header-tabs" id="menuLocationTabs" role="tablist">
                <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo e($location === $key ? 'active' : ''); ?>" 
                           href="<?php echo e(route('admin.menus.index', ['location' => $key])); ?>" 
                           role="tab">
                            <?php echo e($name); ?>

                        </a>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Menu Structure -->
                <div class="col-lg-8">
                    <h5 class="mb-3"><?php echo e($locations[$location] ?? 'Menu'); ?> Structure</h5>
                    
                    <?php if($menuItems->count() > 0): ?>
                        <div id="menu-structure" class="menu-structure">
                            <?php echo $__env->make('admin.menus.partials.menu-items', ['items' => $menuItems, 'level' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-success" onclick="saveMenuStructure()">
                                <i class="fas fa-save"></i> Save Menu Structure
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetMenuStructure()">
                                <i class="fas fa-undo"></i> Reset Changes
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bars fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No menu items found</h5>
                            <p class="text-muted">Start building your menu by adding the first item.</p>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addMenuItemModal">
                                <i class="fas fa-plus"></i> Add First Menu Item
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Menu Preview -->
                <div class="col-lg-4">
                    <h5 class="mb-3">Menu Preview</h5>
                    <div class="card bg-light">
                        <div class="card-body">
                            <div id="menu-preview">
                                <?php if($menuItems->count() > 0): ?>
                                    <?php echo $__env->make('admin.menus.partials.menu-preview', ['items' => $menuItems], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php else: ?>
                                    <p class="text-muted text-center">Menu preview will appear here</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="mt-4">
                        <h6>Quick Actions</h6>
                        <div class="btn-group-vertical w-100" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addQuickMenuItem('Home', '/')">
                                <i class="fas fa-home"></i> Add Home Link
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addQuickMenuItem('About', '/about')">
                                <i class="fas fa-info-circle"></i> Add About Link
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addQuickMenuItem('Contact', '/contact')">
                                <i class="fas fa-envelope"></i> Add Contact Link
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addQuickMenuItem('Services', '/services')">
                                <i class="fas fa-cogs"></i> Add Services Link
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Menu Item Modal -->
<div class="modal fade" id="addMenuItemModal" tabindex="-1" role="dialog" aria-labelledby="addMenuItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMenuItemModalLabel">Add Menu Item</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addMenuItemForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="menu_title">Menu Text <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="menu_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="menu_icon">Icon (Optional)</label>
                                <input type="text" class="form-control" id="menu_icon" name="icon" placeholder="fas fa-home">
                                <small class="form-text text-muted">FontAwesome icon class</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Link Type</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="link_type" id="link_type_url" value="url" checked>
                            <label class="form-check-label" for="link_type_url">Custom URL</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="link_type" id="link_type_route" value="route">
                            <label class="form-check-label" for="link_type_route">Laravel Route</label>
                        </div>
                    </div>
                    
                    <div id="url_field" class="form-group">
                        <label for="menu_url">URL</label>
                        <input type="text" class="form-control" id="menu_url" name="url" placeholder="https://example.com or /page">
                    </div>
                    
                    <div id="route_field" class="form-group" style="display: none;">
                        <label for="menu_route">Route Name</label>
                        <select class="form-control" id="menu_route" name="route_name">
                            <option value="">Select a route</option>
                            <?php $__currentLoopData = $availableRoutes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $routeName => $routeLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($routeName); ?>"><?php echo e($routeLabel); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="menu_target">Target</label>
                                <select class="form-control" id="menu_target" name="target">
                                    <option value="_self">Same Window</option>
                                    <option value="_blank">New Window</option>
                                    <option value="_parent">Parent Frame</option>
                                    <option value="_top">Top Frame</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="menu_css_class">CSS Classes</label>
                                <input type="text" class="form-control" id="menu_css_class" name="css_class" placeholder="custom-class">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="menu_parent">Parent Menu Item</label>
                        <select class="form-control" id="menu_parent" name="parent_id">
                            <option value="">Top Level Item</option>
                            <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($item->id); ?>"><?php echo e($item->title); ?></option>
                                <?php if($item->children->count() > 0): ?>
                                    <?php $__currentLoopData = $item->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($child->id); ?>">-- <?php echo e($child->title); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="menu_description">Description (Admin Only)</label>
                        <textarea class="form-control" id="menu_description" name="description" rows="2" placeholder="Internal description for admin reference"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="menu_is_active" name="is_active" checked>
                        <label class="form-check-label" for="menu_is_active">
                            Active (visible on website)
                        </label>
                    </div>
                    
                    <input type="hidden" name="menu_location" value="<?php echo e($location); ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Menu Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Menu Item Modal -->
<div class="modal fade" id="editMenuItemModal" tabindex="-1" role="dialog" aria-labelledby="editMenuItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMenuItemModalLabel">Edit Menu Item</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editMenuItemForm">
                <div class="modal-body">
                    <!-- Same fields as add form, will be populated via JavaScript -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_menu_title">Menu Text <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_menu_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_menu_icon">Icon (Optional)</label>
                                <input type="text" class="form-control" id="edit_menu_icon" name="icon" placeholder="fas fa-home">
                                <small class="form-text text-muted">FontAwesome icon class</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Link Type</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_link_type" id="edit_link_type_url" value="url">
                            <label class="form-check-label" for="edit_link_type_url">Custom URL</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_link_type" id="edit_link_type_route" value="route">
                            <label class="form-check-label" for="edit_link_type_route">Laravel Route</label>
                        </div>
                    </div>
                    
                    <div id="edit_url_field" class="form-group">
                        <label for="edit_menu_url">URL</label>
                        <input type="text" class="form-control" id="edit_menu_url" name="url" placeholder="https://example.com or /page">
                    </div>
                    
                    <div id="edit_route_field" class="form-group" style="display: none;">
                        <label for="edit_menu_route">Route Name</label>
                        <select class="form-control" id="edit_menu_route" name="route_name">
                            <option value="">Select a route</option>
                            <?php $__currentLoopData = $availableRoutes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $routeName => $routeLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($routeName); ?>"><?php echo e($routeLabel); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_menu_target">Target</label>
                                <select class="form-control" id="edit_menu_target" name="target">
                                    <option value="_self">Same Window</option>
                                    <option value="_blank">New Window</option>
                                    <option value="_parent">Parent Frame</option>
                                    <option value="_top">Top Frame</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_menu_css_class">CSS Classes</label>
                                <input type="text" class="form-control" id="edit_menu_css_class" name="css_class" placeholder="custom-class">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_menu_description">Description (Admin Only)</label>
                        <textarea class="form-control" id="edit_menu_description" name="description" rows="2" placeholder="Internal description for admin reference"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_menu_is_active" name="is_active">
                        <label class="form-check-label" for="edit_menu_is_active">
                            Active (visible on website)
                        </label>
                    </div>
                    
                    <input type="hidden" id="edit_menu_id" name="menu_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Menu Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
<style>
.menu-structure {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    background-color: #f8f9fc;
    min-height: 200px;
}

.menu-item {
    background: white;
    border: 1px solid #d1d3e2;
    border-radius: 0.25rem;
    margin: 0.5rem 0;
    padding: 0.75rem;
    cursor: move;
    position: relative;
}

.menu-item:hover {
    border-color: #5a5c69;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.menu-item.ui-sortable-helper {
    transform: rotate(2deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.menu-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.menu-item-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.menu-item-icon {
    margin-right: 0.5rem;
    color: #5a5c69;
}

.menu-item-title {
    font-weight: 600;
    color: #5a5c69;
}

.menu-item-url {
    font-size: 0.875rem;
    color: #858796;
    margin-left: 0.5rem;
}

.menu-item-actions {
    display: flex;
    gap: 0.25rem;
}

.menu-item-children {
    margin-left: 2rem;
    margin-top: 0.5rem;
    border-left: 2px solid #e3e6f0;
    padding-left: 1rem;
}

.menu-item-inactive {
    opacity: 0.6;
}

.menu-item-inactive .menu-item-title::after {
    content: " (Hidden)";
    font-size: 0.75rem;
    color: #e74a3b;
    font-weight: normal;
}

.sortable-placeholder {
    border: 2px dashed #d1d3e2;
    background: #f8f9fc;
    height: 60px;
    margin: 0.5rem 0;
    border-radius: 0.25rem;
}

#menu-preview ul {
    list-style: none;
    padding-left: 0;
}

#menu-preview ul ul {
    padding-left: 1.5rem;
    margin-top: 0.5rem;
}

#menu-preview li {
    padding: 0.25rem 0;
}

#menu-preview a {
    color: #5a5c69;
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    display: inline-block;
}

#menu-preview a:hover {
    background-color: #eaecf4;
    color: #3a3b45;
}

#menu-preview .inactive {
    opacity: 0.5;
    text-decoration: line-through;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize sortable
    initializeSortable();
    
    // Link type toggle
    $('input[name="link_type"]').change(function() {
        toggleLinkType(this.value, '');
    });
    
    $('input[name="edit_link_type"]').change(function() {
        toggleLinkType(this.value, 'edit_');
    });
    
    // Form submissions
    $('#addMenuItemForm').submit(function(e) {
        e.preventDefault();
        addMenuItem();
    });
    
    $('#editMenuItemForm').submit(function(e) {
        e.preventDefault();
        updateMenuItem();
    });
});

function initializeSortable() {
    $("#menu-structure").sortable({
        items: ".menu-item",
        placeholder: "sortable-placeholder",
        connectWith: ".menu-item-children",
        handle: ".menu-item-content",
        tolerance: "pointer",
        update: function(event, ui) {
            // Update will be handled when save button is clicked
        }
    });
    
    $(".menu-item-children").sortable({
        items: ".menu-item",
        placeholder: "sortable-placeholder",
        connectWith: "#menu-structure, .menu-item-children",
        handle: ".menu-item-content",
        tolerance: "pointer"
    });
}

function toggleLinkType(type, prefix) {
    if (type === 'url') {
        $(`#${prefix}url_field`).show();
        $(`#${prefix}route_field`).hide();
        $(`#${prefix}menu_url`).prop('required', true);
        $(`#${prefix}menu_route`).prop('required', false);
    } else {
        $(`#${prefix}url_field`).hide();
        $(`#${prefix}route_field`).show();
        $(`#${prefix}menu_url`).prop('required', false);
        $(`#${prefix}menu_route`).prop('required', true);
    }
}

function addMenuItem() {
    const formData = new FormData(document.getElementById('addMenuItemForm'));
    
    fetch('<?php echo e(route("admin.menus.store")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#addMenuItemModal').modal('hide');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding menu item');
    });
}

function editMenuItem(id) {
    // Fetch menu item data and populate edit form
    fetch(`/admin/menus/${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = data.item;
            
            $('#edit_menu_id').val(item.id);
            $('#edit_menu_title').val(item.title);
            $('#edit_menu_icon').val(item.icon);
            $('#edit_menu_url').val(item.url);
            $('#edit_menu_route').val(item.route_name);
            $('#edit_menu_target').val(item.target);
            $('#edit_menu_css_class').val(item.css_class);
            $('#edit_menu_description').val(item.description);
            $('#edit_menu_is_active').prop('checked', item.is_active);
            
            // Set link type
            if (item.route_name) {
                $('#edit_link_type_route').prop('checked', true);
                toggleLinkType('route', 'edit_');
            } else {
                $('#edit_link_type_url').prop('checked', true);
                toggleLinkType('url', 'edit_');
            }
            
            $('#editMenuItemModal').modal('show');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading menu item');
    });
}

function updateMenuItem() {
    const formData = new FormData(document.getElementById('editMenuItemForm'));
    const menuId = document.getElementById('edit_menu_id').value;
    
    fetch(`/admin/menus/${menuId}`, {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(Object.fromEntries(formData))
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#editMenuItemModal').modal('hide');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating menu item');
    });
}

function deleteMenuItem(id) {
    if (confirm('Are you sure you want to delete this menu item?')) {
        fetch(`/admin/menus/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting menu item');
        });
    }
}

function toggleMenuItem(id) {
    fetch(`/admin/menus/${id}/toggle-active`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error toggling menu item');
    });
}

function duplicateMenuItem(id) {
    fetch(`/admin/menus/${id}/duplicate`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error duplicating menu item');
    });
}

function saveMenuStructure() {
    const structure = getMenuStructure();
    
    fetch('<?php echo e(route("admin.menus.update-structure")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            location: '<?php echo e($location); ?>',
            items: structure
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Menu structure saved successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving menu structure');
    });
}

function getMenuStructure() {
    const items = [];
    
    $('#menu-structure > .menu-item').each(function(index) {
        const item = {
            id: parseInt($(this).data('id')),
            children: []
        };
        
        // Get children
        $(this).find('.menu-item-children > .menu-item').each(function(childIndex) {
            item.children.push({
                id: parseInt($(this).data('id'))
            });
        });
        
        items.push(item);
    });
    
    return items;
}

function resetMenuStructure() {
    if (confirm('Are you sure you want to reset all changes?')) {
        location.reload();
    }
}

function addQuickMenuItem(title, url) {
    const formData = new FormData();
    formData.append('title', title);
    formData.append('url', url);
    formData.append('menu_location', '<?php echo e($location); ?>');
    formData.append('is_active', '1');
    formData.append('target', '_self');
    
    fetch('<?php echo e(route("admin.menus.store")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding menu item');
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/admin/menus/index.blade.php ENDPATH**/ ?>