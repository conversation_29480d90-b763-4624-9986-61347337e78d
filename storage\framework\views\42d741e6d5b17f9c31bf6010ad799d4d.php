<?php $__env->startSection('content'); ?>
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Menu Management</h1>
            <button type="button" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" onclick="openAddMenuModal()">
                Add Menu Item
            </button>
        </div>

        <!-- Menu Location Tabs -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(route('admin.menus.index', ['location' => $key])); ?>"
                           class="py-4 px-1 border-b-2 font-medium text-sm <?php echo e($location === $key ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                            <?php echo e($name); ?>

                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </nav>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Menu Structure -->
                    <div class="lg:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4"><?php echo e($locations[$location] ?? 'Menu'); ?> Structure</h3>

                        <?php if($menuItems->count() > 0): ?>
                            <div id="menu-structure" class="bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-48">
                                <?php echo $__env->make('admin.menus.partials.menu-items', ['items' => $menuItems, 'level' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>

                            <div class="mt-4 flex gap-3">
                                <button type="button" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium" onclick="saveMenuStructure()">
                                    Save Menu Structure
                                </button>
                                <button type="button" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium" onclick="resetMenuStructure()">
                                    Reset Changes
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-12">
                                <div class="text-gray-400 text-6xl mb-4">📋</div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No menu items found</h3>
                                <p class="text-gray-500 mb-6">Start building your menu by adding the first item.</p>
                                <button type="button" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" onclick="openAddMenuModal()">
                                    Add First Menu Item
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Menu Preview -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Menu Preview</h3>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div id="menu-preview">
                                <?php if($menuItems->count() > 0): ?>
                                    <?php echo $__env->make('admin.menus.partials.menu-preview', ['items' => $menuItems], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php else: ?>
                                    <p class="text-gray-500 text-center">Menu preview will appear here</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">Quick Actions</h4>
                            <div class="space-y-2">
                                <button type="button" class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-left transition-colors duration-200" onclick="addQuickMenuItem('Home', '/')">
                                    🏠 Add Home Link
                                </button>
                                <button type="button" class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-left transition-colors duration-200" onclick="addQuickMenuItem('About', '/about')">
                                    ℹ️ Add About Link
                                </button>
                                <button type="button" class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-left transition-colors duration-200" onclick="addQuickMenuItem('Contact', '/contact')">
                                    ✉️ Add Contact Link
                                </button>
                                <button type="button" class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-left transition-colors duration-200" onclick="addQuickMenuItem('Services', '/services')">
                                    ⚙️ Add Services Link
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Menu Item Modal -->
<div id="addMenuItemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Add Menu Item</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeAddMenuModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="addMenuItemForm">
            <div class="mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menu_title" class="block text-sm font-medium text-gray-700 mb-1">Menu Text <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_title" name="title" required>
                    </div>
                    <div>
                        <label for="menu_icon" class="block text-sm font-medium text-gray-700 mb-1">Icon (Optional)</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_icon" name="icon" placeholder="fas fa-home">
                        <p class="text-xs text-gray-500 mt-1">FontAwesome icon class</p>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Link Type</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="link_type" id="link_type_url" value="url" checked class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Custom URL</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="link_type" id="link_type_route" value="route" class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Laravel Route</span>
                        </label>
                    </div>
                </div>

                <div id="url_field" class="mt-4">
                    <label for="menu_url" class="block text-sm font-medium text-gray-700 mb-1">URL</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_url" name="url" placeholder="https://example.com or /page">
                </div>

                <div id="route_field" class="mt-4 hidden">
                    <label for="menu_route" class="block text-sm font-medium text-gray-700 mb-1">Route Name</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_route" name="route_name">
                        <option value="">Select a route</option>
                        <?php $__currentLoopData = $availableRoutes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $routeName => $routeLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($routeName); ?>"><?php echo e($routeLabel); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <label for="menu_target" class="block text-sm font-medium text-gray-700 mb-1">Target</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_target" name="target">
                            <option value="_self">Same Window</option>
                            <option value="_blank">New Window</option>
                            <option value="_parent">Parent Frame</option>
                            <option value="_top">Top Frame</option>
                        </select>
                    </div>
                    <div>
                        <label for="menu_css_class" class="block text-sm font-medium text-gray-700 mb-1">CSS Classes</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_css_class" name="css_class" placeholder="custom-class">
                    </div>
                </div>

                <div class="mt-4">
                    <label for="menu_parent" class="block text-sm font-medium text-gray-700 mb-1">Parent Menu Item</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_parent" name="parent_id">
                        <option value="">Top Level Item</option>
                        <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item->id); ?>"><?php echo e($item->title); ?></option>
                            <?php if($item->children->count() > 0): ?>
                                <?php $__currentLoopData = $item->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($child->id); ?>">-- <?php echo e($child->title); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="mt-4">
                    <label for="menu_description" class="block text-sm font-medium text-gray-700 mb-1">Description (Admin Only)</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" id="menu_description" name="description" rows="2" placeholder="Internal description for admin reference"></textarea>
                </div>

                <div class="mt-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="menu_is_active" name="is_active" checked class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Active (visible on website)</span>
                    </label>
                </div>

                <input type="hidden" name="menu_location" value="<?php echo e($location); ?>">
            </div>
            <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button type="button" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium" onclick="closeAddMenuModal()">Cancel</button>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">Add Menu Item</button>
            </div>
        </form>
    </div>
</div>



<?php $__env->startPush('styles'); ?>
<style>
.menu-item {
    @apply bg-white border border-gray-200 rounded-lg mb-2 p-3 cursor-move relative transition-all duration-200;
}

.menu-item:hover {
    @apply border-gray-300 shadow-md;
}

.menu-item-content {
    @apply flex items-center justify-between;
}

.menu-item-info {
    @apply flex items-center flex-1;
}

.menu-item-title {
    @apply font-medium text-gray-900;
}

.menu-item-url {
    @apply text-sm text-gray-500 ml-2;
}

.menu-item-actions {
    @apply flex gap-1;
}

.menu-item-children {
    @apply ml-8 mt-2 border-l-2 border-gray-200 pl-4;
}

.menu-item-inactive {
    @apply opacity-60;
}

.sortable-placeholder {
    @apply border-2 border-dashed border-gray-300 bg-gray-50 h-16 mb-2 rounded-lg;
}

#menu-preview ul {
    @apply list-none pl-0;
}

#menu-preview ul ul {
    @apply pl-6 mt-2;
}

#menu-preview li {
    @apply py-1;
}

#menu-preview a {
    @apply text-gray-700 no-underline py-1 px-2 rounded inline-block hover:bg-gray-100 hover:text-gray-900;
}

#menu-preview .inactive {
    @apply opacity-50 line-through;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Link type toggle
    document.querySelectorAll('input[name="link_type"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            toggleLinkType(this.value);
        });
    });

    // Form submission
    document.getElementById('addMenuItemForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addMenuItem();
    });
});

function openAddMenuModal() {
    document.getElementById('addMenuItemModal').classList.remove('hidden');
}

function closeAddMenuModal() {
    document.getElementById('addMenuItemModal').classList.add('hidden');
    document.getElementById('addMenuItemForm').reset();
    toggleLinkType('url'); // Reset to default
}

function toggleLinkType(type) {
    const urlField = document.getElementById('url_field');
    const routeField = document.getElementById('route_field');
    const urlInput = document.getElementById('menu_url');
    const routeSelect = document.getElementById('menu_route');

    if (type === 'url') {
        urlField.classList.remove('hidden');
        routeField.classList.add('hidden');
        urlInput.required = true;
        routeSelect.required = false;
    } else {
        urlField.classList.add('hidden');
        routeField.classList.remove('hidden');
        urlInput.required = false;
        routeSelect.required = true;
    }
}

function addMenuItem() {
    const formData = new FormData(document.getElementById('addMenuItemForm'));

    fetch('<?php echo e(route("admin.menus.store")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeAddMenuModal();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding menu item');
    });
}

function addQuickMenuItem(title, url) {
    const formData = new FormData();
    formData.append('title', title);
    formData.append('url', url);
    formData.append('menu_location', '<?php echo e($location); ?>');
    formData.append('is_active', '1');

    fetch('<?php echo e(route("admin.menus.store")); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding menu item');
    });
}

function editMenuItem(id) {
    // For now, redirect to edit page or show simple prompt
    const newTitle = prompt('Enter new title for menu item:');
    if (newTitle) {
        fetch(`/admin/menus/${id}`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({title: newTitle})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating menu item');
        });
    }
}

function deleteMenuItem(id) {
    if (confirm('Are you sure you want to delete this menu item?')) {
        fetch(`/admin/menus/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting menu item');
        });
    }
}

function toggleMenuItem(id) {
    fetch(`/admin/menus/${id}/toggle-active`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error toggling menu item');
    });
}

function duplicateMenuItem(id) {
    fetch(`/admin/menus/${id}/duplicate`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error duplicating menu item');
    });
}

function saveMenuStructure() {
    alert('Menu structure saving functionality requires additional implementation.');
}

function resetMenuStructure() {
    if (confirm('Are you sure you want to reset all changes?')) {
        location.reload();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/admin/menus/index.blade.php ENDPATH**/ ?>