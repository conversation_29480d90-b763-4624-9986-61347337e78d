<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Installation Wizard') - {{ config('app.name', 'Laravel SaaS Platform') }}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        .step-indicator {
            @apply flex items-center justify-center w-10 h-10 rounded-full border-2 text-sm font-semibold transition-all duration-300;
        }
        .step-indicator.active {
            @apply bg-green-600 border-green-600 text-white;
        }
        .step-indicator.completed {
            @apply bg-green-600 border-green-600 text-white;
        }
        .step-indicator.pending {
            @apply bg-gray-100 border-gray-300 text-gray-500;
        }
        .step-line {
            @apply flex-1 h-0.5 bg-gray-300 mx-4;
        }
        .step-line.completed {
            @apply bg-green-600;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <h1 class="text-2xl font-bold text-gray-900">
                                <i class="fas fa-cog text-green-600 mr-2"></i>
                                Installation Wizard
                            </h1>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">
                        Step @yield('step', '1') of 7
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Steps -->
        <div class="bg-white border-b">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex items-center">
                    <!-- Welcome -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.welcome') ? 'active' : (in_array(Route::currentRouteName(), ['installer.requirements', 'installer.permissions', 'installer.environment', 'installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : 'pending') }}">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Welcome</span>
                    </div>
                    <div class="step-line {{ in_array(Route::currentRouteName(), ['installer.requirements', 'installer.permissions', 'installer.environment', 'installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : '' }}"></div>

                    <!-- Requirements -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.requirements') ? 'active' : (in_array(Route::currentRouteName(), ['installer.permissions', 'installer.environment', 'installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : 'pending') }}">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Requirements</span>
                    </div>
                    <div class="step-line {{ in_array(Route::currentRouteName(), ['installer.permissions', 'installer.environment', 'installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : '' }}"></div>

                    <!-- Permissions -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.permissions') ? 'active' : (in_array(Route::currentRouteName(), ['installer.environment', 'installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : 'pending') }}">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Permissions</span>
                    </div>
                    <div class="step-line {{ in_array(Route::currentRouteName(), ['installer.environment', 'installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : '' }}"></div>

                    <!-- Environment -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.environment') ? 'active' : (in_array(Route::currentRouteName(), ['installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : 'pending') }}">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Environment</span>
                    </div>
                    <div class="step-line {{ in_array(Route::currentRouteName(), ['installer.database', 'installer.admin', 'installer.license', 'installer.final']) ? 'completed' : '' }}"></div>

                    <!-- Database -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.database') ? 'active' : (in_array(Route::currentRouteName(), ['installer.admin', 'installer.license', 'installer.final']) ? 'completed' : 'pending') }}">
                            <i class="fas fa-database"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Database</span>
                    </div>
                    <div class="step-line {{ in_array(Route::currentRouteName(), ['installer.admin', 'installer.license', 'installer.final']) ? 'completed' : '' }}"></div>

                    <!-- Admin -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.admin') ? 'active' : (in_array(Route::currentRouteName(), ['installer.license', 'installer.final']) ? 'completed' : 'pending') }}">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Admin</span>
                    </div>
                    <div class="step-line {{ in_array(Route::currentRouteName(), ['installer.license', 'installer.final']) ? 'completed' : '' }}"></div>

                    <!-- License -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.license') ? 'active' : (request()->routeIs('installer.final') ? 'completed' : 'pending') }}">
                            <i class="fas fa-key"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">License</span>
                    </div>
                    <div class="step-line {{ request()->routeIs('installer.final') ? 'completed' : '' }}"></div>

                    <!-- Final -->
                    <div class="flex flex-col items-center">
                        <div class="step-indicator {{ request()->routeIs('installer.final') ? 'active' : 'pending' }}">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <span class="text-xs mt-2 text-gray-600">Complete</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                @if(session('success'))
                    <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm">{{ session('success') }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm">{{ session('error') }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                @if($errors->any())
                    <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium">Please fix the following errors:</h3>
                                <ul class="mt-2 text-sm list-disc list-inside">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                @yield('content')
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <div class="text-center text-sm text-gray-500">
                    <p>&copy; {{ date('Y') }} {{ config('app.name', 'Laravel SaaS Platform') }}. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    @stack('scripts')
</body>
</html>
