@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Contact Enquiries</h1>
            <div class="flex space-x-2">
                <!-- Filter Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
                        Filter
                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 z-50 mt-2 w-48 bg-white rounded-md shadow-lg py-1">
                        <a href="{{ route('admin.contact-enquiries.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All Enquiries</a>
                        <a href="{{ route('admin.contact-enquiries.index', ['status' => 'unread']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Unread Only</a>
                        <a href="{{ route('admin.contact-enquiries.index', ['status' => 'read']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Read Only</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Form -->
        <div class="mb-6">
            <form method="GET" action="{{ route('admin.contact-enquiries.index') }}" class="flex space-x-4">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="Search enquiries..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                <input type="hidden" name="status" value="{{ request('status') }}">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
                @if(request()->hasAny(['search', 'status']))
                    <a href="{{ route('admin.contact-enquiries.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                        Clear
                    </a>
                @endif
            </form>
        </div>

        @if($enquiries->count() > 0)
            <!-- Bulk Actions -->
            <form id="bulk-form" action="{{ route('admin.contact-enquiries.bulk-action') }}" method="POST" class="mb-6">
                @csrf
                <div class="flex items-center space-x-4">
                    <select name="action" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="">Bulk Actions</option>
                        <option value="mark_read">Mark as Read</option>
                        <option value="mark_unread">Mark as Unread</option>
                        <option value="delete">Delete</option>
                    </select>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Are you sure you want to perform this action?')">
                        Apply
                    </button>
                </div>
            </form>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-green-600">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($enquiries as $enquiry)
                        <tr class="{{ !$enquiry->is_read ? 'bg-blue-50' : '' }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="enquiries[]" value="{{ $enquiry->id }}" form="bulk-form" class="rounded border-gray-300 text-green-600">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 {{ !$enquiry->is_read ? 'font-bold' : '' }}">
                                            {{ $enquiry->name }}
                                        </div>
                                        <div class="text-sm text-gray-500">{{ $enquiry->email }}</div>
                                        @if($enquiry->company)
                                            <div class="text-xs text-gray-400">{{ $enquiry->company }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 {{ !$enquiry->is_read ? 'font-bold' : '' }}">{{ $enquiry->subject }}</div>
                                <div class="text-sm text-gray-500">{{ $enquiry->short_message }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($enquiry->is_read)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Read
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Unread
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $enquiry->created_at->format('M d, Y') }}
                                <div class="text-xs text-gray-400">{{ $enquiry->created_at->format('g:i A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.contact-enquiries.show', $enquiry) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                    <form action="{{ route('admin.contact-enquiries.mark-read', $enquiry) }}" method="POST" class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="text-indigo-600 hover:text-indigo-900">
                                            {{ $enquiry->is_read ? 'Mark Unread' : 'Mark Read' }}
                                        </button>
                                    </form>
                                    <form action="{{ route('admin.contact-enquiries.destroy', $enquiry) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this enquiry?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($enquiries->hasPages())
                <div class="mt-6">
                    {{ $enquiries->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="text-gray-400 text-6xl mb-4">💬</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No enquiries found</h3>
                <p class="text-gray-500">
                    @if(request()->hasAny(['search', 'status']))
                        Try adjusting your search criteria.
                    @else
                        Contact enquiries will appear here when visitors submit the contact form.
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('input[name="enquiries[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});
</script>
@endsection
