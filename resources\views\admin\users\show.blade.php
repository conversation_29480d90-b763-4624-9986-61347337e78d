@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">{{ $user->name }}</h1>
            <div class="flex space-x-2">
                @if($user->id !== auth()->id())
                    <a href="{{ route('admin.users.edit', $user) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Edit User
                    </a>
                @endif
                <a href="{{ route('admin.users.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Users
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- User Profile Card -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center text-white text-2xl font-medium">
                            {{ substr($user->name, 0, 1) }}
                        </div>
                        <div>
                            <h2 class="text-2xl font-semibold text-gray-900">{{ $user->name }}</h2>
                            <p class="text-gray-600">{{ $user->email }}</p>
                            <div class="flex items-center space-x-2 mt-2">
                                @if($user->role === 'admin')
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Admin
                                    </span>
                                @elseif($user->role === 'editor')
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Editor
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        User
                                    </span>
                                @endif
                                
                                @if($user->is_active)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                @endif

                                @if($user->email_verified_at)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Verified
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Unverified
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Blog Posts -->
                @if($user->role === 'admin' || $user->role === 'editor')
                    @if($recentPosts->count() > 0)
                    <div class="mt-6 bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Blog Posts ({{ $user->blog_posts_count }} total)</h3>
                        <div class="space-y-4">
                            @foreach($recentPosts as $post)
                            <div class="border-l-4 {{ $post->is_published ? 'border-green-400' : 'border-gray-300' }} pl-4">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-semibold text-gray-900">
                                        <a href="{{ route('admin.blog-posts.show', $post) }}" class="hover:text-green-600">
                                            {{ Str::limit($post->title, 50) }}
                                        </a>
                                    </h4>
                                    <span class="text-sm text-gray-500">{{ $post->created_at->format('M d, Y') }}</span>
                                </div>
                                <div class="flex items-center space-x-4 mt-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" style="background-color: {{ $post->category->color }}20; color: {{ $post->category->color }};">
                                        {{ $post->category->name }}
                                    </span>
                                    @if($post->is_published)
                                        <span class="text-xs text-green-600">Published</span>
                                    @else
                                        <span class="text-xs text-gray-500">Draft</span>
                                    @endif
                                    @if($post->is_featured)
                                        <span class="text-xs text-yellow-600">Featured</span>
                                    @endif
                                    <span class="text-xs text-gray-400">{{ number_format($post->views_count) }} views</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        
                        @if($user->blog_posts_count > 5)
                            <div class="mt-4 text-center">
                                <a href="{{ route('admin.blog-posts.index', ['author' => $user->id]) }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    View all {{ $user->blog_posts_count }} posts by this user →
                                </a>
                            </div>
                        @endif
                    </div>
                    @else
                    <div class="mt-6 bg-gray-50 p-6 rounded-lg text-center">
                        <div class="text-gray-400 text-4xl mb-3">📝</div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No blog posts yet</h3>
                        <p class="text-gray-500 mb-4">This user hasn't created any blog posts yet.</p>
                        @if($user->role === 'admin' || $user->role === 'editor')
                            <a href="{{ route('admin.blog-posts.create') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Create First Post
                            </a>
                        @endif
                    </div>
                    @endif
                @endif

                <!-- Contact Actions -->
                <div class="mt-6 bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Actions</h3>
                    <div class="space-y-3">
                        <a href="mailto:{{ $user->email }}?subject=Message from {{ config('app.name') }} Admin" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Send Email
                        </a>
                        
                        <button onclick="copyToClipboard('{{ $user->email }}')" 
                                class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-bold rounded ml-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            Copy Email
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- User Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">User Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Name:</span>
                            <div class="text-gray-900 font-medium">{{ $user->name }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Email:</span>
                            <div class="text-gray-900 font-medium break-all">{{ $user->email }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Role:</span>
                            <div class="text-gray-900 font-medium">{{ ucfirst($user->role) }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Status:</span>
                            <div class="text-gray-900 font-medium">
                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                            </div>
                        </div>
                        <div>
                            <span class="text-gray-600">Email Verified:</span>
                            <div class="text-gray-900 font-medium">
                                {{ $user->email_verified_at ? 'Yes' : 'No' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Statistics -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Joined:</span>
                            <div class="text-gray-900">{{ $user->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Member for:</span>
                            <div class="text-gray-900">{{ $user->created_at->diffForHumans() }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $user->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @if($user->email_verified_at)
                        <div>
                            <span class="text-gray-600">Email Verified:</span>
                            <div class="text-gray-900">{{ $user->email_verified_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @endif
                        @if($user->role === 'admin' || $user->role === 'editor')
                        <div>
                            <span class="text-gray-600">Blog Posts:</span>
                            <div class="text-gray-900">{{ $user->blog_posts_count }} posts</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        @if($user->id !== auth()->id())
                            <a href="{{ route('admin.users.edit', $user) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                                Edit User
                            </a>
                            
                            <form action="{{ route('admin.users.toggle-status', $user) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                    {{ $user->is_active ? 'Deactivate User' : 'Activate User' }}
                                </button>
                            </form>
                            
                            @if($user->blog_posts_count == 0)
                                <form action="{{ route('admin.users.destroy', $user) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                        Delete User
                                    </button>
                                </form>
                            @else
                                <div class="w-full bg-gray-400 text-white font-bold py-2 px-4 rounded text-center cursor-not-allowed">
                                    Cannot Delete (Has Posts)
                                </div>
                                <p class="text-xs text-gray-500 text-center">User has authored blog posts</p>
                            @endif
                        @else
                            <div class="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded text-center">
                                Current User (You)
                            </div>
                            <p class="text-xs text-gray-500 text-center">You cannot modify your own account from here</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show a temporary success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    });
}
</script>
@endsection
