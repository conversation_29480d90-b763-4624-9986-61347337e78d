<?php

namespace Database\Seeders;

use App\Models\Popup;
use Illuminate\Database\Seeder;

class PopupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $popups = [
            [
                'name' => 'newsletter_popup',
                'type' => 'newsletter',
                'title' => 'Subscribe to Our Newsletter',
                'content' => 'Stay updated with the latest business insights, industry trends, and expert advice from Fair Price Ventures. Join thousands of business leaders who trust our newsletter for valuable content delivered directly to their inbox.',
                'button_text' => 'Subscribe Now',
                'button_color' => '#10B981',
                'delay_seconds' => 5,
                'is_active' => true,
                'display_pages' => json_encode(['home', 'blog', 'services']),
                'display_frequency' => 1,
            ],
            [
                'name' => 'consultation_popup',
                'type' => 'announcement',
                'title' => 'Free Business Consultation',
                'content' => 'Ready to take your business to the next level? Book a free 30-minute consultation with our expert team and discover how we can help you achieve your business goals.',
                'button_text' => 'Book Free Consultation',
                'button_color' => '#3B82F6',
                'delay_seconds' => 10,
                'is_active' => false, // Disabled for now
                'display_pages' => json_encode(['services']),
                'display_frequency' => 1,
            ],
        ];

        foreach ($popups as $popup) {
            Popup::firstOrCreate(
                ['name' => $popup['name']],
                $popup
            );
        }
    }
}
