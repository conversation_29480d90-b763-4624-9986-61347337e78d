{{-- Mobile Navigation Menu --}}
@if(isset($mainMenu) && $mainMenu && $mainMenu->count() > 0)
    @foreach($mainMenu as $item)
        @if($item->children->count() > 0)
            {{-- Mobile Dropdown Menu --}}
            <div x-data="{ open: false }">
                <button @click="open = !open"
                        class="w-full text-left text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300 flex items-center justify-between">
                    <span class="flex items-center">
                        @if($item->icon)
                            <i class="{{ $item->icon }} mr-2"></i>
                        @endif
                        {{ $item->title }}
                    </span>
                    <i class="fas fa-chevron-down text-sm transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                </button>

                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 -translate-y-1" x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 -translate-y-1"
                     class="pl-6 space-y-1">
                    @foreach($item->children as $child)
                        @php
                            try {
                                $childUrl = $child->full_url;
                            } catch (\Exception $e) {
                                $childUrl = '#';
                            }
                        @endphp
                        <a href="{{ $childUrl }}" target="{{ $child->target ?? '_self' }}"
                           class="block text-gray-600 hover:text-green-600 px-4 py-2 rounded-md text-sm transition-colors duration-300 {{ $child->css_class ?? '' }}">
                            @if($child->icon)
                                <i class="{{ $child->icon }} mr-2"></i>
                            @endif
                            {{ $child->title }}
                        </a>
                    @endforeach
                </div>
            </div>
        @else
            {{-- Single Mobile Menu Item --}}
            @php
                try {
                    $itemUrl = $item->full_url;
                } catch (\Exception $e) {
                    $itemUrl = '#';
                }
            @endphp
            <a href="{{ $itemUrl }}" target="{{ $item->target ?? '_self' }}"
               class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300 {{ $item->css_class ?? '' }}">
                @if($item->icon)
                    <i class="{{ $item->icon }} mr-2"></i>
                @endif
                {{ $item->title }}
            </a>
        @endif
    @endforeach
@else
    {{-- Fallback static mobile menu if no dynamic menu items exist --}}
    <a href="{{ route('home') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Home</a>
    <a href="{{ route('about') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">About</a>
    <a href="{{ route('services.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Services</a>
    <a href="{{ route('projects.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Projects</a>
    <a href="{{ route('events.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Events</a>
    <a href="{{ route('gallery.index') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Gallery</a>
    <a href="{{ route('contact') }}" class="block text-gray-700 hover:text-green-600 px-4 py-3 rounded-md text-base font-semibold transition-colors duration-300">Contact</a>
@endif
