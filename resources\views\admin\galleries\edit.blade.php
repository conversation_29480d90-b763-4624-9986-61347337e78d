@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Edit Gallery: {{ $gallery->title }}</h1>
            <div class="flex gap-3">
                <a href="{{ route('admin.galleries.show', $gallery) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    👁️ View Gallery
                </a>
                <a href="{{ route('admin.galleries.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Back to Galleries
                </a>
            </div>
        </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Gallery Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Gallery Details</h6>
                </div>
                <div class="card-body">
                    <form id="gallery-form" method="POST" action="{{ route('admin.galleries.update', $gallery) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Title -->
                        <div class="form-group">
                            <label for="title">Gallery Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $gallery->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                   id="slug" name="slug" value="{{ old('slug', $gallery->slug) }}">
                            <small class="form-text text-muted">Leave empty to auto-generate from title</small>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $gallery->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Category -->
                        <div class="form-group">
                            <label for="category">Category</label>
                            <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                   id="category" name="category" value="{{ old('category', $gallery->category) }}" list="categories">
                            <datalist id="categories">
                                @foreach($categories as $category)
                                    <option value="{{ $category }}">
                                @endforeach
                            </datalist>
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Additional Images Upload -->
                        <div class="form-group">
                            <label for="images">Add More Images</label>
                            <input type="file" class="form-control-file @error('images.*') is-invalid @enderror" 
                                   id="images" name="images[]" accept="image/*" multiple>
                            <small class="form-text text-muted">Select additional images to add to this gallery (max 5MB each)</small>
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Image Preview -->
                        <div id="image-preview" class="row mt-3" style="display: none;"></div>

                        <div class="form-group text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Gallery
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Existing Images Management -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Gallery Images ({{ $gallery->images->count() }})</h6>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleImageSelection()">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                Bulk Actions
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="#" onclick="bulkImageAction('feature')">Feature Selected</a>
                                <a class="dropdown-item" href="#" onclick="bulkImageAction('unfeature')">Unfeature Selected</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-danger" href="#" onclick="bulkImageAction('delete')">Delete Selected</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($gallery->images->count() > 0)
                        <div id="sortable-images" class="row">
                            @foreach($gallery->images as $image)
                                <div class="col-md-3 mb-4 image-item" data-id="{{ $image->id }}">
                                    <div class="card h-100">
                                        <div class="position-relative">
                                            <input type="checkbox" class="image-checkbox position-absolute" style="top: 10px; left: 10px; z-index: 10;" value="{{ $image->id }}">
                                            
                                            <img src="{{ $image->thumbnail_url }}" alt="{{ $image->title }}" 
                                                 class="card-img-top" style="height: 200px; object-fit: cover; cursor: move;">
                                            
                                            <!-- Image badges -->
                                            <div class="position-absolute" style="top: 10px; right: 10px;">
                                                @if($image->is_featured)
                                                    <span class="badge badge-primary mb-1">Featured</span><br>
                                                @endif
                                                <span class="badge badge-secondary">{{ $image->sort_order }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="card-body p-2">
                                            <input type="text" class="form-control form-control-sm mb-2" 
                                                   placeholder="Image title" value="{{ $image->title }}" 
                                                   onchange="updateImageField({{ $image->id }}, 'title', this.value)">
                                            <textarea class="form-control form-control-sm mb-2" rows="2" 
                                                      placeholder="Caption" 
                                                      onchange="updateImageField({{ $image->id }}, 'caption', this.value)">{{ $image->caption }}</textarea>
                                            <input type="text" class="form-control form-control-sm mb-2" 
                                                   placeholder="Alt text" value="{{ $image->alt_text }}" 
                                                   onchange="updateImageField({{ $image->id }}, 'alt_text', this.value)">
                                        </div>
                                        
                                        <div class="card-footer p-2">
                                            <div class="btn-group w-100" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="setCoverImage({{ $image->id }})" title="Set as Cover">
                                                    <i class="fas fa-image"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        onclick="toggleImageFeature({{ $image->id }})" title="Toggle Featured">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteImage({{ $image->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No images in this gallery</h5>
                            <p class="text-muted">Upload some images to get started.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Publishing Options -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Publishing Options</h6>
                </div>
                <div class="card-body">
                    <!-- Published -->
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="is_published" 
                                   name="is_published" value="1" {{ old('is_published', $gallery->is_published) ? 'checked' : '' }} form="gallery-form">
                            <label class="custom-control-label" for="is_published">Published</label>
                        </div>
                    </div>

                    <!-- Featured -->
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="is_featured" 
                                   name="is_featured" value="1" {{ old('is_featured', $gallery->is_featured) ? 'checked' : '' }} form="gallery-form">
                            <label class="custom-control-label" for="is_featured">Featured Gallery</label>
                        </div>
                    </div>

                    <!-- Sort Order -->
                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $gallery->sort_order) }}" form="gallery-form">
                        @error('sort_order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Cover Image -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Cover Image</h6>
                    @if($gallery->cover_image)
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeCoverImage()">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    @endif
                </div>
                <div class="card-body">
                    @if($gallery->cover_image)
                        <div id="current-cover" class="mb-3">
                            <img src="{{ $gallery->cover_image_url }}" alt="{{ $gallery->title }}" class="img-fluid rounded">
                        </div>
                    @endif
                    
                    <div class="form-group">
                        <input type="file" class="form-control-file @error('cover_image') is-invalid @enderror" 
                               id="cover_image" name="cover_image" accept="image/*" form="gallery-form">
                        <small class="form-text text-muted">Upload new cover image or use "Set as Cover" on gallery images</small>
                        @error('cover_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div id="cover-preview" class="mt-3" style="display: none;">
                        <img id="cover-img" src="" alt="Cover Preview" class="img-fluid rounded">
                    </div>
                </div>
            </div>

            <!-- SEO Meta -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">SEO Meta</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="meta_title">Meta Title</label>
                        <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                               id="meta_title" name="meta_title" value="{{ old('meta_title', $gallery->meta_title) }}" form="gallery-form">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="meta_description">Meta Description</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                  id="meta_description" name="meta_description" rows="3" form="gallery-form">{{ old('meta_description', $gallery->meta_description) }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label for="meta_keywords">Meta Keywords</label>
                        <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror" 
                               id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords', $gallery->meta_keywords) }}" form="gallery-form">
                        <small class="form-text text-muted">Separate keywords with commas</small>
                        @error('meta_keywords')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
<style>
.image-item {
    cursor: move;
}
.image-item.ui-sortable-helper {
    transform: rotate(5deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
.image-checkbox {
    width: 20px;
    height: 20px;
}
</style>
@endpush

@push('scripts')
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from title
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    
    titleInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.value === slugInput.defaultValue) {
            const slug = this.value.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
        }
    });
    
    // Cover image preview
    const coverInput = document.getElementById('cover_image');
    const coverPreview = document.getElementById('cover-preview');
    const coverImg = document.getElementById('cover-img');
    const currentCover = document.getElementById('current-cover');
    
    coverInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                coverImg.src = e.target.result;
                coverPreview.style.display = 'block';
                if (currentCover) {
                    currentCover.style.display = 'none';
                }
            };
            reader.readAsDataURL(file);
        } else {
            coverPreview.style.display = 'none';
            if (currentCover) {
                currentCover.style.display = 'block';
            }
        }
    });
    
    // Additional images preview
    const imagesInput = document.getElementById('images');
    const imagePreview = document.getElementById('image-preview');
    
    imagesInput.addEventListener('change', function() {
        imagePreview.innerHTML = '';
        const files = Array.from(this.files);
        
        if (files.length > 0) {
            imagePreview.style.display = 'flex';
            
            files.forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const col = document.createElement('div');
                        col.className = 'col-md-3 mb-3';
                        col.innerHTML = `
                            <div class="card">
                                <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;">
                                <div class="card-body p-2">
                                    <small class="text-muted">${file.name}</small>
                                </div>
                            </div>
                        `;
                        imagePreview.appendChild(col);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            imagePreview.style.display = 'none';
        }
    });
    
    // Initialize sortable
    $("#sortable-images").sortable({
        items: ".image-item",
        placeholder: "col-md-3 mb-4",
        update: function(event, ui) {
            updateImageOrder();
        }
    });
});

// Image management functions
function updateImageField(imageId, field, value) {
    fetch(`/admin/gallery-images/${imageId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            [field]: value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('Error updating image: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating image');
    });
}

function updateImageOrder() {
    const items = [];
    document.querySelectorAll('#sortable-images .image-item').forEach((item, index) => {
        items.push({
            id: parseInt(item.dataset.id),
            sort_order: index
        });
    });
    
    fetch(`/admin/galleries/{{ $gallery->id }}/images/update-order`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            images: items
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('Error updating order: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating order');
    });
}

function setCoverImage(imageId) {
    fetch(`/admin/galleries/{{ $gallery->id }}/images/${imageId}/set-cover`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error setting cover: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error setting cover');
    });
}

function toggleImageFeature(imageId) {
    // This would toggle the featured status
    updateImageField(imageId, 'is_featured', !document.querySelector(`[data-id="${imageId}"] .badge-primary`));
}

function deleteImage(imageId) {
    if (confirm('Are you sure you want to delete this image?')) {
        fetch(`/admin/gallery-images/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelector(`[data-id="${imageId}"]`).remove();
            } else {
                alert('Error deleting image: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting image');
        });
    }
}

function toggleImageSelection() {
    const checkboxes = document.querySelectorAll('.image-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
}

function bulkImageAction(action) {
    const selectedImages = Array.from(document.querySelectorAll('.image-checkbox:checked')).map(cb => cb.value);
    
    if (selectedImages.length === 0) {
        alert('Please select at least one image.');
        return;
    }
    
    if (action === 'delete' && !confirm('Are you sure you want to delete the selected images?')) {
        return;
    }
    
    fetch(`/admin/galleries/{{ $gallery->id }}/images/bulk-action`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            action: action,
            images: selectedImages
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error performing bulk action');
    });
}

function removeCoverImage() {
    if (confirm('Are you sure you want to remove the cover image?')) {
        fetch(`/admin/galleries/{{ $gallery->id }}/remove-cover-image`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing cover: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing cover');
        });
    }
}
</script>
@endpush
@endsection
