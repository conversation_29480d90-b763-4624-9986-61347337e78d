@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 rounded-full" style="background-color: {{ $tag->color }};"></div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $tag->name }}</h1>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('admin.tags.edit', $tag) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Tag
                </a>
                <a href="{{ route('admin.tags.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Tags
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Tag Preview -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Tag Preview</h3>
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full text-white" style="background-color: {{ $tag->color }};">
                        {{ $tag->name }}
                    </span>
                </div>

                <!-- Recent Blog Posts -->
                @if($recentPosts->count() > 0)
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Blog Posts ({{ $tag->blog_posts_count }} total)</h3>
                    <div class="space-y-4">
                        @foreach($recentPosts as $post)
                        <div class="border-l-4 {{ $post->is_published ? 'border-green-400' : 'border-gray-300' }} pl-4">
                            <div class="flex items-center justify-between">
                                <h4 class="font-semibold text-gray-900">
                                    <a href="{{ route('admin.blog-posts.show', $post) }}" class="hover:text-green-600">
                                        {{ Str::limit($post->title, 50) }}
                                    </a>
                                </h4>
                                <span class="text-sm text-gray-500">{{ $post->created_at->format('M d, Y') }}</span>
                            </div>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" style="background-color: {{ $post->category->color }}20; color: {{ $post->category->color }};">
                                    {{ $post->category->name }}
                                </span>
                                <span class="text-xs text-gray-500">By {{ $post->user->name }}</span>
                                @if($post->is_published)
                                    <span class="text-xs text-green-600">Published</span>
                                @else
                                    <span class="text-xs text-gray-500">Draft</span>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    @if($tag->blog_posts_count > 10)
                        <div class="mt-4 text-center">
                            <a href="{{ route('admin.blog-posts.index', ['tag' => $tag->id]) }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                View all {{ $tag->blog_posts_count }} posts with this tag →
                            </a>
                        </div>
                    @endif
                </div>
                @else
                <div class="bg-gray-50 p-6 rounded-lg text-center">
                    <div class="text-gray-400 text-4xl mb-3">📝</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No blog posts yet</h3>
                    <p class="text-gray-500 mb-4">This tag hasn't been used in any blog posts yet.</p>
                    <a href="{{ route('admin.blog-posts.create') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Create New Post
                    </a>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Tag Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tag Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Name:</span>
                            <div class="text-gray-900 font-medium">{{ $tag->name }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Slug:</span>
                            <div class="text-gray-900 font-mono">{{ $tag->slug }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Color:</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-4 h-4 rounded-full" style="background-color: {{ $tag->color }};"></div>
                                <span class="text-gray-900 font-mono">{{ $tag->color }}</span>
                            </div>
                        </div>
                        <div>
                            <span class="text-gray-600">Blog Posts:</span>
                            <div class="text-gray-900 font-medium">{{ $tag->blog_posts_count }} posts</div>
                        </div>
                    </div>
                </div>

                <!-- Tag Statistics -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Created:</span>
                            <div class="text-gray-900">{{ $tag->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $tag->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @if($tag->updated_at != $tag->created_at)
                        <div>
                            <span class="text-gray-600">Last Modified:</span>
                            <div class="text-gray-900">{{ $tag->updated_at->diffForHumans() }}</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.tags.edit', $tag) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Tag
                        </a>
                        
                        <form action="{{ route('admin.tags.destroy', $tag) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this tag? It will be removed from all blog posts.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Delete Tag
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
