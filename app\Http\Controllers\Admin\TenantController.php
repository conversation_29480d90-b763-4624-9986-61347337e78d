<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use App\Services\TenantService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TenantController extends Controller
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Display a listing of tenants
     */
    public function index(Request $request)
    {
        $query = Tenant::with(['owner', 'creator']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('domain', 'like', "%{$search}%")
                  ->orWhere('subdomain', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true)->where('is_suspended', false);
                    break;
                case 'suspended':
                    $query->where('is_suspended', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'trial':
                    $query->whereNotNull('trial_ends_at')->where('trial_ends_at', '>', now());
                    break;
                case 'expired':
                    $query->where(function ($q) {
                        $q->where('trial_ends_at', '<=', now())
                          ->orWhere('subscription_ends_at', '<=', now());
                    });
                    break;
            }
        }

        $tenants = $query->orderBy('created_at', 'desc')->paginate(20);
        $statistics = $this->tenantService->getTenantStatistics();

        return view('admin.tenants.index', compact('tenants', 'statistics'));
    }

    /**
     * Show the form for creating a new tenant
     */
    public function create()
    {
        $users = User::where('role', 'admin')->orWhere('role', 'user')->get();
        return view('admin.tenants.create', compact('users'));
    }

    /**
     * Store a newly created tenant
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'domain' => 'nullable|string|unique:tenants,domain',
            'subdomain' => 'nullable|string|unique:tenants,subdomain|regex:/^[a-z0-9-]+$/',
            'owner_id' => 'nullable|exists:users,id',
            'trial_days' => 'nullable|integer|min:0|max:365',
            'admin_user.name' => 'required_with:admin_user.email|string|max:255',
            'admin_user.email' => 'required_with:admin_user.name|email|unique:users,email',
            'admin_user.password' => 'required_with:admin_user.email|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->only(['name', 'domain', 'subdomain', 'owner_id']);

        if ($request->filled('trial_days')) {
            $data['trial_ends_at'] = now()->addDays($request->trial_days);
        }

        if ($request->filled('admin_user.email')) {
            $data['admin_user'] = $request->admin_user;
        }

        $result = $this->tenantService->createTenant($data);

        if ($result['success']) {
            return redirect()->route('admin.tenants.show', $result['tenant'])
                ->with('success', $result['message']);
        } else {
            return back()->withErrors(['error' => $result['message']])->withInput();
        }
    }

    /**
     * Display the specified tenant
     */
    public function show(Tenant $tenant)
    {
        $tenant->load(['owner', 'creator']);

        // Get tenant-specific statistics
        $stats = $this->tenantService->runInTenantContext($tenant, function () {
            return [
                'users_count' => User::count(),
                'events_count' => \App\Models\Event::count(),
                'galleries_count' => \App\Models\Gallery::count(),
                'menu_items_count' => \App\Models\MenuItem::count(),
            ];
        });

        return view('admin.tenants.show', compact('tenant', 'stats'));
    }

    /**
     * Show the form for editing the specified tenant
     */
    public function edit(Tenant $tenant)
    {
        $users = User::where('role', 'admin')->orWhere('role', 'user')->get();
        return view('admin.tenants.edit', compact('tenant', 'users'));
    }

    /**
     * Update the specified tenant
     */
    public function update(Request $request, Tenant $tenant)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'domain' => 'nullable|string|unique:tenants,domain,' . $tenant->id,
            'subdomain' => 'nullable|string|unique:tenants,subdomain,' . $tenant->id . '|regex:/^[a-z0-9-]+$/',
            'owner_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
            'is_suspended' => 'boolean',
            'trial_ends_at' => 'nullable|date',
            'subscription_ends_at' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $result = $this->tenantService->updateTenant($tenant, $request->validated());

        if ($result['success']) {
            return redirect()->route('admin.tenants.show', $tenant)
                ->with('success', $result['message']);
        } else {
            return back()->withErrors(['error' => $result['message']])->withInput();
        }
    }

    /**
     * Remove the specified tenant
     */
    public function destroy(Tenant $tenant)
    {
        $result = $this->tenantService->deleteTenant($tenant);

        if ($result['success']) {
            return redirect()->route('admin.tenants.index')
                ->with('success', $result['message']);
        } else {
            return back()->withErrors(['error' => $result['message']]);
        }
    }

    /**
     * Suspend tenant
     */
    public function suspend(Request $request, Tenant $tenant)
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $result = $this->tenantService->suspendTenant($tenant, $request->reason);

        if ($result['success']) {
            return back()->with('success', $result['message']);
        } else {
            return back()->withErrors(['error' => $result['message']]);
        }
    }

    /**
     * Unsuspend tenant
     */
    public function unsuspend(Tenant $tenant)
    {
        $result = $this->tenantService->unsuspendTenant($tenant);

        if ($result['success']) {
            return back()->with('success', $result['message']);
        } else {
            return back()->withErrors(['error' => $result['message']]);
        }
    }

    /**
     * Switch to tenant context (for super admin)
     */
    public function switchContext(Tenant $tenant)
    {
        if (!$tenant->isAccessible()) {
            return back()->withErrors(['error' => 'Tenant is not accessible']);
        }

        // Store original context in session
        session(['original_context' => [
            'user_id' => auth()->id(),
            'is_super_admin' => true,
        ]]);

        // Switch to tenant context
        $this->tenantService->switchToTenant($tenant);

        // Redirect to tenant admin dashboard
        $tenantUrl = $tenant->domain
            ? "https://{$tenant->domain}/admin"
            : "https://{$tenant->subdomain}." . config('app.domain') . "/admin";

        return redirect()->to($tenantUrl);
    }
}
