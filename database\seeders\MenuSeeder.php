<?php

namespace Database\Seeders;

use App\Models\MenuItem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing menu items
        MenuItem::truncate();

        // Main Navigation Menu
        $mainMenuItems = [
            [
                'title' => 'Home',
                'route_name' => 'home',
                'menu_location' => 'main',
                'sort_order' => 1,
                'is_active' => true,
                'icon' => 'fas fa-home',
            ],
            [
                'title' => 'About',
                'route_name' => 'about',
                'menu_location' => 'main',
                'sort_order' => 2,
                'is_active' => true,
                'icon' => 'fas fa-info-circle',
                'children' => [
                    [
                        'title' => 'Our Team',
                        'route_name' => 'team.index',
                        'menu_location' => 'main',
                        'sort_order' => 1,
                        'is_active' => true,
                    ],
                    [
                        'title' => 'Testimonials',
                        'route_name' => 'testimonials.index',
                        'menu_location' => 'main',
                        'sort_order' => 2,
                        'is_active' => true,
                    ],
                ]
            ],
            [
                'title' => 'Services',
                'route_name' => 'services.index',
                'menu_location' => 'main',
                'sort_order' => 3,
                'is_active' => true,
                'icon' => 'fas fa-cogs',
            ],
            [
                'title' => 'Projects',
                'route_name' => 'projects.index',
                'menu_location' => 'main',
                'sort_order' => 4,
                'is_active' => true,
                'icon' => 'fas fa-briefcase',
            ],
            [
                'title' => 'Events',
                'route_name' => 'events.index',
                'menu_location' => 'main',
                'sort_order' => 5,
                'is_active' => true,
                'icon' => 'fas fa-calendar',
            ],
            [
                'title' => 'Gallery',
                'route_name' => 'gallery.index',
                'menu_location' => 'main',
                'sort_order' => 6,
                'is_active' => true,
                'icon' => 'fas fa-images',
            ],
            [
                'title' => 'Contact',
                'route_name' => 'contact',
                'menu_location' => 'main',
                'sort_order' => 7,
                'is_active' => true,
                'icon' => 'fas fa-envelope',
            ],
        ];

        // Footer Navigation Menu
        $footerMenuItems = [
            [
                'title' => 'Home',
                'route_name' => 'home',
                'menu_location' => 'footer',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'About',
                'route_name' => 'about',
                'menu_location' => 'footer',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'Services',
                'route_name' => 'services.index',
                'menu_location' => 'footer',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'title' => 'Events',
                'route_name' => 'events.index',
                'menu_location' => 'footer',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'title' => 'Gallery',
                'route_name' => 'gallery.index',
                'menu_location' => 'footer',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'title' => 'Contact',
                'route_name' => 'contact',
                'menu_location' => 'footer',
                'sort_order' => 6,
                'is_active' => true,
            ],
        ];

        // Create main menu items
        foreach ($mainMenuItems as $itemData) {
            $children = $itemData['children'] ?? [];
            unset($itemData['children']);

            $menuItem = MenuItem::create($itemData);

            // Create children if they exist
            foreach ($children as $childData) {
                $childData['parent_id'] = $menuItem->id;
                MenuItem::create($childData);
            }
        }

        // Create footer menu items
        foreach ($footerMenuItems as $itemData) {
            MenuItem::create($itemData);
        }

        $this->command->info('Menu items seeded successfully!');
    }
}
