<?php

use Illuminate\Support\Facades\Password;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.auth-minimal')] class extends Component
{
    public string $email = '';

    /**
     * Send a password reset link to the provided email address.
     */
    public function sendPasswordResetLink(): void
    {
        $this->validate([
            'email' => ['required', 'string', 'email'],
        ]);

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
        $status = Password::sendResetLink(
            $this->only('email')
        );

        if ($status != Password::RESET_LINK_SENT) {
            $this->addError('email', __($status));

            return;
        }

        $this->reset('email');

        session()->flash('status', __($status));
    }
}; ?>

<div>
    <!-- Session Status -->
    <x-auth-session-status :status="session('status')" />

    <!-- Welcome Message -->
    <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Reset Password</h2>
        <p class="text-gray-600">Enter your email to receive a reset link</p>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-600 mr-3 mt-0.5"></i>
            <p class="text-sm text-blue-800">
                {{ __('Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.') }}
            </p>
        </div>
    </div>

    <form wire:submit="sendPasswordResetLink" class="space-y-6">
        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email Address')" />
            <x-text-input wire:model="email" id="email" type="email" name="email" placeholder="Enter your email address" required autofocus />
            <x-input-error :messages="$errors->get('email')" />
        </div>

        <!-- Send Reset Link Button -->
        <div class="pt-4">
            <x-primary-button>
                <i class="fas fa-paper-plane mr-2"></i>
                {{ __('Send Reset Link') }}
            </x-primary-button>
        </div>

        <!-- Back to Login Link -->
        <div class="text-center pt-6 border-t border-gray-200">
            <p class="text-gray-600">
                Remember your password?
                <a href="{{ route('login') }}" class="text-green-600 hover:text-green-800 font-semibold transition-colors duration-200" wire:navigate>
                    Back to login
                </a>
            </p>
        </div>
    </form>
</div>
