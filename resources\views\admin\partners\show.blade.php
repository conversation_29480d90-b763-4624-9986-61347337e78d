@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{{ ucfirst($partner->type) }} Details</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.partners.edit', $partner) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Edit
            </a>
            <a href="{{ route('admin.partners.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Partners & Clients
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Logo -->
                <div class="lg:col-span-1">
                    @if($partner->logo_url)
                        <div class="bg-gray-50 rounded-lg p-6 flex items-center justify-center">
                            <img src="{{ $partner->logo_url }}" alt="{{ $partner->name }}" class="max-w-full max-h-48 object-contain">
                        </div>
                    @else
                        <div class="bg-gray-200 rounded-lg p-6 flex items-center justify-center h-48">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-gray-400 rounded-lg mx-auto mb-4 flex items-center justify-center">
                                    <span class="text-2xl font-bold text-white">{{ substr($partner->name, 0, 1) }}</span>
                                </div>
                                <p class="text-gray-500">No logo uploaded</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Partner Info -->
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <!-- Basic Info -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $partner->name }}</h2>
                            
                            <div class="flex flex-wrap gap-4 mb-4">
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $partner->type === 'partner' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                    {{ $partner->type_name }}
                                </span>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $partner->is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $partner->is_featured ? 'Featured' : 'Regular' }}
                                </span>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $partner->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $partner->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <!-- Details -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if($partner->industry)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Industry</h3>
                                <p class="text-gray-700">{{ $partner->industry }}</p>
                            </div>
                            @endif

                            @if($partner->website)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Website</h3>
                                <a href="{{ $partner->website }}" target="_blank" class="text-green-600 hover:text-green-800 break-all">
                                    {{ $partner->website }}
                                </a>
                            </div>
                            @endif
                        </div>

                        <!-- Description -->
                        @if($partner->description)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                            <div class="prose max-w-none">
                                <p class="text-gray-700 leading-relaxed whitespace-pre-line">{{ $partner->description }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Metadata -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                    <span class="font-medium">Type:</span> {{ $partner->type_name }}
                                </div>
                                <div>
                                    <span class="font-medium">Sort Order:</span> {{ $partner->sort_order }}
                                </div>
                                <div>
                                    <span class="font-medium">Created:</span> {{ $partner->created_at->format('M j, Y g:i A') }}
                                </div>
                                <div>
                                    <span class="font-medium">Updated:</span> {{ $partner->updated_at->format('M j, Y g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-gray-50 px-6 py-4 border-t">
            <div class="flex justify-between items-center">
                <div class="flex space-x-2">
                    <form action="{{ route('admin.partners.toggle-featured', $partner) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                            {{ $partner->is_featured ? 'Unfeature' : 'Feature' }}
                        </button>
                    </form>
                    
                    <form action="{{ route('admin.partners.toggle-status', $partner) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            {{ $partner->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>
                </div>
                
                <form action="{{ route('admin.partners.destroy', $partner) }}" method="POST" class="inline" 
                      onsubmit="return confirm('Are you sure you want to delete this {{ $partner->type }}? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Delete {{ ucfirst($partner->type) }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
