<?php

use App\Livewire\Forms\LoginForm;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.auth-minimal')] class extends Component
{
    public LoginForm $form;

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->form->authenticate();

        Session::regenerate();

        // Redirect based on user role
        $user = auth()->user();

        if ($user && $user->role === 'admin') {
            $this->redirect(route('admin.dashboard'), navigate: false);
        } else {
            $this->redirect(route('dashboard'), navigate: false);
        }
    }
}; ?>

<div>
    <!-- Session Status -->
    <x-auth-session-status :status="session('status')" />

    <form wire:submit="login" class="space-y-6">
        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email Address')" />
            <x-text-input wire:model="form.email" id="email" type="email" name="email" placeholder="Enter your email address" required autofocus autocomplete="username" />
            <x-input-error :messages="$errors->get('form.email')" />
        </div>

        <!-- Password -->
        <div>
            <x-input-label for="password" :value="__('Password')" />
            <x-text-input wire:model="form.password" id="password" type="password" name="password" placeholder="Enter your password" required autocomplete="current-password" />
            <x-input-error :messages="$errors->get('form.password')" />
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
            <label for="remember" class="inline-flex items-center">
                <input wire:model="form.remember" id="remember" type="checkbox" class="rounded border-gray-300 text-green-600 shadow-sm focus:ring-green-500 focus:ring-opacity-20" name="remember">
                <span class="ml-2 text-sm text-gray-600 font-medium">{{ __('Remember me') }}</span>
            </label>

            @if (Route::has('password.request'))
                <a class="text-sm text-green-600 hover:text-green-800 font-medium transition-colors duration-200" href="{{ route('password.request') }}" wire:navigate>
                    {{ __('Forgot password?') }}
                </a>
            @endif
        </div>

        <!-- Login Button -->
        <div class="pt-4">
            <x-primary-button>
                <i class="fas fa-sign-in-alt mr-2"></i>
                {{ __('Sign In') }}
            </x-primary-button>
        </div>

        <!-- Register Link -->
        {{-- @if (Route::has('register'))
            <div class="text-center pt-6 border-t border-gray-200">
                <p class="text-gray-600">
                    Don't have an account?
                    <a href="{{ route('register') }}" class="text-green-600 hover:text-green-800 font-semibold transition-colors duration-200" wire:navigate>
                        Create one here
                    </a>
                </p>
            </div>
        @endif --}}
    </form>
</div>
