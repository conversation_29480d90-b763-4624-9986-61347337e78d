<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use App\Livewire\Pages\Auth\Login;
use App\Livewire\Pages\Auth\Register;
use App\Livewire\Pages\Auth\ForgotPassword;
use App\Livewire\Pages\Auth\ResetPassword;
use App\Livewire\Pages\Auth\VerifyEmail;
use App\Livewire\Pages\Auth\ConfirmPassword;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Livewire components
        Livewire::component('pages.auth.login', Login::class);
        Livewire::component('pages.auth.register', Register::class);
        Livewire::component('pages.auth.forgot-password', ForgotPassword::class);
        Livewire::component('pages.auth.reset-password', ResetPassword::class);
        Livewire::component('pages.auth.verify-email', VerifyEmail::class);
        Livewire::component('pages.auth.confirm-password', ConfirmPassword::class);
        
        // Other boot code...
    }
}


