@extends('layouts.public')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">Our Partners</h1>
                <p class="text-xl text-green-100 max-w-3xl mx-auto">
                    We collaborate with industry leaders and innovative companies to deliver exceptional value to our clients.
                </p>
            </div>
        </div>
    </div>

    <!-- Featured Partners Section -->
    @if($featuredPartners->count() > 0)
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Partners</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Our strategic partnerships enable us to provide comprehensive solutions and cutting-edge services.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                @foreach($featuredPartners as $partner)
                <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 border border-gray-200">
                    <div class="text-center">
                        @if($partner->logo_url)
                            <div class="mb-4">
                                <img src="{{ $partner->logo_url }}" alt="{{ $partner->name }}" class="h-16 w-auto mx-auto object-contain">
                            </div>
                        @endif
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $partner->name }}</h3>
                        @if($partner->industry)
                            <p class="text-sm text-green-600 font-medium mb-3">{{ $partner->industry }}</p>
                        @endif
                        @if($partner->description)
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit($partner->description, 120) }}</p>
                        @endif
                        <div class="flex justify-center space-x-3">
                            @if($partner->website)
                                <a href="{{ $partner->website }}" target="_blank" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            @endif
                            <a href="{{ route('partners.show', $partner) }}" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                <i class="fas fa-info-circle"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- All Partners Section -->
    @if($partners->count() > 0)
    <div class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">All Partners</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Discover our complete network of trusted partners and collaborators.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($partners as $partner)
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-200">
                    <div class="text-center">
                        @if($partner->logo_url)
                            <div class="mb-4">
                                <img src="{{ $partner->logo_url }}" alt="{{ $partner->name }}" class="h-12 w-auto mx-auto object-contain">
                            </div>
                        @endif
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $partner->name }}</h3>
                        @if($partner->industry)
                            <p class="text-sm text-green-600 font-medium mb-3">{{ $partner->industry }}</p>
                        @endif
                        @if($partner->description)
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ Str::limit($partner->description, 100) }}</p>
                        @endif
                        <div class="flex justify-center space-x-3">
                            @if($partner->website)
                                <a href="{{ $partner->website }}" target="_blank" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            @endif
                            <a href="{{ route('partners.show', $partner) }}" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                <i class="fas fa-info-circle"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Empty State -->
    @if($partners->count() === 0 && $featuredPartners->count() === 0)
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="max-w-md mx-auto">
                <i class="fas fa-handshake text-6xl text-gray-300 mb-6"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Partners Yet</h3>
                <p class="text-gray-600">We're building our partner network. Check back soon for updates!</p>
            </div>
        </div>
    </div>
    @endif

    <!-- Call to Action -->
    <div class="bg-green-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">Interested in Partnering with Us?</h2>
            <p class="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
                Join our network of trusted partners and let's create something amazing together.
            </p>
            <a href="{{ route('contact') }}" class="inline-flex items-center px-8 py-3 bg-white text-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-300">
                <i class="fas fa-envelope mr-2"></i>
                Get in Touch
            </a>
        </div>
    </div>
</div>
@endsection
