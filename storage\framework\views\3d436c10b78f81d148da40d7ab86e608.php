
<?php if(isset($footerMenu) && $footerMenu && $footerMenu->count() > 0): ?>
    <?php $__currentLoopData = $footerMenu; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li>
            <?php
                try {
                    $itemUrl = $item->full_url;
                } catch (\Exception $e) {
                    $itemUrl = '#';
                }
            ?>
            <a href="<?php echo e($itemUrl); ?>" target="<?php echo e($item->target ?? '_self'); ?>"
               class="text-gray-300 hover:text-green-400 transition-colors duration-300 <?php echo e($item->css_class ?? ''); ?>">
                <?php if($item->icon): ?>
                    <i class="<?php echo e($item->icon); ?> mr-1"></i>
                <?php endif; ?>
                <?php echo e($item->title); ?>

            </a>

            <?php if($item->children->count() > 0): ?>
                <ul class="mt-2 ml-4 space-y-1">
                    <?php $__currentLoopData = $item->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <?php
                                try {
                                    $childUrl = $child->full_url;
                                } catch (\Exception $e) {
                                    $childUrl = '#';
                                }
                            ?>
                            <a href="<?php echo e($childUrl); ?>" target="<?php echo e($child->target ?? '_self'); ?>"
                               class="text-gray-400 hover:text-green-400 text-sm transition-colors duration-300 <?php echo e($child->css_class ?? ''); ?>">
                                <?php if($child->icon): ?>
                                    <i class="<?php echo e($child->icon); ?> mr-1"></i>
                                <?php endif; ?>
                                <?php echo e($child->title); ?>

                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    
    <li><a href="<?php echo e(route('home')); ?>" class="text-gray-300 hover:text-green-400">Home</a></li>
    <li><a href="<?php echo e(route('about')); ?>" class="text-gray-300 hover:text-green-400">About</a></li>
    <li><a href="<?php echo e(route('services.index')); ?>" class="text-gray-300 hover:text-green-400">Services</a></li>
    <li><a href="<?php echo e(route('events.index')); ?>" class="text-gray-300 hover:text-green-400">Events</a></li>
    <li><a href="<?php echo e(route('gallery.index')); ?>" class="text-gray-300 hover:text-green-400">Gallery</a></li>
    <li><a href="<?php echo e(route('partners.index')); ?>" class="text-gray-300 hover:text-green-400">Partners</a></li>
    <li><a href="<?php echo e(route('clients.index')); ?>" class="text-gray-300 hover:text-green-400">Clients</a></li>
    <li><a href="<?php echo e(route('testimonials.index')); ?>" class="text-gray-300 hover:text-green-400">Testimonials</a></li>
    <li><a href="<?php echo e(route('faqs.index')); ?>" class="text-gray-300 hover:text-green-400">FAQs</a></li>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/partials/footer-navigation.blade.php ENDPATH**/ ?>