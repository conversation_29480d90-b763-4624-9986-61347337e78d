<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use App\Services\TenantService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TenantMiddleware
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip tenant resolution for certain routes
        if ($this->shouldSkipTenantResolution($request)) {
            return $next($request);
        }

        // Resolve tenant from request
        $tenant = $this->resolveTenant($request);

        if (!$tenant) {
            return $this->handleTenantNotFound($request);
        }

        if (!$tenant->isAccessible()) {
            return $this->handleTenantNotAccessible($request, $tenant);
        }

        // Set current tenant
        $this->tenantService->setCurrentTenant($tenant);

        // Configure tenant database connection
        $tenant->configureDatabaseConnection();

        // Add tenant to request
        $request->attributes->set('tenant', $tenant);

        return $next($request);
    }

    /**
     * Resolve tenant from request
     */
    protected function resolveTenant(Request $request): ?Tenant
    {
        $host = $request->getHost();

        // Check for exact domain match first
        $tenant = Tenant::where('domain', $host)
            ->where('is_active', true)
            ->first();

        if ($tenant) {
            return $tenant;
        }

        // Check for subdomain match
        $parts = explode('.', $host);
        if (count($parts) >= 2) {
            $subdomain = $parts[0];

            // Skip common subdomains
            if (!in_array($subdomain, ['www', 'mail', 'ftp', 'admin', 'api'])) {
                return Tenant::where('subdomain', $subdomain)
                    ->where('is_active', true)
                    ->first();
            }
        }

        return null;
    }

    /**
     * Check if tenant resolution should be skipped
     */
    protected function shouldSkipTenantResolution(Request $request): bool
    {
        $skipRoutes = [
            'installer.*',
            'admin.tenants.*',
            'api.tenants.*',
            'health-check',
            'horizon.*',
            'telescope.*',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->routeIs($pattern)) {
                return true;
            }
        }

        // Skip for main domain admin access
        $mainDomain = config('app.main_domain');
        if ($mainDomain && $request->getHost() === $mainDomain && $request->is('admin/*')) {
            return true;
        }

        return false;
    }

    /**
     * Handle tenant not found
     */
    protected function handleTenantNotFound(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Tenant not found',
                'message' => 'The requested tenant could not be found or is not active.',
            ], 404);
        }

        // Redirect to main domain or show tenant not found page
        $mainDomain = config('app.main_domain');
        if ($mainDomain && $request->getHost() !== $mainDomain) {
            return redirect()->to("https://{$mainDomain}");
        }

        abort(404, 'Tenant not found');
    }

    /**
     * Handle tenant not accessible
     */
    protected function handleTenantNotAccessible(Request $request, Tenant $tenant): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Tenant not accessible',
                'message' => $this->getTenantNotAccessibleMessage($tenant),
                'tenant_status' => [
                    'is_suspended' => $tenant->is_suspended,
                    'is_active' => $tenant->is_active,
                    'trial_ends_at' => $tenant->trial_ends_at,
                    'subscription_ends_at' => $tenant->subscription_ends_at,
                ],
            ], 403);
        }

        // Show tenant-specific error page
        return response()->view('tenant.not-accessible', [
            'tenant' => $tenant,
            'message' => $this->getTenantNotAccessibleMessage($tenant),
        ], 403);
    }

    /**
     * Get tenant not accessible message
     */
    protected function getTenantNotAccessibleMessage(Tenant $tenant): string
    {
        if ($tenant->is_suspended) {
            return 'This site has been temporarily suspended. Please contact support for assistance.';
        }

        if (!$tenant->is_active) {
            return 'This site is currently inactive. Please contact the site administrator.';
        }

        if ($tenant->subscription_ends_at && $tenant->subscription_ends_at->isPast()) {
            return 'This site\'s subscription has expired. Please renew to continue accessing the site.';
        }

        return 'This site is currently not accessible. Please try again later.';
    }
}
