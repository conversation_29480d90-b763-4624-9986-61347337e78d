<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SettingsService
{
    /**
     * Default settings configuration
     */
    protected $defaultSettings = [
        'general' => [
            'site_name' => [
                'label' => 'Site Name',
                'type' => 'string',
                'default' => 'Laravel SaaS Platform',
                'validation' => ['required', 'string', 'max:255'],
                'description' => 'The name of your website',
            ],
            'site_description' => [
                'label' => 'Site Description',
                'type' => 'text',
                'default' => 'A powerful SaaS platform built with <PERSON><PERSON>',
                'validation' => ['nullable', 'string', 'max:500'],
                'description' => 'A brief description of your website',
            ],
            'site_logo' => [
                'label' => 'Site Logo',
                'type' => 'image',
                'default' => '',
                'validation' => ['nullable', 'string'],
                'description' => 'URL to your site logo',
            ],
            'site_favicon' => [
                'label' => 'Site Favicon',
                'type' => 'image',
                'default' => '',
                'validation' => ['nullable', 'string'],
                'description' => 'URL to your site favicon',
            ],
            'timezone' => [
                'label' => 'Timezone',
                'type' => 'select',
                'default' => 'UTC',
                'options' => 'timezones',
                'validation' => ['required', 'string'],
                'description' => 'Default timezone for the application',
            ],
            'date_format' => [
                'label' => 'Date Format',
                'type' => 'select',
                'default' => 'Y-m-d',
                'options' => [
                    'Y-m-d' => '2023-12-31',
                    'm/d/Y' => '12/31/2023',
                    'd/m/Y' => '31/12/2023',
                    'F j, Y' => 'December 31, 2023',
                ],
                'validation' => ['required', 'string'],
                'description' => 'Default date format for display',
            ],
        ],
        'email' => [
            'mail_from_name' => [
                'label' => 'From Name',
                'type' => 'string',
                'default' => 'Laravel SaaS Platform',
                'validation' => ['required', 'string', 'max:255'],
                'description' => 'Default sender name for emails',
            ],
            'mail_from_address' => [
                'label' => 'From Email',
                'type' => 'email',
                'default' => '<EMAIL>',
                'validation' => ['required', 'email'],
                'description' => 'Default sender email address',
            ],
            'mail_reply_to' => [
                'label' => 'Reply To Email',
                'type' => 'email',
                'default' => '',
                'validation' => ['nullable', 'email'],
                'description' => 'Default reply-to email address',
            ],
        ],
        'social' => [
            'facebook_url' => [
                'label' => 'Facebook URL',
                'type' => 'url',
                'default' => '',
                'validation' => ['nullable', 'url'],
                'description' => 'Your Facebook page URL',
            ],
            'twitter_url' => [
                'label' => 'Twitter URL',
                'type' => 'url',
                'default' => '',
                'validation' => ['nullable', 'url'],
                'description' => 'Your Twitter profile URL',
            ],
            'linkedin_url' => [
                'label' => 'LinkedIn URL',
                'type' => 'url',
                'default' => '',
                'validation' => ['nullable', 'url'],
                'description' => 'Your LinkedIn profile URL',
            ],
            'instagram_url' => [
                'label' => 'Instagram URL',
                'type' => 'url',
                'default' => '',
                'validation' => ['nullable', 'url'],
                'description' => 'Your Instagram profile URL',
            ],
        ],
        'seo' => [
            'meta_title' => [
                'label' => 'Default Meta Title',
                'type' => 'string',
                'default' => '',
                'validation' => ['nullable', 'string', 'max:60'],
                'description' => 'Default meta title for pages',
            ],
            'meta_description' => [
                'label' => 'Default Meta Description',
                'type' => 'textarea',
                'default' => '',
                'validation' => ['nullable', 'string', 'max:160'],
                'description' => 'Default meta description for pages',
            ],
            'meta_keywords' => [
                'label' => 'Default Meta Keywords',
                'type' => 'string',
                'default' => '',
                'validation' => ['nullable', 'string', 'max:255'],
                'description' => 'Default meta keywords for pages',
            ],
            'google_analytics_id' => [
                'label' => 'Google Analytics ID',
                'type' => 'string',
                'default' => '',
                'validation' => ['nullable', 'string'],
                'description' => 'Google Analytics tracking ID',
            ],
        ],
        'maintenance' => [
            'maintenance_mode' => [
                'label' => 'Maintenance Mode',
                'type' => 'boolean',
                'default' => false,
                'validation' => ['boolean'],
                'description' => 'Enable maintenance mode',
            ],
            'maintenance_message' => [
                'label' => 'Maintenance Message',
                'type' => 'textarea',
                'default' => 'We are currently performing scheduled maintenance. Please check back soon.',
                'validation' => ['nullable', 'string'],
                'description' => 'Message to display during maintenance',
            ],
        ],
    ];

    /**
     * Initialize default settings
     */
    public function initializeDefaults(): void
    {
        foreach ($this->defaultSettings as $group => $settings) {
            foreach ($settings as $key => $config) {
                Setting::firstOrCreate(
                    ['key' => $key],
                    [
                        'value' => $config['default'],
                        'type' => $config['type'],
                        'group' => $group,
                        'label' => $config['label'],
                        'description' => $config['description'] ?? '',
                        'validation_rules' => $config['validation'] ?? [],
                        'options' => $this->getOptions($config['options'] ?? null),
                        'sort_order' => array_search($key, array_keys($settings)),
                    ]
                );
            }
        }
    }

    /**
     * Get options for select fields
     */
    private function getOptions($options): ?array
    {
        if (is_array($options)) {
            return $options;
        }

        if ($options === 'timezones') {
            return $this->getTimezones();
        }

        return null;
    }

    /**
     * Get timezone options
     */
    private function getTimezones(): array
    {
        $timezones = [];
        foreach (timezone_identifiers_list() as $timezone) {
            $timezones[$timezone] = $timezone;
        }
        return $timezones;
    }

    /**
     * Update settings with validation
     */
    public function updateSettings(array $settings): array
    {
        $errors = [];
        $updated = [];

        foreach ($settings as $key => $value) {
            try {
                $setting = Setting::where('key', $key)->first();
                
                if (!$setting) {
                    $errors[$key] = 'Setting not found';
                    continue;
                }

                // Validate the value
                if ($setting->validation_rules) {
                    $validator = Validator::make(
                        [$key => $value],
                        [$key => $setting->validation_rules]
                    );

                    if ($validator->fails()) {
                        $errors[$key] = $validator->errors()->first($key);
                        continue;
                    }
                }

                // Update the setting
                $setting->setValue($value);
                $updated[] = $key;

            } catch (\Exception $e) {
                $errors[$key] = $e->getMessage();
            }
        }

        return [
            'success' => empty($errors),
            'updated' => $updated,
            'errors' => $errors,
        ];
    }

    /**
     * Get settings for a specific group
     */
    public function getGroupSettings(string $group): array
    {
        return Setting::where('group', $group)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($setting) {
                return [
                    'key' => $setting->key,
                    'value' => $setting->getValue(),
                    'type' => $setting->type,
                    'label' => $setting->label,
                    'description' => $setting->description,
                    'options' => $setting->options,
                    'validation_rules' => $setting->validation_rules,
                ];
            })
            ->keyBy('key')
            ->toArray();
    }

    /**
     * Get all settings grouped by category
     */
    public function getAllSettings(): array
    {
        $settings = Setting::orderBy('group')->orderBy('sort_order')->get();
        $grouped = [];

        foreach ($settings as $setting) {
            $grouped[$setting->group][$setting->key] = [
                'key' => $setting->key,
                'value' => $setting->getValue(),
                'type' => $setting->type,
                'label' => $setting->label,
                'description' => $setting->description,
                'options' => $setting->options,
                'validation_rules' => $setting->validation_rules,
            ];
        }

        return $grouped;
    }

    /**
     * Export settings
     */
    public function exportSettings(): array
    {
        $settings = Setting::all();
        $export = [];

        foreach ($settings as $setting) {
            $export[$setting->key] = [
                'value' => $setting->getValue(),
                'type' => $setting->type,
                'group' => $setting->group,
                'label' => $setting->label,
                'description' => $setting->description,
            ];
        }

        return $export;
    }

    /**
     * Import settings
     */
    public function importSettings(array $settings): array
    {
        $imported = 0;
        $errors = [];

        foreach ($settings as $key => $data) {
            try {
                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $data['value'],
                        'type' => $data['type'] ?? 'string',
                        'group' => $data['group'] ?? 'general',
                        'label' => $data['label'] ?? $key,
                        'description' => $data['description'] ?? '',
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                $errors[$key] = $e->getMessage();
            }
        }

        // Clear cache
        Setting::clearCache();

        return [
            'success' => empty($errors),
            'imported' => $imported,
            'errors' => $errors,
        ];
    }
}
