@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gallery Management</h1>
        <a href="{{ route('admin.galleries.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create New Gallery
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.galleries.index') }}" class="row g-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search" placeholder="Search galleries..." value="{{ request('search') }}">
                </div>
                <div class="col-md-2">
                    <select name="category" class="form-control">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>{{ $category }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                        <option value="unpublished" {{ request('status') == 'unpublished' ? 'selected' : '' }}>Unpublished</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-check-label">
                        <input type="checkbox" name="featured" value="1" {{ request('featured') ? 'checked' : '' }} class="form-check-input">
                        Featured Only
                    </label>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                    <a href="{{ route('admin.galleries.index') }}" class="btn btn-outline-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Galleries Grid -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Galleries</h6>
        </div>
        <div class="card-body">
            @if($galleries->count() > 0)
                <!-- Bulk Actions -->
                <form id="bulk-action-form" method="POST" action="{{ route('admin.galleries.bulk-action') }}">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <select name="action" class="form-control" required>
                                    <option value="">Select Action</option>
                                    <option value="publish">Publish</option>
                                    <option value="unpublish">Unpublish</option>
                                    <option value="feature">Feature</option>
                                    <option value="unfeature">Unfeature</option>
                                    <option value="delete">Delete</option>
                                </select>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        @foreach($galleries as $gallery)
                        <div class="col-md-4 col-lg-3 mb-4">
                            <div class="card h-100 gallery-card">
                                <div class="position-relative">
                                    <input type="checkbox" name="galleries[]" value="{{ $gallery->id }}" class="gallery-checkbox position-absolute" style="top: 10px; left: 10px; z-index: 10;">
                                    
                                    @if($gallery->cover_image_url)
                                        <img src="{{ $gallery->cover_image_url }}" alt="{{ $gallery->title }}" class="card-img-top" style="height: 200px; object-fit: cover;">
                                    @else
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                            <i class="fas fa-images text-muted fa-3x"></i>
                                        </div>
                                    @endif
                                    
                                    <!-- Status badges -->
                                    <div class="position-absolute" style="top: 10px; right: 10px;">
                                        @if($gallery->is_featured)
                                            <span class="badge badge-primary mb-1">Featured</span><br>
                                        @endif
                                        @if($gallery->is_published)
                                            <span class="badge badge-success">Published</span>
                                        @else
                                            <span class="badge badge-warning">Draft</span>
                                        @endif
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h6 class="card-title">{{ $gallery->title }}</h6>
                                    @if($gallery->category)
                                        <p class="text-muted small mb-1">
                                            <i class="fas fa-folder mr-1"></i>{{ $gallery->category }}
                                        </p>
                                    @endif
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-images mr-1"></i>{{ $gallery->images_count }} images
                                    </p>
                                    @if($gallery->description)
                                        <p class="card-text small text-muted">{{ Str::limit($gallery->description, 60) }}</p>
                                    @endif
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ route('admin.galleries.show', $gallery) }}" class="btn btn-info btn-sm" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-warning btn-sm" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ route('admin.galleries.destroy', $gallery) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this gallery and all its images?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </form>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $galleries->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No galleries found</h5>
                    <p class="text-muted">Start by creating your first gallery.</p>
                    <a href="{{ route('admin.galleries.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Gallery
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
.gallery-card {
    transition: transform 0.2s;
}
.gallery-card:hover {
    transform: translateY(-2px);
}
.gallery-checkbox {
    width: 20px;
    height: 20px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const galleryCheckboxes = document.querySelectorAll('.gallery-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            galleryCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Bulk action form validation
    document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.gallery-checkbox:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one gallery.');
            return false;
        }
        
        const action = document.querySelector('select[name="action"]').value;
        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected galleries and all their images?')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
@endpush
@endsection
