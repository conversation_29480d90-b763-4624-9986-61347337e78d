<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Backup existing data
        $projects = collect();
        if (Schema::hasTable('projects')) {
            $projects = DB::table('projects')->get();
            Schema::drop('projects');
        }

        // Create with proper structure
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->longText('content')->nullable();
            $table->string('client')->nullable();
            $table->string('category')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->enum('status', ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled'])->default('planning');
            $table->string('featured_image')->nullable();
            $table->json('gallery')->nullable(); // Array of image paths
            $table->string('project_url')->nullable();
            $table->json('technologies')->nullable(); // Array of technologies used
            $table->integer('sort_order')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_active']);
            $table->index(['is_featured']);
            $table->index(['status']);
            $table->index(['category']);
            $table->index(['slug']);
        });

        // Restore data with proper structure
        foreach ($projects as $project) {
            if (isset($project->title) && $project->title) {
                DB::table('projects')->insert([
                    'title' => $project->title,
                    'slug' => $project->slug ?: \Str::slug($project->title),
                    'description' => $project->description,
                    'content' => $project->content,
                    'client' => $project->client,
                    'category' => $project->category,
                    'start_date' => $project->start_date ? date('Y-m-d', strtotime($project->start_date)) : null,
                    'end_date' => $project->end_date ? date('Y-m-d', strtotime($project->end_date)) : null,
                    'status' => in_array($project->status, ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled']) ? $project->status : 'planning',
                    'featured_image' => $project->featured_image,
                    'gallery' => $project->gallery ? json_encode([$project->gallery]) : null,
                    'project_url' => $project->project_url,
                    'technologies' => $project->technologies ? json_encode([$project->technologies]) : null,
                    'sort_order' => $project->sort_order ?? 0,
                    'is_featured' => isset($project->is_featured) && $project->is_featured ? true : false,
                    'is_active' => isset($project->is_active) && $project->is_active ? true : false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
