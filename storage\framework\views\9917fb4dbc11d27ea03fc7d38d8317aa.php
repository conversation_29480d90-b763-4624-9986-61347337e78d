<!-- Global Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-8 flex flex-col items-center shadow-2xl">
        <!-- Loading Spinner -->
        <div class="relative">
            <div class="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full animate-spin"></div>
            <div class="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-green-400 rounded-full animate-spin" style="animation-direction: reverse; animation-duration: 1.5s;"></div>
        </div>
        
        <!-- Loading Text -->
        <div class="mt-4 text-center">
            <h3 class="text-lg font-semibold text-gray-900" id="loading-title">Processing...</h3>
            <p class="text-sm text-gray-600 mt-1" id="loading-message">Please wait while we process your request.</p>
        </div>
    </div>
</div>

<!-- Loading JavaScript -->
<script>
window.LoadingManager = {
    overlay: null,
    title: null,
    message: null,
    
    init: function() {
        this.overlay = document.getElementById('loading-overlay');
        this.title = document.getElementById('loading-title');
        this.message = document.getElementById('loading-message');
    },
    
    show: function(title = 'Processing...', message = 'Please wait while we process your request.') {
        if (!this.overlay) this.init();
        
        this.title.textContent = title;
        this.message.textContent = message;
        this.overlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    },
    
    hide: function() {
        if (!this.overlay) this.init();
        
        this.overlay.classList.add('hidden');
        document.body.style.overflow = '';
    },
    
    // Show loading for form submissions
    showForForm: function(form, title = 'Saving...', message = 'Please wait while we save your changes.') {
        this.show(title, message);
        
        // Auto-hide after 30 seconds as fallback
        setTimeout(() => {
            this.hide();
        }, 30000);
    },
    
    // Show loading for AJAX requests
    showForAjax: function(title = 'Loading...', message = 'Please wait while we fetch the data.') {
        this.show(title, message);
    },
    
    // Show loading for file uploads
    showForUpload: function(title = 'Uploading...', message = 'Please wait while we upload your file.') {
        this.show(title, message);
    },
    
    // Show loading for deletions
    showForDelete: function(title = 'Deleting...', message = 'Please wait while we delete the item.') {
        this.show(title, message);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    LoadingManager.init();
    
    // Auto-show loading for all form submissions
    document.addEventListener('submit', function(e) {
        const form = e.target;
        
        // Skip if form has data-no-loading attribute
        if (form.hasAttribute('data-no-loading')) {
            return;
        }
        
        // Determine loading message based on form action
        let title = 'Processing...';
        let message = 'Please wait while we process your request.';
        
        if (form.action.includes('delete') || form.querySelector('input[name="_method"][value="DELETE"]')) {
            title = 'Deleting...';
            message = 'Please wait while we delete the item.';
        } else if (form.enctype === 'multipart/form-data') {
            title = 'Uploading...';
            message = 'Please wait while we upload your files.';
        } else if (form.method.toLowerCase() === 'post' || form.querySelector('input[name="_method"][value="PUT"]')) {
            title = 'Saving...';
            message = 'Please wait while we save your changes.';
        }
        
        LoadingManager.showForForm(form, title, message);
    });
    
    // Auto-show loading for AJAX requests
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            // Skip if request has no-loading header
            const options = args[1] || {};
            if (options.headers && options.headers['X-No-Loading']) {
                return originalFetch.apply(this, args);
            }
            
            LoadingManager.showForAjax();
            
            return originalFetch.apply(this, args)
                .then(response => {
                    LoadingManager.hide();
                    return response;
                })
                .catch(error => {
                    LoadingManager.hide();
                    throw error;
                });
        };
    }
    
    // Auto-show loading for jQuery AJAX (if jQuery is available)
    if (window.jQuery) {
        jQuery(document).ajaxStart(function() {
            LoadingManager.showForAjax();
        });
        
        jQuery(document).ajaxStop(function() {
            LoadingManager.hide();
        });
    }
});

// Hide loading on page unload
window.addEventListener('beforeunload', function() {
    LoadingManager.hide();
});

// Hide loading on page visibility change (when user switches tabs)
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // Small delay to ensure page is fully loaded
        setTimeout(() => {
            LoadingManager.hide();
        }, 500);
    }
});
</script>

<style>
/* Additional loading styles */
#loading-overlay {
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease-in-out;
}

/* Pulse animation for loading text */
#loading-title, #loading-message {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Prevent scrolling when loading is shown */
body.loading-active {
    overflow: hidden !important;
}
</style>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/components/loading-overlay.blade.php ENDPATH**/ ?>