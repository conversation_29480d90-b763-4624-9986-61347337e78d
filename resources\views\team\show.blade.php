@extends('layouts.public')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div data-aos="fade-right">
                <div class="mb-4">
                    <a href="{{ route('team.index') }}" class="text-green-600 hover:text-green-700 font-medium">
                        ← Back to Team
                    </a>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $team->name }}</h1>
                <p class="text-2xl text-green-600 font-semibold mb-6">{{ $team->position }}</p>
                
                @if($team->department)
                    <p class="text-lg text-gray-600 mb-6">{{ $team->department_name }}</p>
                @endif
                
                @if($team->bio)
                    <p class="text-lg text-gray-600 mb-8 leading-relaxed">{{ $team->bio }}</p>
                @endif
                
                @if($team->linkedin || $team->twitter || $team->email)
                    <div class="flex space-x-4">
                        @if($team->email)
                            <a href="mailto:{{ $team->email }}" class="bg-green-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-green-700 transition-all duration-300">
                                <i class="fas fa-envelope mr-2"></i>Send Email
                            </a>
                        @endif
                        @if($team->linkedin)
                            <a href="{{ $team->linkedin }}" target="_blank" class="bg-blue-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-blue-700 transition-all duration-300">
                                <i class="fab fa-linkedin mr-2"></i>LinkedIn
                            </a>
                        @endif
                        @if($team->twitter)
                            <a href="{{ $team->twitter }}" target="_blank" class="bg-blue-400 text-white px-6 py-3 rounded-full font-semibold hover:bg-blue-500 transition-all duration-300">
                                <i class="fab fa-twitter mr-2"></i>Twitter
                            </a>
                        @endif
                    </div>
                @endif
            </div>
            
            <div data-aos="fade-left" class="text-center">
                @if($team->image_url)
                    <img src="{{ $team->image_url }}" alt="{{ $team->name }}" class="w-80 h-80 rounded-full mx-auto object-cover shadow-2xl">
                @else
                    <div class="w-80 h-80 rounded-full mx-auto bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-2xl">
                        <span class="text-8xl font-bold text-white">{{ substr($team->name, 0, 1) }}</span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Detailed Information -->
@if($team->bio || $team->skills || $team->experience)
<section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($team->bio)
            <div class="mb-12" data-aos="fade-up">
                <h2 class="text-3xl font-bold text-gray-900 mb-6">About {{ $team->name }}</h2>
                <div class="prose prose-lg max-w-none">
                    <p class="text-gray-600 leading-relaxed">{{ $team->bio }}</p>
                </div>
            </div>
        @endif
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            @if($team->skills && count($team->skills) > 0)
                <div data-aos="fade-up" data-aos-delay="100">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Skills & Expertise</h3>
                    <div class="flex flex-wrap gap-3">
                        @foreach($team->skills as $skill)
                            <span class="px-4 py-2 bg-green-100 text-green-800 font-medium rounded-full">
                                {{ $skill }}
                            </span>
                        @endforeach
                    </div>
                </div>
            @endif
            
            @if($team->experience)
                <div data-aos="fade-up" data-aos-delay="200">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Experience</h3>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-briefcase text-white"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-2xl font-bold text-green-600">{{ $team->experience }}+</p>
                                <p class="text-gray-600">Years of Experience</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Contact Section -->
<section class="py-20 bg-green-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div data-aos="fade-up">
            <h2 class="text-4xl font-bold text-white mb-4">Get in Touch with {{ $team->name }}</h2>
            <p class="text-xl text-green-100 mb-8">
                Interested in working with {{ $team->name }} or learning more about their expertise? Reach out today.
            </p>
            
            <div class="flex flex-wrap justify-center gap-4">
                @if($team->email)
                    <a href="mailto:{{ $team->email }}" class="bg-white text-green-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300">
                        <i class="fas fa-envelope mr-2"></i>Send Email
                    </a>
                @endif
                
                <a href="{{ route('contact') }}" class="bg-green-700 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-800 transition-all duration-300">
                    Contact Our Team
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Other Team Members -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Meet Other Team Members</h2>
            <p class="text-xl text-gray-600">Discover more talented professionals in our team</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @php
                $otherMembers = \App\Models\Team::active()
                    ->where('id', '!=', $team->id)
                    ->limit(4)
                    ->get();
            @endphp
            
            @foreach($otherMembers as $index => $member)
                <div class="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($member->image_url)
                        <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-20 h-20 rounded-full mx-auto mb-4 object-cover shadow-lg">
                    @else
                        <div class="w-20 h-20 rounded-full mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                            <span class="text-lg font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                        </div>
                    @endif
                    
                    <h3 class="text-lg font-bold text-gray-900 mb-1">{{ $member->name }}</h3>
                    <p class="text-green-600 font-semibold text-sm mb-3">{{ $member->position }}</p>
                    
                    <a href="{{ route('team.show', $member) }}" class="inline-block bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold hover:bg-green-700 transition-colors duration-300">
                        View Profile
                    </a>
                </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('team.index') }}" class="bg-green-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-700 transition-all duration-300">
                View All Team Members
            </a>
        </div>
    </div>
</section>
@endsection

@push('styles')
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
@endpush

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
@endpush
