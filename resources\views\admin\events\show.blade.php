@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Event Details</h1>
            <div class="flex gap-3">
                <a href="{{ route('admin.events.edit', $event) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    ✏️ Edit Event
                </a>
                <a href="{{ route('admin.events.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Back to Events
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <!-- Event Details -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">{{ $event->title }}</h3>
                    </div>
                    <div class="p-6">
                    @if($event->featured_image)
                        <div class="mb-4">
                            <img src="{{ asset('storage/' . $event->featured_image) }}" alt="{{ $event->title }}" 
                                 class="img-fluid rounded" style="max-height: 400px; width: 100%; object-fit: cover;">
                        </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Event Date:</strong><br>
                            <span class="text-muted">{{ $event->formatted_event_date }}</span>
                            @if($event->event_end_date)
                                <br><strong>End Date:</strong><br>
                                <span class="text-muted">{{ $event->event_end_date->format('M d, Y \a\t g:i A') }}</span>
                            @endif
                        </div>
                        <div class="col-md-6">
                            @if($event->location)
                                <strong>Location:</strong><br>
                                <span class="text-muted">{{ $event->location }}</span><br>
                            @endif
                            @if($event->price)
                                <strong>Price:</strong><br>
                                <span class="text-muted">${{ number_format($event->price, 2) }}</span><br>
                            @endif
                        </div>
                    </div>

                    @if($event->description)
                        <div class="mb-3">
                            <strong>Description:</strong>
                            <p class="text-muted mt-2">{{ $event->description }}</p>
                        </div>
                    @endif

                    @if($event->content)
                        <div class="mb-3">
                            <strong>Full Content:</strong>
                            <div class="mt-2 border p-3 rounded bg-light">
                                {!! $event->content !!}
                            </div>
                        </div>
                    @endif

                    @if($event->max_attendees)
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Maximum Attendees:</strong><br>
                                <span class="text-muted">{{ $event->max_attendees }}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Current Attendees:</strong><br>
                                <span class="text-muted">{{ $event->current_attendees }}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Available Spots:</strong><br>
                                <span class="text-muted">{{ $event->available_spots ?? 'Unlimited' }}</span>
                            </div>
                        </div>
                    @endif

                    @if($event->organizer_info && count(array_filter($event->organizer_info)))
                        <div class="mb-3">
                            <strong>Organizer Information:</strong>
                            <div class="mt-2 border p-3 rounded bg-light">
                                @if($event->organizer_info['name'] ?? null)
                                    <strong>Name:</strong> {{ $event->organizer_info['name'] }}<br>
                                @endif
                                @if($event->organizer_info['email'] ?? null)
                                    <strong>Email:</strong> <a href="mailto:{{ $event->organizer_info['email'] }}">{{ $event->organizer_info['email'] }}</a><br>
                                @endif
                                @if($event->organizer_info['phone'] ?? null)
                                    <strong>Phone:</strong> <a href="tel:{{ $event->organizer_info['phone'] }}">{{ $event->organizer_info['phone'] }}</a><br>
                                @endif
                                @if($event->organizer_info['website'] ?? null)
                                    <strong>Website:</strong> <a href="{{ $event->organizer_info['website'] }}" target="_blank">{{ $event->organizer_info['website'] }}</a>
                                @endif
                            </div>
                        </div>
                    @endif

                    @if($event->event_url)
                        <div class="mb-3">
                            <strong>External Event URL:</strong><br>
                            <a href="{{ $event->event_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt"></i> Visit Event Page
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Event Status -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Status</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Status:</strong>
                        @if($event->status == 'published')
                            <span class="badge badge-success ml-2">Published</span>
                        @elseif($event->status == 'draft')
                            <span class="badge badge-warning ml-2">Draft</span>
                        @else
                            <span class="badge badge-danger ml-2">Cancelled</span>
                        @endif
                    </div>

                    <div class="mb-3">
                        <strong>Featured:</strong>
                        @if($event->is_featured)
                            <span class="badge badge-primary ml-2">Featured</span>
                        @else
                            <span class="badge badge-secondary ml-2">Regular</span>
                        @endif
                    </div>

                    <div class="mb-3">
                        <strong>Event Type:</strong>
                        @if($event->is_upcoming)
                            <span class="badge badge-info ml-2">Upcoming</span>
                        @else
                            <span class="badge badge-dark ml-2">Past</span>
                        @endif
                    </div>

                    @if($event->max_attendees && $event->is_full)
                        <div class="mb-3">
                            <span class="badge badge-danger">Event Full</span>
                        </div>
                    @endif

                    <div class="mb-3">
                        <strong>Sort Order:</strong>
                        <span class="text-muted ml-2">{{ $event->sort_order }}</span>
                    </div>

                    <div class="mb-3">
                        <strong>Slug:</strong>
                        <span class="text-muted ml-2">{{ $event->slug }}</span>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            @if($event->meta_title || $event->meta_description || $event->meta_keywords)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
                    </div>
                    <div class="card-body">
                        @if($event->meta_title)
                            <div class="mb-3">
                                <strong>Meta Title:</strong>
                                <p class="text-muted small">{{ $event->meta_title }}</p>
                            </div>
                        @endif

                        @if($event->meta_description)
                            <div class="mb-3">
                                <strong>Meta Description:</strong>
                                <p class="text-muted small">{{ $event->meta_description }}</p>
                            </div>
                        @endif

                        @if($event->meta_keywords)
                            <div class="mb-3">
                                <strong>Meta Keywords:</strong>
                                <p class="text-muted small">{{ $event->meta_keywords }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Event Timestamps -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timestamps</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Created:</strong><br>
                        <small class="text-muted">{{ $event->created_at->format('M d, Y g:i A') }}</small>
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ $event->updated_at->format('M d, Y g:i A') }}</small>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.events.edit', $event) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Event
                        </a>
                        
                        @if($event->status == 'published')
                            <a href="{{ route('events.show', $event) }}" target="_blank" class="btn btn-info">
                                <i class="fas fa-eye"></i> View on Website
                            </a>
                        @endif

                        <form method="POST" action="{{ route('admin.events.destroy', $event) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this event? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash"></i> Delete Event
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
