<?php

namespace App\Http\Middleware;

use App\Models\License;
use App\Services\InstallerService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LicenseMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip license check for installer routes
        if ($request->routeIs('installer.*')) {
            return $next($request);
        }

        // Skip license check if not installed yet
        $installerService = app(InstallerService::class);
        if (!$installerService->isInstalled()) {
            return redirect()->route('installer.welcome');
        }

        // Get current license
        $license = License::current();

        // If no license found, redirect to license activation
        if (!$license) {
            return $this->redirectToLicenseActivation($request);
        }

        // Verify license periodically
        if ($license->needsVerification()) {
            $license->verify();
            $license->refresh();
        }

        // Check if license is valid
        if (!$license->isValid()) {
            return $this->handleInvalidLicense($request, $license);
        }

        // Add license information to request
        $request->attributes->set('license', $license);

        return $next($request);
    }

    /**
     * Redirect to license activation page
     */
    private function redirectToLicenseActivation(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'License activation required',
                'redirect' => route('admin.license.activate')
            ], 402);
        }

        return redirect()->route('admin.license.activate')
            ->with('error', 'Please activate your license to continue using this application.');
    }

    /**
     * Handle invalid license
     */
    private function handleInvalidLicense(Request $request, License $license): Response
    {
        $message = $this->getLicenseErrorMessage($license);

        if ($request->expectsJson()) {
            return response()->json([
                'error' => $message,
                'license_status' => $license->status,
                'grace_period_days' => $license->getGracePeriodDaysRemaining(),
                'redirect' => route('admin.license.manage')
            ], 402);
        }

        return redirect()->route('admin.license.manage')
            ->with('error', $message);
    }

    /**
     * Get appropriate error message based on license status
     */
    private function getLicenseErrorMessage(License $license): string
    {
        switch ($license->status) {
            case 'suspended':
                return 'Your license has been suspended. Please contact support for assistance.';

            case 'expired':
                if ($license->isInGracePeriod()) {
                    $days = $license->getGracePeriodDaysRemaining();
                    return "Your license has expired. You have {$days} days remaining in the grace period to renew.";
                }
                return 'Your license has expired. Please renew your license to continue using this application.';

            case 'pending_verification':
                return 'License verification is pending. Please check your internet connection or contact support.';

            default:
                return 'Your license is invalid. Please contact support for assistance.';
        }
    }
}
