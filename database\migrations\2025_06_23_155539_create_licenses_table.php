<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('licenses', function (Blueprint $table) {
            $table->id();
            $table->string('license_key')->unique();
            $table->string('purchase_code')->unique();
            $table->string('domain');
            $table->string('ip_address')->nullable();
            $table->enum('status', ['active', 'suspended', 'expired', 'pending_verification'])->default('pending_verification');
            $table->enum('license_type', ['regular', 'extended'])->default('regular');
            $table->timestamp('activated_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('last_verified_at')->nullable();
            $table->json('verification_data')->nullable();
            $table->integer('grace_period_days')->default(7);
            $table->boolean('is_grace_period_active')->default(false);
            $table->timestamp('grace_period_started_at')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['domain', 'status']);
            $table->index(['license_key', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('licenses');
    }
};
