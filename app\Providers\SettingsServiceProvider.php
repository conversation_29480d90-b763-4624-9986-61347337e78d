<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Config;
use App\Models\GeneralSetting;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        try {
            // Get settings instance
            $settings = GeneralSetting::getInstance();
            
            // Share settings with all views
            View::share('globalSettings', $settings);
            
            // Configure application settings based on database settings
            if ($settings->application_name) {
                Config::set('app.name', $settings->application_name);
            }
            
            if ($settings->timezone) {
                Config::set('app.timezone', $settings->timezone);
            }
            
            // Configure mail settings if SMTP is configured
            if ($settings->isSmtpConfigured()) {
                Config::set([
                    'mail.mailers.smtp.host' => $settings->smtp_host,
                    'mail.mailers.smtp.port' => $settings->smtp_port,
                    'mail.mailers.smtp.username' => $settings->smtp_user,
                    'mail.mailers.smtp.password' => $settings->smtp_pass,
                    'mail.mailers.smtp.encryption' => 'tls',
                    'mail.from.address' => $settings->email_from,
                    'mail.from.name' => $settings->application_name,
                ]);
            }
            
            // Configure reCAPTCHA if configured
            if ($settings->isRecaptchaConfigured()) {
                Config::set([
                    'services.recaptcha.site_key' => $settings->recaptcha_site_key,
                    'services.recaptcha.secret_key' => $settings->recaptcha_secret_key,
                ]);
            }
            
        } catch (\Exception $e) {
            // Handle case where database/table doesn't exist yet
            // This can happen during initial setup or migrations
        }
    }
}
