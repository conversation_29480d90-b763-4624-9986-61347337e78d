@extends('layouts.public')

@section('title', $gallery->meta_title ?: $gallery->title)
@section('meta_description', $gallery->meta_description ?: $gallery->description)
@section('meta_keywords', $gallery->meta_keywords)

@section('content')
<!-- Gallery Hero Section -->
<section class="relative">
    <div class="h-96 bg-cover bg-center relative" style="background-image: url('{{ $gallery->cover_image_url }}')">
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>
    </div>
    
    <div class="absolute inset-0 flex items-center">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div class="text-white">
                <div class="flex items-center text-sm mb-4">
                    <a href="{{ route('gallery.index') }}" class="hover:text-green-300 transition-colors">Gallery</a>
                    @if($gallery->category)
                        <i class="fas fa-chevron-right mx-2"></i>
                        <a href="{{ route('gallery.category', $gallery->category) }}" class="hover:text-green-300 transition-colors">{{ $gallery->category }}</a>
                    @endif
                    <i class="fas fa-chevron-right mx-2"></i>
                    <span>{{ $gallery->title }}</span>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold mb-4">{{ $gallery->title }}</h1>
                
                <div class="flex flex-wrap items-center gap-6 text-lg">
                    @if($gallery->category)
                        <div class="flex items-center">
                            <i class="fas fa-folder mr-2"></i>
                            {{ $gallery->category }}
                        </div>
                    @endif
                    
                    <div class="flex items-center">
                        <i class="fas fa-images mr-2"></i>
                        {{ $gallery->images->count() }} {{ Str::plural('photo', $gallery->images->count()) }}
                    </div>
                    
                    <div class="flex items-center">
                        <i class="fas fa-calendar mr-2"></i>
                        {{ $gallery->created_at->format('M d, Y') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Content -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                @if($gallery->description)
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Gallery</h2>
                        <p class="text-lg text-gray-700 leading-relaxed">{{ $gallery->description }}</p>
                    </div>
                @endif

                <!-- Gallery Images -->
                @if($gallery->images->count() > 0)
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold text-gray-900">Photos</h2>
                            <div class="flex items-center space-x-4">
                                <button onclick="toggleView()" class="text-gray-600 hover:text-gray-800 transition-colors">
                                    <i id="view-icon" class="fas fa-th-large"></i>
                                    <span id="view-text" class="ml-1">Grid View</span>
                                </button>
                                <button onclick="openSlideshow(0)" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                                    <i class="fas fa-play mr-2"></i>Slideshow
                                </button>
                            </div>
                        </div>
                        
                        <!-- Grid View -->
                        <div id="grid-view" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            @foreach($gallery->images as $index => $image)
                                <div class="relative group cursor-pointer" onclick="openLightbox({{ $index }})">
                                    <img src="{{ $image->thumbnail_url }}" alt="{{ $image->alt_text ?: $image->title }}" 
                                         class="w-full h-48 object-cover rounded-lg transition-transform duration-300 group-hover:scale-105">
                                    
                                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-search-plus text-white text-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                                    </div>
                                    
                                    @if($image->is_featured)
                                        <div class="absolute top-2 right-2">
                                            <span class="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                Featured
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        <!-- List View -->
                        <div id="list-view" class="space-y-6" style="display: none;">
                            @foreach($gallery->images as $index => $image)
                                <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="openLightbox({{ $index }})">
                                    <img src="{{ $image->thumbnail_url }}" alt="{{ $image->alt_text ?: $image->title }}" 
                                         class="w-24 h-24 object-cover rounded-lg flex-shrink-0">
                                    
                                    <div class="flex-1">
                                        @if($image->title)
                                            <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $image->title }}</h3>
                                        @endif
                                        @if($image->caption)
                                            <p class="text-gray-600 mb-2">{{ $image->caption }}</p>
                                        @endif
                                        <div class="text-sm text-gray-500">
                                            {{ $image->dimensions }} • {{ $image->file_size_human }}
                                            @if($image->is_featured)
                                                <span class="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Featured</span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-search-plus text-gray-400 text-xl"></i>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @else
                    <div class="text-center py-16">
                        <i class="fas fa-images text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-2xl font-semibold text-gray-900 mb-2">No Photos Yet</h3>
                        <p class="text-gray-600">This gallery doesn't have any photos at the moment.</p>
                    </div>
                @endif

                <!-- Share Section -->
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Share This Gallery</h2>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                           target="_blank" 
                           class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f mr-2"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($gallery->title) }}" 
                           target="_blank" 
                           class="bg-blue-400 text-white px-4 py-2 rounded-md hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter mr-2"></i>Twitter
                        </a>
                        <a href="https://www.pinterest.com/pin/create/button/?url={{ urlencode(request()->url()) }}&media={{ urlencode($gallery->cover_image_url) }}&description={{ urlencode($gallery->title) }}" 
                           target="_blank" 
                           class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                            <i class="fab fa-pinterest mr-2"></i>Pinterest
                        </a>
                        <button onclick="copyToClipboard('{{ request()->url() }}')" 
                                class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                            <i class="fas fa-link mr-2"></i>Copy Link
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Gallery Info -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8 sticky top-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Gallery Information</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <span class="font-medium text-gray-900">Photos:</span>
                            <span class="text-gray-600 ml-2">{{ $gallery->images->count() }}</span>
                        </div>
                        
                        @if($gallery->category)
                            <div>
                                <span class="font-medium text-gray-900">Category:</span>
                                <a href="{{ route('gallery.category', $gallery->category) }}" class="text-green-600 hover:text-green-700 ml-2">{{ $gallery->category }}</a>
                            </div>
                        @endif
                        
                        <div>
                            <span class="font-medium text-gray-900">Created:</span>
                            <span class="text-gray-600 ml-2">{{ $gallery->created_at->format('M d, Y') }}</span>
                        </div>
                        
                        @if($gallery->images->where('is_featured', true)->count() > 0)
                            <div>
                                <span class="font-medium text-gray-900">Featured Photos:</span>
                                <span class="text-gray-600 ml-2">{{ $gallery->images->where('is_featured', true)->count() }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Other Galleries -->
                @if($otherGalleries->count() > 0)
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">More Galleries</h3>
                        <div class="space-y-4">
                            @foreach($otherGalleries as $otherGallery)
                                <div class="flex items-center space-x-3">
                                    <img src="{{ $otherGallery->cover_image_url }}" alt="{{ $otherGallery->title }}" 
                                         class="w-16 h-16 object-cover rounded-lg flex-shrink-0">
                                    
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-medium text-gray-900 truncate">
                                            <a href="{{ route('gallery.show', $otherGallery) }}" class="hover:text-green-600 transition-colors">
                                                {{ $otherGallery->title }}
                                            </a>
                                        </h4>
                                        <p class="text-sm text-gray-500">
                                            {{ $otherGallery->image_count }} {{ Str::plural('photo', $otherGallery->image_count) }}
                                        </p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-4">
                            <a href="{{ route('gallery.index') }}" class="text-green-600 hover:text-green-700 font-medium">
                                View All Galleries <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Related Galleries -->
@if($relatedGalleries->count() > 0)
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Related Galleries</h2>
            <p class="text-lg text-gray-600">More galleries you might enjoy</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($relatedGalleries as $relatedGallery)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="relative h-48 bg-cover bg-center" style="background-image: url('{{ $relatedGallery->cover_image_url }}')">
                        <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                            <a href="{{ route('gallery.show', $relatedGallery) }}" class="opacity-0 hover:opacity-100 transition-opacity duration-300 bg-white text-gray-900 px-4 py-2 rounded-md font-medium">
                                View Gallery
                            </a>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <a href="{{ route('gallery.show', $relatedGallery) }}" class="hover:text-green-600 transition-colors">
                                {{ $relatedGallery->title }}
                            </a>
                        </h3>
                        
                        @if($relatedGallery->description)
                            <p class="text-gray-600 mb-4">{{ Str::limit($relatedGallery->description, 100) }}</p>
                        @endif
                        
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>
                                <i class="fas fa-images mr-1"></i>
                                {{ $relatedGallery->image_count }} {{ Str::plural('photo', $relatedGallery->image_count) }}
                            </span>
                            <span>{{ $relatedGallery->created_at->format('M Y') }}</span>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden">
    <div class="flex items-center justify-center h-full p-4">
        <div class="relative max-w-4xl max-h-full">
            <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white text-2xl z-10 hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
            
            <button onclick="previousImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl z-10 hover:text-gray-300">
                <i class="fas fa-chevron-left"></i>
            </button>
            
            <button onclick="nextImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl z-10 hover:text-gray-300">
                <i class="fas fa-chevron-right"></i>
            </button>
            
            <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
            
            <div id="lightbox-info" class="absolute bottom-4 left-4 right-4 text-white">
                <h3 id="lightbox-title" class="text-lg font-semibold mb-1"></h3>
                <p id="lightbox-caption" class="text-sm text-gray-300"></p>
                <div id="lightbox-counter" class="text-xs text-gray-400 mt-2"></div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Gallery images data
const galleryImages = @json($gallery->images->map(function($image) {
    return [
        'id' => $image->id,
        'title' => $image->title,
        'caption' => $image->caption,
        'alt_text' => $image->alt_text,
        'image_url' => $image->image_url,
        'thumbnail_url' => $image->thumbnail_url,
    ];
}));

let currentImageIndex = 0;

function toggleView() {
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const viewIcon = document.getElementById('view-icon');
    const viewText = document.getElementById('view-text');
    
    if (gridView.style.display === 'none') {
        gridView.style.display = 'grid';
        listView.style.display = 'none';
        viewIcon.className = 'fas fa-th-large';
        viewText.textContent = 'Grid View';
    } else {
        gridView.style.display = 'none';
        listView.style.display = 'block';
        viewIcon.className = 'fas fa-list';
        viewText.textContent = 'List View';
    }
}

function openLightbox(index) {
    currentImageIndex = index;
    showImage();
    document.getElementById('lightbox').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    document.getElementById('lightbox').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
    showImage();
}

function previousImage() {
    currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
    showImage();
}

function showImage() {
    const image = galleryImages[currentImageIndex];
    document.getElementById('lightbox-image').src = image.image_url;
    document.getElementById('lightbox-image').alt = image.alt_text || image.title;
    document.getElementById('lightbox-title').textContent = image.title || '';
    document.getElementById('lightbox-caption').textContent = image.caption || '';
    document.getElementById('lightbox-counter').textContent = `${currentImageIndex + 1} of ${galleryImages.length}`;
}

function openSlideshow(startIndex) {
    currentImageIndex = startIndex;
    openLightbox(startIndex);
    
    // Auto-advance slideshow
    const slideshowInterval = setInterval(() => {
        if (document.getElementById('lightbox').classList.contains('hidden')) {
            clearInterval(slideshowInterval);
        } else {
            nextImage();
        }
    }, 3000);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
        button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
        button.classList.add('bg-green-600', 'hover:bg-green-700');
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-gray-600', 'hover:bg-gray-700');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (!document.getElementById('lightbox').classList.contains('hidden')) {
        if (e.key === 'ArrowLeft') {
            previousImage();
        } else if (e.key === 'ArrowRight') {
            nextImage();
        } else if (e.key === 'Escape') {
            closeLightbox();
        }
    }
});
</script>
@endpush
@endsection
