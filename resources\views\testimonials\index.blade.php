@extends('layouts.public')

@section('title', 'Client Testimonials')
@section('meta_description', 'Read what our satisfied clients have to say about our services and their experience working with us.')

@section('content')
<div class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Client Testimonials</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Don't just take our word for it. Here's what our satisfied clients have to say about working with us.
            </p>
        </div>

        <!-- Add Testimonial CTA -->
        <div class="text-center mb-12">
            <a href="{{ route('testimonials.create') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                Share Your Experience
            </a>
        </div>

        <!-- Testimonials Grid -->
        @if($testimonials->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            @foreach($testimonials as $testimonial)
            <div class="bg-white rounded-lg shadow-md p-8 hover:shadow-lg transition-shadow duration-300">
                <!-- Rating -->
                <div class="flex items-center mb-4">
                    {!! $testimonial->stars_html !!}
                </div>

                <!-- Testimonial Text -->
                <blockquote class="text-gray-700 mb-6 italic">
                    "{{ $testimonial->testimonial }}"
                </blockquote>

                <!-- Client Info -->
                <div class="flex items-center">
                    @if($testimonial->client_image_url)
                        <img src="{{ $testimonial->client_image_url }}" alt="{{ $testimonial->client_name }}" 
                             class="w-12 h-12 rounded-full object-cover mr-4">
                    @else
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                            <span class="text-green-600 font-semibold text-lg">
                                {{ substr($testimonial->client_name, 0, 1) }}
                            </span>
                        </div>
                    @endif
                    
                    <div>
                        <div class="font-semibold text-gray-900">{{ $testimonial->client_name }}</div>
                        @if($testimonial->client_position || $testimonial->client_company)
                            <div class="text-sm text-gray-600">
                                @if($testimonial->client_position && $testimonial->client_company)
                                    {{ $testimonial->client_position }} at {{ $testimonial->client_company }}
                                @elseif($testimonial->client_position)
                                    {{ $testimonial->client_position }}
                                @else
                                    {{ $testimonial->client_company }}
                                @endif
                            </div>
                        @endif
                        @if($testimonial->project_title)
                            <div class="text-xs text-green-600 mt-1">
                                Project: {{ $testimonial->project_title }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($testimonials->hasPages())
            <div class="flex justify-center">
                {{ $testimonials->links() }}
            </div>
        @endif
        @else
        <!-- No Testimonials Message -->
        <div class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">💬</div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Testimonials Yet</h3>
            <p class="text-gray-500 mb-6">
                Be the first to share your experience working with us!
            </p>
            <a href="{{ route('testimonials.create') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                Share Your Experience
            </a>
        </div>
        @endif

        <!-- Contact CTA -->
        <div class="bg-green-50 rounded-lg p-8 text-center mt-12">
            <h3 class="text-xl font-bold text-gray-900 mb-2">Ready to work with us?</h3>
            <p class="text-gray-600 mb-4">
                Join our satisfied clients and let us help your business grow.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                    Get Started
                </a>
                <a href="{{ route('services.index') }}" class="bg-white hover:bg-gray-50 text-green-600 border-2 border-green-600 font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                    View Our Services
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
