# GreenWeb Installation Guide

## Pre-Installation Checklist

Before installing GreenWeb, ensure your server meets all requirements and you have the necessary information ready.

### Required Information
- [ ] Database credentials (host, name, username, password)
- [ ] CodeCanyon purchase code
- [ ] Domain name or subdomain
- [ ] SMTP email settings (optional but recommended)
- [ ] SSL certificate (recommended for production)

### Server Preparation
- [ ] PHP 8.2+ installed with required extensions
- [ ] MySQL 8.0+ or MariaDB 10.3+ database server
- [ ] Web server (Apache/Nginx) configured
- [ ] Composer installed globally
- [ ] Node.js 18+ installed (for asset compilation)

## Installation Methods

### Method 1: Web-Based Installation (Recommended)

This is the easiest method for most users, especially those without command-line access.

#### Step 1: Upload Files
1. Download the GreenWeb package from CodeCanyon
2. Extract the ZIP file to your local computer
3. Upload all files to your web server's document root
4. Ensure proper file permissions:
   ```bash
   chmod -R 755 storage bootstrap/cache
   chown -R www-data:www-data storage bootstrap/cache
   ```

#### Step 2: Access Installation Wizard
1. Navigate to `https://yourdomain.com/installer` in your web browser
2. The installation wizard will automatically start

#### Step 3: System Requirements Check
The installer will check:
- PHP version and extensions
- File permissions
- Database connectivity
- Server configuration

If any requirements are not met, follow the provided instructions to resolve them.

#### Step 4: Database Configuration
1. Enter your database credentials:
   - **Database Host**: Usually `localhost` or `127.0.0.1`
   - **Database Name**: Create a new database for GreenWeb
   - **Username**: Database username with full privileges
   - **Password**: Database password

2. Click "Test Connection" to verify credentials
3. The installer will create necessary tables and seed initial data

#### Step 5: Environment Setup
1. **Application Name**: Enter your application name
2. **Application URL**: Your domain URL (with https://)
3. **Environment**: Select `production` for live sites
4. **Debug Mode**: Keep disabled for production

#### Step 6: Admin Account Creation
1. **Name**: Your full name
2. **Email**: Admin email address
3. **Password**: Strong password (min 8 characters)
4. **Confirm Password**: Re-enter password

#### Step 7: License Activation
1. **Purchase Code**: Enter your CodeCanyon purchase code
2. **License Key**: Will be generated automatically
3. Click "Activate License" to verify with our servers

#### Step 8: Final Configuration
1. Review all settings
2. Click "Complete Installation"
3. The installer will finalize the setup

#### Step 9: Post-Installation
1. Delete the `installer` directory for security
2. Access your admin panel at `/admin`
3. Configure additional settings as needed

### Method 2: Manual Installation

For advanced users who prefer command-line installation.

#### Step 1: Download and Extract
```bash
# Download from CodeCanyon and extract
unzip greenweb-v1.0.0.zip
cd greenweb
```

#### Step 2: Install Dependencies
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node.js dependencies and build assets
npm install
npm run build
```

#### Step 3: Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

#### Step 4: Configure Environment
Edit the `.env` file with your settings:

```env
APP_NAME="Your App Name"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

LICENSE_KEY=your_license_key
CODECANYON_PURCHASE_CODE=your_purchase_code
```

#### Step 5: Database Setup
```bash
# Run migrations
php artisan migrate

# Seed initial data
php artisan db:seed
```

#### Step 6: Storage Configuration
```bash
# Create storage link
php artisan storage:link

# Set permissions
chmod -R 755 storage bootstrap/cache
```

#### Step 7: Create Admin User
```bash
php artisan make:admin-user
```

#### Step 8: Optimize for Production
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache
```

## Multi-Tenant Setup

### Single-Domain Multi-Tenancy (Subdomains)

#### Step 1: DNS Configuration
Configure your DNS to point all subdomains to your server:
```
*.yourdomain.com A *************
```

#### Step 2: Web Server Configuration

**Apache (.htaccess)**
```apache
RewriteEngine On
RewriteCond %{HTTP_HOST} ^(.+)\.yourdomain\.com$
RewriteRule ^(.*)$ /index.php [QSA,L]
```

**Nginx**
```nginx
server {
    listen 80;
    server_name *.yourdomain.com;
    root /var/www/greenweb/public;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

#### Step 3: Environment Configuration
```env
TENANT_DATABASE_PREFIX=tenant_
MAIN_DOMAIN=yourdomain.com
```

### Multi-Domain Multi-Tenancy

#### Step 1: Domain Configuration
Point each tenant domain to your server via DNS.

#### Step 2: SSL Certificates
Obtain SSL certificates for each domain (use Let's Encrypt for free certificates).

#### Step 3: Web Server Configuration
Configure your web server to handle multiple domains pointing to the same application.

## Troubleshooting

### Common Issues

#### 1. Permission Errors
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache
```

#### 2. Database Connection Failed
- Verify database credentials
- Ensure database server is running
- Check firewall settings
- Verify user has proper privileges

#### 3. License Activation Failed
- Check internet connectivity
- Verify purchase code is correct
- Ensure domain is accessible from our servers
- Contact support if issues persist

#### 4. 500 Internal Server Error
- Check error logs: `storage/logs/laravel.log`
- Verify file permissions
- Ensure all PHP extensions are installed
- Check web server configuration

#### 5. Assets Not Loading
- Run `php artisan storage:link`
- Check file permissions
- Verify web server configuration
- Clear browser cache

### Performance Optimization

#### 1. Enable OPcache
Add to your PHP configuration:
```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

#### 2. Configure Redis (Optional)
```env
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

#### 3. Enable Gzip Compression
**Apache (.htaccess)**
```apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

**Nginx**
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

## Security Hardening

### 1. Hide Sensitive Files
Ensure these files are not accessible via web:
- `.env`
- `composer.json`
- `package.json`
- `artisan`

### 2. Disable Directory Listing
**Apache (.htaccess)**
```apache
Options -Indexes
```

**Nginx**
```nginx
autoindex off;
```

### 3. Set Security Headers
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

## Post-Installation Steps

### 1. Configure Email
Set up SMTP settings in the admin panel for:
- User registration emails
- Password reset emails
- Notification emails

### 2. Configure Backups
Set up automated backups:
```bash
# Add to crontab
0 2 * * * cd /path/to/greenweb && php artisan backup:run
```

### 3. Set Up Monitoring
- Configure error monitoring (Sentry, Bugsnag)
- Set up uptime monitoring
- Configure performance monitoring

### 4. SSL Certificate
Install SSL certificate for secure connections:
```bash
# Using Let's Encrypt
certbot --nginx -d yourdomain.com
```

## Support

If you encounter any issues during installation:

1. Check the troubleshooting section above
2. Review the error logs
3. Search our documentation
4. Contact our support team with:
   - Error messages
   - Server configuration
   - Steps to reproduce the issue

---

**Congratulations!** You have successfully installed GreenWeb. Access your admin panel and start building your SaaS platform.
