<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Partner extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'description',
        'logo',
        'website',
        'industry',
        'sort_order',
        'is_featured',
        'is_active',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopePartners($query)
    {
        return $query->where('type', 'partner');
    }

    public function scopeClients($query)
    {
        return $query->where('type', 'client');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getLogoUrlAttribute()
    {
        if ($this->logo && Storage::disk('public')->exists($this->logo)) {
            return asset('storage/' . $this->logo);
        }
        return null;
    }

    public function getTypeNameAttribute()
    {
        return match($this->type) {
            'partner' => 'Partner',
            'client' => 'Client',
            default => ucfirst($this->type),
        };
    }

    // Static methods
    public static function getPartners($limit = null)
    {
        $query = static::active()->partners()->ordered();
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    public static function getClients($limit = null)
    {
        $query = static::active()->clients()->ordered();
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    public static function getFeaturedPartners($limit = 8)
    {
        return static::active()->featured()->partners()->ordered()->limit($limit)->get();
    }

    public static function getFeaturedClients($limit = 8)
    {
        return static::active()->featured()->clients()->ordered()->limit($limit)->get();
    }
}
