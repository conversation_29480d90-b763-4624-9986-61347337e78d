<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing key-value structure table
        Schema::drop('general_settings');

        // Create with proper column structure that matches the model
        Schema::create('general_settings', function (Blueprint $table) {
            $table->id();

            // Basic Site Information
            $table->string('application_name')->nullable();
            $table->string('tagline')->nullable();
            $table->string('catchphrase')->nullable();
            $table->text('vision')->nullable();
            $table->text('mission')->nullable();
            $table->longText('about_us')->nullable();
            $table->string('copyright')->nullable();

            // Images and Branding
            $table->string('favicon')->nullable();
            $table->string('logo')->nullable();
            $table->string('header_image')->nullable();
            $table->string('banner_image')->nullable();

            // Contact Information
            $table->string('phone1')->nullable();
            $table->string('phone2')->nullable();
            $table->string('email1')->nullable();
            $table->string('email2')->nullable();
            $table->text('address')->nullable();
            $table->text('map')->nullable();

            // System Configuration
            $table->string('timezone')->nullable();
            $table->string('currency')->nullable();
            $table->string('default_language')->nullable();

            // Email Configuration
            $table->string('email_from')->nullable();
            $table->string('smtp_host')->nullable();
            $table->integer('smtp_port')->nullable();
            $table->string('smtp_user')->nullable();
            $table->string('smtp_pass')->nullable();

            // Social Media Links
            $table->string('facebook_link')->nullable();
            $table->string('twitter_link')->nullable();
            $table->string('google_link')->nullable();
            $table->string('youtube_link')->nullable();
            $table->string('linkedin_link')->nullable();
            $table->string('instagram_link')->nullable();
            $table->string('whatsapp_link')->nullable();

            // Security & Integrations
            $table->string('recaptcha_site_key')->nullable();
            $table->string('recaptcha_secret_key')->nullable();
            $table->string('recaptcha_lang')->nullable();

            // SMS Configuration
            $table->string('sms_senderid')->nullable();
            $table->string('sms_username')->nullable();
            $table->string('sms_password')->nullable();

            // Push Notifications
            $table->string('push_public_key')->nullable();
            $table->string('push_private_key')->nullable();

            // SEO
            $table->text('seo_keywords')->nullable();

            $table->timestamps();
        });

        // Create default settings record
        DB::table('general_settings')->insert([
            'application_name' => 'Fair Price Ventures',
            'tagline' => 'Your Trusted Business Partner for Innovative Solutions and Exceptional Service',
            'catchphrase' => 'Delivering exceptional value through innovative solutions and unmatched service excellence.',
            'timezone' => 'UTC',
            'currency' => 'USD',
            'default_language' => 'en',
            'copyright' => '© ' . date('Y') . ' Fair Price Ventures. All rights reserved.',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
