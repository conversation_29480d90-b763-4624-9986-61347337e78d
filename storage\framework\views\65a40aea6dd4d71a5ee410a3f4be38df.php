<?php $__env->startSection('content'); ?>
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900"><?php echo e($blogPost->title); ?></h1>
            <div class="flex space-x-2">
                <?php if($blogPost->is_published && $blogPost->published_at <= now()): ?>
                    <a href="<?php echo e(route('blog.show', $blogPost)); ?>" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        View Live Post
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('admin.blog-posts.edit', $blogPost)); ?>" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Post
                </a>
                <a href="<?php echo e(route('admin.blog-posts.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Posts
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Featured Image -->
                <?php if($blogPost->featured_image): ?>
                    <div class="mb-6">
                        <img src="<?php echo e(asset('storage/' . $blogPost->featured_image)); ?>" alt="<?php echo e($blogPost->title); ?>" class="w-full h-64 object-cover rounded-lg">
                    </div>
                <?php endif; ?>

                <!-- Excerpt -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Excerpt</h3>
                    <p class="text-gray-700 italic"><?php echo e($blogPost->excerpt); ?></p>
                </div>

                <!-- Content -->
                <div class="prose prose-lg max-w-none">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Content</h3>
                    <?php echo $blogPost->content; ?>

                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Post Status -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Post Status</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Status:</span>
                            <?php if($blogPost->is_published && $blogPost->published_at <= now()): ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Published
                                </span>
                            <?php elseif($blogPost->is_published && $blogPost->published_at > now()): ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Scheduled
                                </span>
                            <?php else: ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    Draft
                                </span>
                            <?php endif; ?>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Featured:</span>
                            <?php if($blogPost->is_featured): ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Featured
                                </span>
                            <?php else: ?>
                                <span class="text-sm text-gray-500">No</span>
                            <?php endif; ?>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Views:</span>
                            <span class="text-sm text-gray-900"><?php echo e(number_format($blogPost->views_count)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Slug:</span>
                            <span class="text-sm text-gray-900 font-mono"><?php echo e($blogPost->slug); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Author & Category -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Author & Category</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-gray-600">Author:</span>
                            <div class="flex items-center space-x-2 mt-1">
                                <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    <?php echo e(substr($blogPost->user->name, 0, 1)); ?>

                                </div>
                                <span class="text-sm text-gray-900"><?php echo e($blogPost->user->name); ?></span>
                            </div>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">Category:</span>
                            <div class="mt-1">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" style="background-color: <?php echo e($blogPost->category->color); ?>20; color: <?php echo e($blogPost->category->color); ?>;">
                                    <?php echo e($blogPost->category->name); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tags -->
                <?php if($blogPost->tags->count() > 0): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php $__currentLoopData = $blogPost->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white" style="background-color: <?php echo e($tag->color); ?>;">
                                <?php echo e($tag->name); ?>

                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Post Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Post Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Created:</span>
                            <div class="text-gray-900"><?php echo e($blogPost->created_at->format('M d, Y g:i A')); ?></div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900"><?php echo e($blogPost->updated_at->format('M d, Y g:i A')); ?></div>
                        </div>
                        <?php if($blogPost->published_at): ?>
                            <div>
                                <span class="text-gray-600">Published:</span>
                                <div class="text-gray-900"><?php echo e($blogPost->published_at->format('M d, Y g:i A')); ?></div>
                            </div>
                        <?php endif; ?>
                        <?php if($blogPost->is_published): ?>
                            <div>
                                <span class="text-gray-600">URL:</span>
                                <div class="text-blue-600 hover:text-blue-800">
                                    <a href="<?php echo e(route('blog.show', $blogPost)); ?>" target="_blank">/blog/<?php echo e($blogPost->slug); ?></a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- SEO Information -->
                <?php if($blogPost->meta_title || $blogPost->meta_description || $blogPost->meta_keywords): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Information</h3>
                    <div class="space-y-3 text-sm">
                        <?php if($blogPost->meta_title): ?>
                        <div>
                            <span class="text-gray-600">Meta Title:</span>
                            <div class="text-gray-900"><?php echo e($blogPost->meta_title); ?></div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($blogPost->meta_description): ?>
                        <div>
                            <span class="text-gray-600">Meta Description:</span>
                            <div class="text-gray-900"><?php echo e($blogPost->meta_description); ?></div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($blogPost->meta_keywords): ?>
                        <div>
                            <span class="text-gray-600">Meta Keywords:</span>
                            <div class="text-gray-900"><?php echo e($blogPost->meta_keywords); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="<?php echo e(route('admin.blog-posts.edit', $blogPost)); ?>" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Post
                        </a>
                        
                        <form action="<?php echo e(route('admin.blog-posts.destroy', $blogPost)); ?>" method="POST" onsubmit="return confirm('Are you sure you want to delete this blog post? This action cannot be undone.')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Delete Post
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/admin/blog-posts/show.blade.php ENDPATH**/ ?>