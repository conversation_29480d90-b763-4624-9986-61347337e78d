@extends('layouts.public')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Our Projects</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover our portfolio of successful projects and see how we've helped businesses achieve their goals through innovative solutions and strategic partnerships.
            </p>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
@if($featuredProjects->count() > 0)
<section class="bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Featured Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Highlighting some of our most impactful and successful project implementations.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredProjects as $index => $project)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($project->featured_image_url)
                        <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" class="w-full h-48 object-cover">
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                            <div class="text-white text-center">
                                <i class="fas fa-project-diagram text-4xl mb-2"></i>
                                <h4 class="text-lg font-bold">{{ $project->title }}</h4>
                            </div>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">{{ $project->title }}</h3>
                            {!! $project->status_badge !!}
                        </div>
                        
                        @if($project->client)
                            <p class="text-green-600 font-semibold mb-2">{{ $project->client }}</p>
                        @endif
                        
                        <p class="text-gray-600 mb-4">{{ Str::limit($project->description, 120) }}</p>
                        
                        @if($project->technologies && count($project->technologies) > 0)
                            <div class="flex flex-wrap gap-2 mb-4">
                                @foreach(array_slice($project->technologies, 0, 3) as $tech)
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                        {{ $tech }}
                                    </span>
                                @endforeach
                                @if(count($project->technologies) > 3)
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">
                                        +{{ count($project->technologies) - 3 }} more
                                    </span>
                                @endif
                            </div>
                        @endif
                        
                        <div class="flex justify-between items-center">
                            <a href="{{ route('projects.show', $project) }}" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                                View Details →
                            </a>
                            
                            @if($project->start_date)
                                <span class="text-sm text-gray-500">{{ $project->start_date->format('M Y') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- All Projects Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {{-- <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">All Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Browse through our complete portfolio of projects across various industries and technologies.
            </p>
        </div> --}}

        @if($projects->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($projects as $index => $project)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ ($index % 6) * 100 }}">
                        @if($project->featured_image_url)
                            <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                                <div class="text-white text-center">
                                    <i class="fas fa-project-diagram text-4xl mb-2"></i>
                                    <h4 class="text-lg font-bold">{{ $project->title }}</h4>
                                </div>
                            </div>
                        @endif
                        
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-bold text-gray-900">{{ $project->title }}</h3>
                                {!! $project->status_badge !!}
                            </div>
                            
                            @if($project->client)
                                <p class="text-green-600 font-semibold mb-2">{{ $project->client }}</p>
                            @endif
                            
                            <p class="text-gray-600 mb-4">{{ Str::limit($project->description, 120) }}</p>
                            
                            @if($project->technologies && count($project->technologies) > 0)
                                <div class="flex flex-wrap gap-2 mb-4">
                                    @foreach(array_slice($project->technologies, 0, 3) as $tech)
                                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                            {{ $tech }}
                                        </span>
                                    @endforeach
                                    @if(count($project->technologies) > 3)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">
                                            +{{ count($project->technologies) - 3 }} more
                                        </span>
                                    @endif
                                </div>
                            @endif
                            
                            <div class="flex justify-between items-center">
                                <a href="{{ route('projects.show', $project) }}" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                                    View Details →
                                </a>
                                
                                @if($project->start_date)
                                    <span class="text-sm text-gray-500">{{ $project->start_date->format('M Y') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                {{ $projects->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-project-diagram text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">No Projects Found</h3>
                <p class="text-gray-600">We're working on exciting new projects. Check back soon!</p>
            </div>
        @endif
    </div>
</section>
@endsection

@push('styles')
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
@endpush

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
@endpush
