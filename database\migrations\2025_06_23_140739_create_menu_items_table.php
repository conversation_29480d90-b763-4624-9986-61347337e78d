<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_items', function (Blueprint $table) {
            $table->id();
            $table->string('menu_location')->default('main'); // main, footer, etc.
            $table->string('title'); // Display text
            $table->string('url')->nullable(); // URL/link
            $table->string('route_name')->nullable(); // Laravel route name
            $table->json('route_params')->nullable(); // Route parameters
            $table->string('target')->default('_self'); // _self, _blank, etc.
            $table->string('css_class')->nullable(); // Custom CSS classes
            $table->string('icon')->nullable(); // Icon class (e.g., fas fa-home)
            $table->foreignId('parent_id')->nullable()->constrained('menu_items')->onDelete('cascade'); // For nested menus
            $table->integer('sort_order')->default(0); // Display order
            $table->boolean('is_active')->default(true); // Visibility
            $table->text('description')->nullable(); // Admin description
            $table->timestamps();

            // Indexes
            $table->index(['menu_location', 'parent_id', 'sort_order']);
            $table->index(['is_active', 'sort_order']);
            $table->index('parent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_items');
    }
};
