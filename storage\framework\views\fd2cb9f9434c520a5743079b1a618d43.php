<?php $__env->startPush('styles'); ?>
<!-- Modern Fonts -->
<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700,800&display=swap" rel="stylesheet" />

<!-- Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

<style>
    body { font-family: 'Inter', sans-serif; }
    .gradient-bg { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
    .gradient-text { background: linear-gradient(135deg, #10b981 0%, #059669 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    .glass-effect { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.1); }
    .hover-scale { transition: transform 0.3s ease; }
    .hover-scale:hover { transform: scale(1.05); }
    .floating { animation: floating 3s ease-in-out infinite; }
    @keyframes floating { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
    .green-primary { color: #059669; }
    .bg-green-primary { background-color: #059669; }
    .border-green-primary { border-color: #059669; }
    .hover-bg-green-primary:hover { background-color: #047857; }
    .hover-text-green-primary:hover { color: #059669; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative h-[500px] flex items-center justify-center overflow-hidden">
    <!-- Background Image -->
    <?php if($globalSettings->banner_image): ?>
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-70"
             style="background-image: url('<?php echo e(asset('storage/' . $globalSettings->banner_image)); ?>');">
        </div>
    <?php endif; ?>

    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-green-900 to-green-800 opacity-75"></div>

    <!-- Beautiful SVG Background Pattern -->
    <?php if (isset($component)) { $__componentOriginal0b276ea3c6be839613bcdc6b32037890 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0b276ea3c6be839613bcdc6b32037890 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.background-patterns','data' => ['variant' => 'hero','opacity' => '0.15']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('background-patterns'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'hero','opacity' => '0.15']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $attributes = $__attributesOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $component = $__componentOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__componentOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>

    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-72 h-72 bg-white mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div class="absolute top-0 right-4 w-72 h-72 bg-green-300 mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
        <div class="absolute -bottom-8 left-20 w-72 h-72 bg-emerald-300 mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="space-y-8" data-aos="fade-up" data-aos-duration="1000">
            <h1 class="text-5xl md:text-5xl font-bold text-white leading-tight">
                <?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?>

            </h1>
            <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto">
                <?php echo e($globalSettings->tagline ?? 'Your Trusted Business Partner for Innovative Solutions and Exceptional Service'); ?>

            </p>
            <p class="text-lg text-white/80 max-w-4xl mx-auto">
                <?php echo e($globalSettings->catchphrase ?? 'Delivering exceptional value through innovative solutions and unmatched service excellence.'); ?>

            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
                <a href="<?php echo e(route('services.index')); ?>" class="bg-white text-green-600 px-8 py-4 rounded-md font-semibold hover:bg-gray-100 transition-all duration-300 hover-scale shadow-lg">
                    Explore Our Services
                </a>
                <a href="<?php echo e(route('contact')); ?>" class="border-2 border-white text-white px-8 py-4 rounded-md font-semibold hover:bg-white hover:text-green-600 transition-all duration-300 hover-scale">
                    Get In Touch
                </a>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 floating">
            <div class="w-4 h-4 bg-white  opacity-60"></div>
        </div>
        <div class="absolute top-40 right-20 floating" style="animation-delay: 1s;">
            <div class="w-6 h-6 bg-white  opacity-40"></div>
        </div>
        <div class="absolute bottom-20 left-1/4 floating" style="animation-delay: 2s;">
            <div class="w-3 h-3 bg-white  opacity-50"></div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <a href="#about" class="text-white hover:text-gray-200 transition-colors duration-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </a>
    </div>
</section>

<!-- About Section -->
<section id="about" class="relative py-20 bg-white overflow-hidden">
    <!-- Subtle Background Pattern -->
    <?php if (isset($component)) { $__componentOriginal0b276ea3c6be839613bcdc6b32037890 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0b276ea3c6be839613bcdc6b32037890 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.background-patterns','data' => ['variant' => 'subtle','opacity' => '0.08']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('background-patterns'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'subtle','opacity' => '0.08']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $attributes = $__attributesOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $component = $__componentOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__componentOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">About Us</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    <?php echo e(Str::limit($globalSettings->about_us, 200)); ?>... <a href="<?php echo e(route('pages.show', 'about')); ?>" class="text-green-600 hover:text-green-700 transition-colors duration-300">Read More</a>
            </p>

        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div data-aos="fade-right">
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Our Mission</h3>
                <p class="text-lg text-gray-600 mb-6">
                    <?php echo e($globalSettings->mission ?? 'We are committed to delivering innovative, cost-effective solutions that exceed our clients\' expectations while fostering long-term partnerships built on trust, integrity, and mutual success.'); ?>

                </p>

                <h3 class="text-3xl font-bold text-gray-900 mb-6">Our Vision</h3>
                <p class="text-lg text-gray-600 mb-6">
                    <?php echo e($globalSettings->vision ?? 'We are committed to delivering innovative, cost-effective solutions that exceed our clients\' expectations while fostering long-term partnerships built on trust, integrity, and mutual success.'); ?>

                </p>
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">500+</div>
                        <div class="text-gray-600">Happy Clients</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">10+</div>
                        <div class="text-gray-600">Years Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">1000+</div>
                        <div class="text-gray-600">Projects Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">24/7</div>
                        <div class="text-gray-600">Support</div>
                    </div>
                </div>
            </div>
            <div data-aos="fade-left">
                <?php if($globalSettings->header_image ?? null): ?>
                    <img src="<?php echo e(asset('storage/' . $globalSettings->header_image)); ?>" alt="About Us" class="rounded-lg shadow-2xl hover-scale">
                <?php else: ?>
                    <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-lg shadow-2xl h-96 flex items-center justify-center">
                        <div class="text-white text-center">
                            <i class="fas fa-building text-6xl mb-4"></i>
                            <h4 class="text-2xl font-bold"><?php echo e($globalSettings->application_name ?? 'Fair Price Ventures'); ?></h4>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
<?php if($featuredServices->count() > 0): ?>
<section class="relative py-20 bg-gray-50 overflow-hidden">
    <!-- Default Background Pattern -->
    <?php if (isset($component)) { $__componentOriginal0b276ea3c6be839613bcdc6b32037890 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0b276ea3c6be839613bcdc6b32037890 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.background-patterns','data' => ['variant' => 'default','opacity' => '0.06']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('background-patterns'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'default','opacity' => '0.06']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $attributes = $__attributesOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $component = $__componentOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__componentOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Our Featured Services</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover comprehensive business solutions tailored to meet your unique needs and drive your success.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-xl shadow-lg p-8 hover-scale transition-all duration-300" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                <?php if($service->featured_image): ?>
                    <img src="<?php echo e(asset('storage/' . $service->featured_image)); ?>" alt="<?php echo e($service->title); ?>" class="w-full h-48 object-cover rounded-lg mb-6">
                <?php endif; ?>

                <?php if($service->icon): ?>
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl text-green-600"><?php echo e($service->icon); ?></span>
                    </div>
                <?php endif; ?>

                <h3 class="text-xl font-bold text-gray-900 mb-4"><?php echo e($service->title); ?></h3>
                <p class="text-gray-600 mb-6"><?php echo e($service->description); ?></p>

                <?php if($service->price): ?>
                    <p class="text-green-600 font-semibold mb-6"><?php echo e($service->formatted_price); ?></p>
                <?php endif; ?>

                <a href="<?php echo e(route('services.show', $service)); ?>" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                    Learn More →
                </a>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('services.index')); ?>" class="bg-green-600 text-white px-8 py-4 rounded-md font-semibold hover:bg-green-700 transition-all duration-300 hover-scale shadow-lg">
                View All Services
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Latest Blog Posts Section -->


<!-- Featured Projects Section -->
<?php if($featuredProjects->count() > 0): ?>
<section id="projects" class="relative py-20 bg-gray-50 overflow-hidden">
    <!-- Default Background Pattern -->
    <?php if (isset($component)) { $__componentOriginal0b276ea3c6be839613bcdc6b32037890 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0b276ea3c6be839613bcdc6b32037890 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.background-patterns','data' => ['variant' => 'default','opacity' => '0.05']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('background-patterns'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'default','opacity' => '0.05']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $attributes = $__attributesOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__attributesOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0b276ea3c6be839613bcdc6b32037890)): ?>
<?php $component = $__componentOriginal0b276ea3c6be839613bcdc6b32037890; ?>
<?php unset($__componentOriginal0b276ea3c6be839613bcdc6b32037890); ?>
<?php endif; ?>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Featured Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover some of our most successful projects and see how we've helped businesses achieve their goals.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredProjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover-scale" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                    <?php if($project->featured_image_url): ?>
                        <img src="<?php echo e($project->featured_image_url); ?>" alt="<?php echo e($project->title); ?>" class="w-full h-48 object-cover">
                    <?php else: ?>
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                            <div class="text-white text-center">
                                <i class="fas fa-project-diagram text-4xl mb-2"></i>
                                <h4 class="text-lg font-bold"><?php echo e($project->title); ?></h4>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900"><?php echo e($project->title); ?></h3>
                            <?php echo $project->status_badge; ?>

                        </div>

                        <?php if($project->client): ?>
                            <p class="text-green-600 font-semibold mb-2"><?php echo e($project->client); ?></p>
                        <?php endif; ?>

                        <p class="text-gray-600 mb-4"><?php echo e($project->description); ?></p>

                        <?php if($project->technologies && count($project->technologies) > 0): ?>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <?php $__currentLoopData = array_slice($project->technologies, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium ">
                                        <?php echo e($tech); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if(count($project->technologies) > 3): ?>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium ">
                                        +<?php echo e(count($project->technologies) - 3); ?> more
                                    </span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="flex justify-between items-center">
                            <?php if($project->project_url): ?>
                                <a href="<?php echo e($project->project_url); ?>" target="_blank" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                                    View Project →
                                </a>
                            <?php else: ?>
                                <span class="text-gray-400">Project Details</span>
                            <?php endif; ?>

                            <?php if($project->start_date): ?>
                                <span class="text-sm text-gray-500"><?php echo e($project->start_date->format('M Y')); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-12">
            <a href="#" class="bg-green-600 text-white px-8 py-4  font-semibold hover:bg-green-700 transition-all duration-300 hover-scale shadow-lg">
                View All Projects
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Team Section -->


<!-- Partners & Clients Section -->
<?php if($featuredPartners->count() > 0 || $featuredClients->count() > 0): ?>
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Partners & Clients</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We're proud to work with amazing organizations and deliver exceptional results for our valued clients.
            </p>
        </div>

        <!-- Partners -->
        <?php if($featuredPartners->count() > 0): ?>
        <div class="mb-16">
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Our Partners</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                <?php $__currentLoopData = $featuredPartners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                        <?php if($partner->logo_url): ?>
                            <img src="<?php echo e($partner->logo_url); ?>" alt="<?php echo e($partner->name); ?>" class="max-w-full max-h-16 object-contain filter grayscale hover:grayscale-0 transition-all duration-300">
                        <?php else: ?>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <span class="text-lg font-bold text-white"><?php echo e(substr($partner->name, 0, 1)); ?></span>
                                </div>
                                <span class="text-xs font-medium text-gray-600"><?php echo e($partner->name); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Clients -->
        <?php if($featuredClients->count() > 0): ?>
        <div>
            <h3 class="text-2xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Our Clients</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                <?php $__currentLoopData = $featuredClients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                        <?php if($client->logo_url): ?>
                            <img src="<?php echo e($client->logo_url); ?>" alt="<?php echo e($client->name); ?>" class="max-w-full max-h-16 object-contain filter grayscale hover:grayscale-0 transition-all duration-300">
                        <?php else: ?>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <span class="text-lg font-bold text-white"><?php echo e(substr($client->name, 0, 1)); ?></span>
                                </div>
                                <span class="text-xs font-medium text-gray-600"><?php echo e($client->name); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?>

<!-- Testimonials Section -->
<?php if($featuredTestimonials->count() > 0): ?>
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Don't just take our word for it. Here's what our satisfied clients have to say about working with us.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredTestimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-gray-50 rounded-lg p-8 shadow-sm hover:shadow-md transition-shadow duration-300" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                    <div class="flex items-center mb-4">
                        <?php echo $testimonial->stars_html; ?>

                    </div>

                    <blockquote class="text-gray-700 mb-6 italic">
                        "<?php echo e($testimonial->testimonial); ?>"
                    </blockquote>

                    <div class="flex items-center">
                        <?php if($testimonial->client_image_url): ?>
                            <img src="<?php echo e($testimonial->client_image_url); ?>" alt="<?php echo e($testimonial->client_name); ?>" class="w-12 h-12  object-cover mr-4">
                        <?php else: ?>
                            <div class="w-12 h-12  bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center mr-4">
                                <span class="text-lg font-bold text-white"><?php echo e(substr($testimonial->client_name, 0, 1)); ?></span>
                            </div>
                        <?php endif; ?>

                        <div>
                            <div class="font-semibold text-gray-900"><?php echo e($testimonial->client_name); ?></div>
                            <?php if($testimonial->client_position || $testimonial->client_company): ?>
                                <div class="text-sm text-gray-600">
                                    <?php echo e($testimonial->client_position); ?>

                                    <?php if($testimonial->client_position && $testimonial->client_company): ?> at <?php endif; ?>
                                    <?php echo e($testimonial->client_company); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- View All Testimonials Button -->
        <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="300">
            <a href="<?php echo e(route('testimonials.index')); ?>" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                View All Testimonials
            </a>
            <div class="mt-4">
                <a href="<?php echo e(route('testimonials.create')); ?>" class="text-green-600 hover:text-green-800 font-medium">
                    Share Your Experience →
                </a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action Section -->
<section class="py-20 gradient-bg text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center" data-aos="fade-up">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Transform Your Business?</h2>
        <p class="text-xl mb-8 text-white/90 max-w-3xl mx-auto">
           We are here to serve you. Reach out to us for inquiries, partnerships, or to learn more about our services.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a href="<?php echo e(route('contact')); ?>" class="bg-white text-green-600 px-8 py-4 rounded-md font-semibold hover:bg-gray-100 transition-all duration-300 hover-scale shadow-lg">
                Contact Us Today
            </a>
            <a href="<?php echo e(route('services.index')); ?>" class="border-2 border-white text-white px-8 py-4 rounded-md font-semibold hover:bg-white hover:text-green-600 transition-all duration-300 hover-scale">
                Explore Services
            </a>
        </div>
    </div>
</section>

<!-- Newsletter Popup -->
<?php if($globalSettings->newsletter_popup_enabled): ?>
    <div x-data="{
        show: false,
        init() {
            setTimeout(() => {
                this.show = true;
            }, <?php echo e(($globalSettings->newsletter_popup_delay ?? 5) * 1000); ?>);
        }
    }"
    x-show="show"
    x-transition
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    style="display: none;">
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 relative">
            <button @click="show = false" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <div class="text-center">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">
                    <?php echo e($globalSettings->newsletter_popup_title ?? 'Stay Updated!'); ?>

                </h3>
                <p class="text-gray-600 mb-6">
                    <?php echo e($globalSettings->newsletter_popup_message ?? 'Subscribe to our newsletter to get the latest updates and exclusive offers!'); ?>

                </p>

                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('newsletter-signup', ['source' => 'popup']);

$__html = app('livewire')->mount($__name, $__params, 'lw-3297361295-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/home.blade.php ENDPATH**/ ?>