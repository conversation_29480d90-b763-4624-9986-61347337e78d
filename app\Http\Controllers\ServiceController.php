<?php

namespace App\Http\Controllers;

use App\Models\Service;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    public function index()
    {
        $services = Service::published()->ordered()->paginate(12);

        return view('services.index', compact('services'));
    }

    public function show(Service $service)
    {
        if (!$service->is_active) {
            abort(404);
        }

        $relatedServices = Service::active()
            ->where('id', '!=', $service->id)
            ->limit(3)
            ->get();

        $serviceFaqs = $service->activeFaqs;

        return view('services.show', compact('service', 'relatedServices', 'serviceFaqs'));
    }
}
