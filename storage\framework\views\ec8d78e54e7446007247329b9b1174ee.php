<?php $__env->startSection('title', 'Gallery'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Photo Gallery</h1>
            <p class="text-xl text-green-100 max-w-3xl mx-auto">
                Explore our collection of beautiful moments and memories captured through our lens.
            </p>
        </div>
    </div>
</section>

<!-- Featured Galleries -->
<?php if($featuredGalleries->count() > 0): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Galleries</h2>
            <p class="text-lg text-gray-600">Highlighted collections from our portfolio</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredGalleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div class="relative h-64 bg-cover bg-center" style="background-image: url('<?php echo e($gallery->cover_image_url); ?>')">
                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                        <div class="p-6 text-white">
                            <span class="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium mb-2 inline-block">
                                Featured
                            </span>
                            <h3 class="text-xl font-semibold"><?php echo e($gallery->title); ?></h3>
                            <?php if($gallery->category): ?>
                                <p class="text-green-200 text-sm"><?php echo e($gallery->category); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <?php if($gallery->description): ?>
                        <p class="text-gray-600 mb-4"><?php echo e(Str::limit($gallery->description, 100)); ?></p>
                    <?php endif; ?>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">
                            <i class="fas fa-images mr-1"></i>
                            <?php echo e($gallery->image_count); ?> <?php echo e(Str::plural('photo', $gallery->image_count)); ?>

                        </span>
                        
                        <a href="<?php echo e(route('gallery.show', $gallery)); ?>" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                            View Gallery
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Search and Filter Section -->
<section class="py-8 bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <form method="GET" action="<?php echo e(route('gallery.index')); ?>" class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" placeholder="Search galleries..." 
                       value="<?php echo e(request('search')); ?>" 
                       class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500">
            </div>
            
            <select name="category" class="px-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500">
                <option value="">All Categories</option>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($category); ?>" <?php echo e(request('category') == $category ? 'selected' : ''); ?>><?php echo e($category); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            
            <label class="flex items-center">
                <input type="checkbox" name="featured" value="1" <?php echo e(request('featured') ? 'checked' : ''); ?> 
                       class="mr-2 text-green-600 focus:ring-green-500">
                Featured Only
            </label>
            
            <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            
            <?php if(request()->hasAny(['search', 'category', 'featured'])): ?>
                <a href="<?php echo e(route('gallery.index')); ?>" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-times mr-1"></i>Clear
                </a>
            <?php endif; ?>
        </form>
    </div>
</section>

<!-- Galleries Grid -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if($galleries->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php $__currentLoopData = $galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
                    <div class="relative h-48 bg-cover bg-center" style="background-image: url('<?php echo e($gallery->cover_image_url); ?>')">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                            <a href="<?php echo e(route('gallery.show', $gallery)); ?>" class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white text-gray-900 px-4 py-2 rounded-md font-medium">
                                View Gallery
                            </a>
                        </div>
                        
                        <!-- Gallery badges -->
                        <div class="absolute top-3 right-3">
                            <?php if($gallery->is_featured): ?>
                                <span class="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                                    Featured
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="<?php echo e(route('gallery.show', $gallery)); ?>" class="hover:text-green-600 transition-colors">
                                <?php echo e($gallery->title); ?>

                            </a>
                        </h3>
                        
                        <?php if($gallery->category): ?>
                            <p class="text-sm text-green-600 mb-2"><?php echo e($gallery->category); ?></p>
                        <?php endif; ?>
                        
                        <?php if($gallery->description): ?>
                            <p class="text-gray-600 text-sm mb-3"><?php echo e(Str::limit($gallery->description, 80)); ?></p>
                        <?php endif; ?>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>
                                <i class="fas fa-images mr-1"></i>
                                <?php echo e($gallery->image_count); ?> <?php echo e(Str::plural('photo', $gallery->image_count)); ?>

                            </span>
                            <span><?php echo e($gallery->created_at->format('M Y')); ?></span>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <div class="mt-12 flex justify-center">
                <?php echo e($galleries->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-16">
                <i class="fas fa-images text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-2xl font-semibold text-gray-900 mb-2">No Galleries Found</h3>
                <p class="text-gray-600 mb-6">
                    <?php if(request()->hasAny(['search', 'category', 'featured'])): ?>
                        No galleries match your search criteria. Try adjusting your filters.
                    <?php else: ?>
                        There are no galleries available at the moment. Check back soon!
                    <?php endif; ?>
                </p>
                <?php if(request()->hasAny(['search', 'category', 'featured'])): ?>
                    <a href="<?php echo e(route('gallery.index')); ?>" class="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors">
                        View All Galleries
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Categories Section -->
<?php if($categories->count() > 0 && !request()->hasAny(['search', 'category', 'featured'])): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Browse by Category</h2>
            <p class="text-lg text-gray-600">Explore galleries organized by themes and topics</p>
        </div>
        
        <div class="flex flex-wrap justify-center gap-4">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('gallery.category', $category)); ?>" 
                   class="bg-white hover:bg-green-50 border border-gray-200 hover:border-green-300 px-6 py-3 rounded-full text-gray-700 hover:text-green-700 transition-colors duration-300">
                    <?php echo e($category); ?>

                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/gallery/index.blade.php ENDPATH**/ ?>