<?php

if (!function_exists('setting')) {
    /**
     * Get a setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        return \App\Models\GeneralSetting::get($key, $default);
    }
}

if (!function_exists('settings')) {
    /**
     * Get the settings instance
     *
     * @return \App\Models\GeneralSetting
     */
    function settings()
    {
        return \App\Models\GeneralSetting::getInstance();
    }
}

if (!function_exists('app_name')) {
    /**
     * Get the application name
     *
     * @return string
     */
    function app_name()
    {
        return setting('application_name', config('app.name', 'Fair Price Ventures'));
    }
}

if (!function_exists('app_logo')) {
    /**
     * Get the application logo URL
     *
     * @return string|null
     */
    function app_logo()
    {
        $settings = settings();
        return $settings->logo_url;
    }
}

if (!function_exists('app_favicon')) {
    /**
     * Get the application favicon URL
     *
     * @return string|null
     */
    function app_favicon()
    {
        $settings = settings();
        return $settings->favicon_url;
    }
}

if (!function_exists('contact_info')) {
    /**
     * Get contact information
     *
     * @return array
     */
    function contact_info()
    {
        $settings = settings();
        return $settings->contact_info;
    }
}

if (!function_exists('social_links')) {
    /**
     * Get social media links
     *
     * @return array
     */
    function social_links()
    {
        $settings = settings();
        return $settings->social_links;
    }
}

if (!function_exists('is_smtp_configured')) {
    /**
     * Check if SMTP is configured
     *
     * @return bool
     */
    function is_smtp_configured()
    {
        return settings()->isSmtpConfigured();
    }
}

if (!function_exists('is_recaptcha_configured')) {
    /**
     * Check if reCAPTCHA is configured
     *
     * @return bool
     */
    function is_recaptcha_configured()
    {
        return settings()->isRecaptchaConfigured();
    }
}

if (!function_exists('recaptcha_site_key')) {
    /**
     * Get reCAPTCHA site key
     *
     * @return string|null
     */
    function recaptcha_site_key()
    {
        return setting('recaptcha_site_key');
    }
}

if (!function_exists('format_currency')) {
    /**
     * Format currency based on settings
     *
     * @param float $amount
     * @return string
     */
    function format_currency($amount)
    {
        $currency = setting('currency', 'USD');
        
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'KES' => 'KSh',
            'NGN' => '₦',
            'ZAR' => 'R',
        ];
        
        $symbol = $symbols[$currency] ?? $currency;
        
        return $symbol . number_format($amount, 2);
    }
}

if (!function_exists('app_timezone')) {
    /**
     * Get the application timezone
     *
     * @return string
     */
    function app_timezone()
    {
        return setting('timezone', config('app.timezone', 'UTC'));
    }
}

if (!function_exists('seo_keywords')) {
    /**
     * Get SEO keywords
     *
     * @return string
     */
    function seo_keywords()
    {
        return setting('seo_keywords', '');
    }
}
