<?php $__env->startSection('content'); ?>
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <div class="mb-6">
                    <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                    <p class="text-gray-600">Welcome to the Fair Price Ventures admin panel</p>
                </div>

                <!-- Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="bg-blue-50 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="text-blue-600 text-3xl mr-4">📝</div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Blog Posts</h3>
                                <p class="text-2xl font-bold text-blue-600"><?php echo e($stats['total_blog_posts']); ?></p>
                                <p class="text-sm text-gray-600"><?php echo e($stats['published_blog_posts']); ?> published</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="text-green-600 text-3xl mr-4">📧</div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Newsletter Subscribers</h3>
                                <p class="text-2xl font-bold text-green-600"><?php echo e($stats['total_newsletter_subscribers']); ?></p>
                                <p class="text-sm text-gray-600">Active subscribers</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="text-yellow-600 text-3xl mr-4">💬</div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Contact Enquiries</h3>
                                <p class="text-2xl font-bold text-yellow-600"><?php echo e($stats['total_contact_enquiries']); ?></p>
                                <p class="text-sm text-gray-600"><?php echo e($stats['unread_enquiries']); ?> unread</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="text-purple-600 text-3xl mr-4">👥</div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Users</h3>
                                <p class="text-2xl font-bold text-purple-600"><?php echo e($stats['total_users']); ?></p>
                                <p class="text-sm text-gray-600">Total users</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Recent Enquiries -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Contact Enquiries</h2>
                        <?php if($recent_enquiries->count() > 0): ?>
                            <div class="space-y-4">
                                <?php $__currentLoopData = $recent_enquiries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enquiry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border-l-4 <?php echo e($enquiry->is_read ? 'border-gray-300' : 'border-yellow-400'); ?> pl-4">
                                    <div class="flex items-center justify-between">
                                        <h3 class="font-semibold text-gray-900"><?php echo e($enquiry->name); ?></h3>
                                        <span class="text-sm text-gray-500"><?php echo e($enquiry->created_at->diffForHumans()); ?></span>
                                    </div>
                                    <p class="text-sm text-gray-600"><?php echo e($enquiry->subject); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e($enquiry->short_message); ?></p>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500">No recent enquiries</p>
                        <?php endif; ?>
                    </div>

                    <!-- Recent Blog Posts -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Blog Posts</h2>
                        <?php if($recent_blog_posts->count() > 0): ?>
                            <div class="space-y-4">
                                <?php $__currentLoopData = $recent_blog_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border-l-4 <?php echo e($post->is_published ? 'border-green-400' : 'border-gray-300'); ?> pl-4">
                                    <div class="flex items-center justify-between">
                                        <h3 class="font-semibold text-gray-900"><?php echo e(Str::limit($post->title, 40)); ?></h3>
                                        <span class="text-sm text-gray-500"><?php echo e($post->created_at->diffForHumans()); ?></span>
                                    </div>
                                    <p class="text-sm text-gray-600"><?php echo e($post->category->name); ?></p>
                                    <p class="text-xs text-gray-500">By <?php echo e($post->user->name); ?></p>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500">No recent blog posts</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <a href="<?php echo e(route('home')); ?>" target="_blank" class="bg-blue-600 text-white p-4 rounded-lg text-center hover:bg-blue-700 transition duration-300">
                            <div class="text-2xl mb-2">🏠</div>
                            <div class="text-sm font-semibold">View Website</div>
                        </a>

                        <a href="<?php echo e(route('admin.blog-posts.create')); ?>" class="bg-green-600 text-white p-4 rounded-lg text-center hover:bg-green-700 transition duration-300">
                            <div class="text-2xl mb-2">📝</div>
                            <div class="text-sm font-semibold">New Blog Post</div>
                        </a>

                        <a href="<?php echo e(route('admin.contact-enquiries.index')); ?>" class="bg-yellow-600 text-white p-4 rounded-lg text-center hover:bg-yellow-700 transition duration-300">
                            <div class="text-2xl mb-2">💬</div>
                            <div class="text-sm font-semibold">View Enquiries</div>
                        </a>

                        <a href="<?php echo e(route('admin.pages.create')); ?>" class="bg-purple-600 text-white p-4 rounded-lg text-center hover:bg-purple-700 transition duration-300">
                            <div class="text-2xl mb-2">📄</div>
                            <div class="text-sm font-semibold">New Page</div>
                        </a>

                        <a href="<?php echo e(route('admin.services.index')); ?>" class="bg-indigo-600 text-white p-4 rounded-lg text-center hover:bg-indigo-700 transition duration-300">
                            <div class="text-2xl mb-2">🛍️</div>
                            <div class="text-sm font-semibold">Manage Services</div>
                        </a>

                        <a href="<?php echo e(route('admin.categories.index')); ?>" class="bg-pink-600 text-white p-4 rounded-lg text-center hover:bg-pink-700 transition duration-300">
                            <div class="text-2xl mb-2">📂</div>
                            <div class="text-sm font-semibold">Categories</div>
                        </a>

                        <a href="<?php echo e(route('admin.newsletter-subscribers.index')); ?>" class="bg-teal-600 text-white p-4 rounded-lg text-center hover:bg-teal-700 transition duration-300">
                            <div class="text-2xl mb-2">📧</div>
                            <div class="text-sm font-semibold">Newsletter</div>
                        </a>

                        <a href="<?php echo e(route('admin.users.index')); ?>" class="bg-gray-600 text-white p-4 rounded-lg text-center hover:bg-gray-700 transition duration-300">
                            <div class="text-2xl mb-2">👥</div>
                            <div class="text-sm font-semibold">Users</div>
                        </a>
                    </div>
                </div>

                <!-- Settings Display -->
                <div class="mt-8">
                    <?php if (isset($component)) { $__componentOriginala6756f194d8427573a83d8956fe476c5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala6756f194d8427573a83d8956fe476c5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.settings-display','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('settings-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala6756f194d8427573a83d8956fe476c5)): ?>
<?php $attributes = $__attributesOriginala6756f194d8427573a83d8956fe476c5; ?>
<?php unset($__attributesOriginala6756f194d8427573a83d8956fe476c5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala6756f194d8427573a83d8956fe476c5)): ?>
<?php $component = $__componentOriginala6756f194d8427573a83d8956fe476c5; ?>
<?php unset($__componentOriginala6756f194d8427573a83d8956fe476c5); ?>
<?php endif; ?>
                </div>

                <!-- Admin Panel Status -->
                
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>