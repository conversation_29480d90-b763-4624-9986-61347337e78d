<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'color',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tag) {
            if (empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });

        static::updating(function ($tag) {
            if ($tag->isDirty('name') && empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get blog posts with this tag
     */
    public function blogPosts()
    {
        return $this->belongsToMany(BlogPost::class);
    }

    /**
     * Get published blog posts with this tag
     */
    public function publishedBlogPosts()
    {
        return $this->belongsToMany(BlogPost::class)->published();
    }

    /**
     * Get posts count for this tag
     */
    public function getPostsCountAttribute(): int
    {
        return $this->publishedBlogPosts()->count();
    }
}
