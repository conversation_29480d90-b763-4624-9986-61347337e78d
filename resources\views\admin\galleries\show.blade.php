@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gallery: {{ $gallery->title }}</h1>
        <div>
            <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Gallery
            </a>
            <a href="{{ route('admin.galleries.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Galleries
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Gallery Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Gallery Information</h6>
                </div>
                <div class="card-body">
                    @if($gallery->cover_image)
                        <div class="mb-4">
                            <img src="{{ $gallery->cover_image_url }}" alt="{{ $gallery->title }}" 
                                 class="img-fluid rounded" style="max-height: 400px; width: 100%; object-fit: cover;">
                        </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Title:</strong><br>
                            <span class="text-muted">{{ $gallery->title }}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Slug:</strong><br>
                            <span class="text-muted">{{ $gallery->slug }}</span>
                        </div>
                    </div>

                    @if($gallery->category)
                        <div class="mb-3">
                            <strong>Category:</strong><br>
                            <span class="badge badge-info">{{ $gallery->category }}</span>
                        </div>
                    @endif

                    @if($gallery->description)
                        <div class="mb-3">
                            <strong>Description:</strong>
                            <p class="text-muted mt-2">{{ $gallery->description }}</p>
                        </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Total Images:</strong><br>
                            <span class="text-muted">{{ $gallery->images->count() }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Featured Images:</strong><br>
                            <span class="text-muted">{{ $gallery->images->where('is_featured', true)->count() }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Sort Order:</strong><br>
                            <span class="text-muted">{{ $gallery->sort_order }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Images -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Gallery Images ({{ $gallery->images->count() }})</h6>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleView()">
                            <i class="fas fa-th" id="view-icon"></i> <span id="view-text">Grid View</span>
                        </button>
                        <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Add Images
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($gallery->images->count() > 0)
                        <!-- Grid View -->
                        <div id="grid-view" class="row">
                            @foreach($gallery->images as $image)
                                <div class="col-md-3 col-sm-4 col-6 mb-4">
                                    <div class="card h-100">
                                        <div class="position-relative">
                                            <img src="{{ $image->thumbnail_url }}" alt="{{ $image->title }}" 
                                                 class="card-img-top" style="height: 200px; object-fit: cover; cursor: pointer;"
                                                 onclick="openLightbox('{{ $image->image_url }}', '{{ $image->title }}', '{{ $image->caption }}')">
                                            
                                            <!-- Image badges -->
                                            <div class="position-absolute" style="top: 10px; right: 10px;">
                                                @if($image->is_featured)
                                                    <span class="badge badge-primary mb-1">Featured</span><br>
                                                @endif
                                                <span class="badge badge-secondary">{{ $image->sort_order }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="card-body p-2">
                                            @if($image->title)
                                                <h6 class="card-title text-truncate">{{ $image->title }}</h6>
                                            @endif
                                            @if($image->caption)
                                                <p class="card-text small text-muted">{{ Str::limit($image->caption, 50) }}</p>
                                            @endif
                                            <small class="text-muted">
                                                {{ $image->dimensions }} • {{ $image->file_size_human }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- List View -->
                        <div id="list-view" class="table-responsive" style="display: none;">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="80">Image</th>
                                        <th>Title</th>
                                        <th>Caption</th>
                                        <th>Dimensions</th>
                                        <th>Size</th>
                                        <th>Order</th>
                                        <th>Featured</th>
                                        <th width="100">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($gallery->images as $image)
                                        <tr>
                                            <td>
                                                <img src="{{ $image->thumbnail_url }}" alt="{{ $image->title }}" 
                                                     class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                                                     onclick="openLightbox('{{ $image->image_url }}', '{{ $image->title }}', '{{ $image->caption }}')">
                                            </td>
                                            <td>{{ $image->title ?: 'Untitled' }}</td>
                                            <td>{{ Str::limit($image->caption, 50) ?: '-' }}</td>
                                            <td>{{ $image->dimensions }}</td>
                                            <td>{{ $image->file_size_human }}</td>
                                            <td>{{ $image->sort_order }}</td>
                                            <td>
                                                @if($image->is_featured)
                                                    <span class="badge badge-primary">Featured</span>
                                                @else
                                                    <span class="badge badge-secondary">Regular</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="openLightbox('{{ $image->image_url }}', '{{ $image->title }}', '{{ $image->caption }}')" 
                                                            title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <a href="{{ route('admin.galleries.edit', $gallery) }}" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No images in this gallery</h5>
                            <p class="text-muted">Add some images to get started.</p>
                            <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Images
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Gallery Status -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Gallery Status</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Status:</strong>
                        @if($gallery->is_published)
                            <span class="badge badge-success ml-2">Published</span>
                        @else
                            <span class="badge badge-warning ml-2">Draft</span>
                        @endif
                    </div>

                    <div class="mb-3">
                        <strong>Featured:</strong>
                        @if($gallery->is_featured)
                            <span class="badge badge-primary ml-2">Featured</span>
                        @else
                            <span class="badge badge-secondary ml-2">Regular</span>
                        @endif
                    </div>

                    <div class="mb-3">
                        <strong>Sort Order:</strong>
                        <span class="text-muted ml-2">{{ $gallery->sort_order }}</span>
                    </div>

                    <div class="mb-3">
                        <strong>Slug:</strong>
                        <span class="text-muted ml-2">{{ $gallery->slug }}</span>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            @if($gallery->meta_title || $gallery->meta_description || $gallery->meta_keywords)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
                    </div>
                    <div class="card-body">
                        @if($gallery->meta_title)
                            <div class="mb-3">
                                <strong>Meta Title:</strong>
                                <p class="text-muted small">{{ $gallery->meta_title }}</p>
                            </div>
                        @endif

                        @if($gallery->meta_description)
                            <div class="mb-3">
                                <strong>Meta Description:</strong>
                                <p class="text-muted small">{{ $gallery->meta_description }}</p>
                            </div>
                        @endif

                        @if($gallery->meta_keywords)
                            <div class="mb-3">
                                <strong>Meta Keywords:</strong>
                                <p class="text-muted small">{{ $gallery->meta_keywords }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Gallery Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Total Images:</strong><br>
                        <span class="text-muted">{{ $gallery->images->count() }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>Featured Images:</strong><br>
                        <span class="text-muted">{{ $gallery->images->where('is_featured', true)->count() }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>Total File Size:</strong><br>
                        <span class="text-muted">{{ number_format($gallery->images->sum('file_size') / 1024 / 1024, 2) }} MB</span>
                    </div>
                </div>
            </div>

            <!-- Gallery Timestamps -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timestamps</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Created:</strong><br>
                        <small class="text-muted">{{ $gallery->created_at->format('M d, Y g:i A') }}</small>
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ $gallery->updated_at->format('M d, Y g:i A') }}</small>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Gallery
                        </a>
                        
                        @if($gallery->is_published)
                            <a href="{{ route('gallery.show', $gallery) }}" target="_blank" class="btn btn-info">
                                <i class="fas fa-eye"></i> View on Website
                            </a>
                        @endif

                        <form method="POST" action="{{ route('admin.galleries.destroy', $gallery) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this gallery and all its images? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash"></i> Delete Gallery
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lightbox Modal -->
<div class="modal fade" id="lightboxModal" tabindex="-1" role="dialog" aria-labelledby="lightboxModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="lightboxModalLabel">Image Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="lightboxImage" src="" alt="" class="img-fluid">
                <div id="lightboxCaption" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleView() {
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const viewIcon = document.getElementById('view-icon');
    const viewText = document.getElementById('view-text');
    
    if (gridView.style.display === 'none') {
        // Switch to grid view
        gridView.style.display = 'block';
        listView.style.display = 'none';
        viewIcon.className = 'fas fa-th';
        viewText.textContent = 'Grid View';
    } else {
        // Switch to list view
        gridView.style.display = 'none';
        listView.style.display = 'block';
        viewIcon.className = 'fas fa-list';
        viewText.textContent = 'List View';
    }
}

function openLightbox(imageUrl, title, caption) {
    const modal = document.getElementById('lightboxModal');
    const image = document.getElementById('lightboxImage');
    const captionDiv = document.getElementById('lightboxCaption');
    const modalTitle = document.getElementById('lightboxModalLabel');
    
    image.src = imageUrl;
    image.alt = title || 'Gallery Image';
    modalTitle.textContent = title || 'Gallery Image';
    
    if (caption) {
        captionDiv.innerHTML = `<p class="text-muted">${caption}</p>`;
        captionDiv.style.display = 'block';
    } else {
        captionDiv.style.display = 'none';
    }
    
    $('#lightboxModal').modal('show');
}
</script>
@endpush
@endsection
