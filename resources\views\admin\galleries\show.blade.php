@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Gallery: {{ $gallery->title }}</h1>
            <div class="flex gap-3">
                <a href="{{ route('admin.galleries.edit', $gallery) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    ✏️ Edit Gallery
                </a>
                <a href="{{ route('admin.galleries.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Back to Galleries
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <!-- Gallery Details -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Gallery Information</h3>
                    </div>
                    <div class="p-6">
                        @if($gallery->cover_image)
                            <div class="mb-6">
                                <img src="{{ $gallery->cover_image_url }}" alt="{{ $gallery->title }}"
                                     class="w-full rounded-lg object-cover" style="max-height: 400px;">
                            </div>
                        @endif

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div>
                                <h4 class="font-medium text-gray-900">Title:</h4>
                                <p class="text-gray-600">{{ $gallery->title }}</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Slug:</h4>
                                <p class="text-gray-600">{{ $gallery->slug }}</p>
                            </div>
                        </div>

                        @if($gallery->category)
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900">Category:</h4>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">{{ $gallery->category }}</span>
                            </div>
                        @endif

                        @if($gallery->description)
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900">Description:</h4>
                                <p class="text-gray-600 mt-2">{{ $gallery->description }}</p>
                            </div>
                        @endif

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <h4 class="font-medium text-gray-900">Total Images:</h4>
                                <p class="text-gray-600">{{ $gallery->images->count() }}</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Featured Images:</h4>
                                <p class="text-gray-600">{{ $gallery->images->where('is_featured', true)->count() }}</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Sort Order:</h4>
                                <p class="text-gray-600">{{ $gallery->sort_order }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gallery Images -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Gallery Images ({{ $gallery->images->count() }})</h3>
                        <div class="flex gap-2">
                            <button type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm" onclick="toggleView()">
                                <span id="view-text">📋 List View</span>
                            </button>
                            <a href="{{ route('admin.galleries.edit', $gallery) }}" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                ➕ Add Images
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        @if($gallery->images->count() > 0)
                            <!-- Grid View -->
                            <div id="grid-view" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                @foreach($gallery->images as $image)
                                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                                        <div class="relative">
                                            <img src="{{ $image->thumbnail_url }}" alt="{{ $image->title }}"
                                                 class="w-full h-48 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                                 onclick="openLightbox('{{ $image->image_url }}', '{{ $image->title }}', '{{ $image->caption }}')">

                                            <!-- Image badges -->
                                            <div class="absolute top-2 right-2 flex flex-col gap-1">
                                                @if($image->is_featured)
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Featured</span>
                                                @endif
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">{{ $image->sort_order }}</span>
                                            </div>
                                        </div>

                                        <div class="p-3">
                                            @if($image->title)
                                                <h4 class="font-medium text-gray-900 truncate">{{ $image->title }}</h4>
                                            @endif
                                            @if($image->caption)
                                                <p class="text-sm text-gray-600 mt-1">{{ Str::limit($image->caption, 50) }}</p>
                                            @endif
                                            <p class="text-xs text-gray-500 mt-2">
                                                {{ $image->dimensions }} • {{ $image->file_size_human }}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- List View -->
                            <div id="list-view" class="hidden overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Caption</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dimensions</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Featured</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($gallery->images as $image)
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <img src="{{ $image->thumbnail_url }}" alt="{{ $image->title }}"
                                                         class="w-16 h-16 object-cover rounded cursor-pointer"
                                                         onclick="openLightbox('{{ $image->image_url }}', '{{ $image->title }}', '{{ $image->caption }}')">
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $image->title ?: 'Untitled' }}</td>
                                                <td class="px-6 py-4 text-sm text-gray-600">{{ Str::limit($image->caption, 50) ?: '-' }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ $image->dimensions }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ $image->file_size_human }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ $image->sort_order }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    @if($image->is_featured)
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Featured</span>
                                                    @else
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Regular</span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div class="flex gap-2">
                                                        <button type="button" class="text-blue-600 hover:text-blue-900"
                                                                onclick="openLightbox('{{ $image->image_url }}', '{{ $image->title }}', '{{ $image->caption }}')"
                                                                title="View">
                                                            👁️
                                                        </button>
                                                        <a href="{{ route('admin.galleries.edit', $gallery) }}"
                                                           class="text-green-600 hover:text-green-900" title="Edit">
                                                            ✏️
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-12">
                                <div class="text-6xl mb-4">🖼️</div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No images in this gallery</h3>
                                <p class="text-gray-600 mb-4">Add some images to get started.</p>
                                <a href="{{ route('admin.galleries.edit', $gallery) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    ➕ Add Images
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div>
                <!-- Gallery Status -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Gallery Status</h3>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <h4 class="font-medium text-gray-900">Status:</h4>
                            @if($gallery->is_published)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Published</span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Draft</span>
                            @endif
                        </div>

                        <div class="mb-4">
                            <h4 class="font-medium text-gray-900">Featured:</h4>
                            @if($gallery->is_featured)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Featured</span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Regular</span>
                            @endif
                        </div>

                        <div class="mb-4">
                            <h4 class="font-medium text-gray-900">Sort Order:</h4>
                            <p class="text-gray-600">{{ $gallery->sort_order }}</p>
                        </div>

                        <div class="mb-4">
                            <h4 class="font-medium text-gray-900">Slug:</h4>
                            <p class="text-gray-600">{{ $gallery->slug }}</p>
                        </div>
                    </div>
                </div>

                <!-- SEO Information -->
                @if($gallery->meta_title || $gallery->meta_description || $gallery->meta_keywords)
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">SEO Information</h3>
                        </div>
                        <div class="p-6">
                            @if($gallery->meta_title)
                                <div class="mb-4">
                                    <h4 class="font-medium text-gray-900">Meta Title:</h4>
                                    <p class="text-gray-600 text-sm">{{ $gallery->meta_title }}</p>
                                </div>
                            @endif
                            @if($gallery->meta_description)
                                <div class="mb-4">
                                    <h4 class="font-medium text-gray-900">Meta Description:</h4>
                                    <p class="text-gray-600 text-sm">{{ $gallery->meta_description }}</p>
                                </div>
                            @endif
                            @if($gallery->meta_keywords)
                                <div class="mb-4">
                                    <h4 class="font-medium text-gray-900">Meta Keywords:</h4>
                                    <p class="text-gray-600 text-sm">{{ $gallery->meta_keywords }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Gallery Statistics -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
                    </div>
                    <div class="p-6">
                        <div class="mb-3">
                            <h4 class="font-medium text-gray-900">Total Images:</h4>
                            <p class="text-gray-600">{{ $gallery->images->count() }}</p>
                        </div>
                        <div class="mb-3">
                            <h4 class="font-medium text-gray-900">Featured Images:</h4>
                            <p class="text-gray-600">{{ $gallery->images->where('is_featured', true)->count() }}</p>
                        </div>
                        <div class="mb-3">
                            <h4 class="font-medium text-gray-900">Total File Size:</h4>
                            <p class="text-gray-600">{{ number_format($gallery->images->sum('file_size') / 1024 / 1024, 2) }} MB</p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <a href="{{ route('admin.galleries.edit', $gallery) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                                ✏️ Edit Gallery
                            </a>

                            @if($gallery->is_published)
                                <a href="{{ route('gallery.show', $gallery) }}" target="_blank" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block">
                                    👁️ View on Website
                                </a>
                            @endif

                            <form method="POST" action="{{ route('admin.galleries.destroy', $gallery) }}"
                                  onsubmit="return confirm('Are you sure you want to delete this gallery and all its images? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    🗑️ Delete Gallery
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lightbox Modal -->
<div id="lightboxModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 id="lightboxModalLabel" class="text-lg font-bold text-gray-900">Image Preview</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeLightbox()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="text-center">
            <img id="lightboxImage" src="" alt="" class="max-w-full h-auto rounded">
            <div id="lightboxCaption" class="mt-3"></div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleView() {
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const viewText = document.getElementById('view-text');

    if (gridView.classList.contains('hidden')) {
        // Switch to grid view
        gridView.classList.remove('hidden');
        listView.classList.add('hidden');
        viewText.textContent = '📋 List View';
    } else {
        // Switch to list view
        gridView.classList.add('hidden');
        listView.classList.remove('hidden');
        viewText.textContent = '🔲 Grid View';
    }
}

function openLightbox(imageUrl, title, caption) {
    const modal = document.getElementById('lightboxModal');
    const image = document.getElementById('lightboxImage');
    const captionDiv = document.getElementById('lightboxCaption');
    const modalTitle = document.getElementById('lightboxModalLabel');

    image.src = imageUrl;
    image.alt = title || 'Gallery Image';
    modalTitle.textContent = title || 'Gallery Image';

    if (caption) {
        captionDiv.innerHTML = `<p class="text-gray-600">${caption}</p>`;
        captionDiv.classList.remove('hidden');
    } else {
        captionDiv.classList.add('hidden');
    }

    modal.classList.remove('hidden');
}

function closeLightbox() {
    document.getElementById('lightboxModal').classList.add('hidden');
}
</script>
@endpush
@endsection
