@extends('layouts.public')

@section('content')
<div class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Blog</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Stay updated with the latest insights, trends, and expert advice from our team of business consultants.
            </p>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <form method="GET" action="{{ route('blog.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-center md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="Search articles..." class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                
                <!-- Category Filter -->
                <div>
                    <select name="category" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}" {{ request('category') === $category->slug ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <!-- Tag Filter -->
                <div>
                    <select name="tag" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="">All Tags</option>
                        @foreach($tags as $tag)
                            <option value="{{ $tag->slug }}" {{ request('tag') === $tag->slug ? 'selected' : '' }}>
                                {{ $tag->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-green-700 transition duration-300">
                    Filter
                </button>
                
                @if(request()->hasAny(['search', 'category', 'tag']))
                    <a href="{{ route('blog.index') }}" class="bg-gray-500 text-white px-6 py-2 rounded-md font-semibold hover:bg-gray-600 transition duration-300">
                        Clear
                    </a>
                @endif
            </form>
        </div>

        <!-- Blog Posts Grid -->
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @foreach($posts as $post)
                <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    @if($post->featured_image)
                        <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}" class="w-full h-48 object-cover">
                    @endif
                    
                    <div class="p-6">
                        <!-- Post Meta -->
                        <div class="flex items-center text-sm text-gray-500 mb-3">
                            <span class="bg-{{ $post->category->color }} text-white px-2 py-1 rounded text-xs mr-3">
                                {{ $post->category->name }}
                            </span>
                            <span>{{ $post->formatted_published_date }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ $post->reading_time_text }}</span>
                            @if($post->is_featured)
                                <span class="ml-2 bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded">Featured</span>
                            @endif
                        </div>
                        
                        <!-- Post Title -->
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">
                            <a href="{{ route('blog.show', $post) }}" class="hover:text-green-600">
                                {{ $post->title }}
                            </a>
                        </h3>
                        
                        <!-- Post Excerpt -->
                        <p class="text-gray-600 mb-4">{{ $post->excerpt }}</p>
                        
                        <!-- Tags -->
                        @if($post->tags->count() > 0)
                            <div class="flex flex-wrap gap-2 mb-4">
                                @foreach($post->tags as $tag)
                                    <a href="{{ route('blog.tag', $tag) }}" class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded hover:bg-gray-200">
                                        {{ $tag->name }}
                                    </a>
                                @endforeach
                            </div>
                        @endif
                        
                        <!-- Read More -->
                        <div class="flex items-center justify-between">
                            <a href="{{ route('blog.show', $post) }}" class="text-green-600 font-semibold hover:text-green-800">
                                Read More →
                            </a>
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                {{ $post->views_count }}
                            </div>
                        </div>
                    </div>
                </article>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="flex justify-center">
                    {{ $posts->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="text-gray-400 text-6xl mb-4">📝</div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-2">No articles found</h3>
                <p class="text-gray-600 mb-6">Try adjusting your search criteria or browse all articles.</p>
                <a href="{{ route('blog.index') }}" class="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition duration-300">
                    View All Articles
                </a>
            </div>
        @endif

        <!-- Newsletter Signup -->
        <div class="mt-16 bg-green-600 rounded-lg p-8 text-center text-white">
            <h2 class="text-3xl font-bold mb-4">Stay Updated</h2>
            <p class="text-xl mb-6 text-green-100">
                Subscribe to our newsletter to receive the latest articles and insights directly in your inbox.
            </p>
            @livewire('newsletter-signup', ['source' => 'blog'])
        </div>
    </div>
</div>
@endsection
