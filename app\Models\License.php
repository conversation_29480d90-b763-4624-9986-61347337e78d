<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class License extends Model
{
    use HasFactory;

    protected $fillable = [
        'license_key',
        'purchase_code',
        'domain',
        'ip_address',
        'status',
        'license_type',
        'activated_at',
        'expires_at',
        'last_verified_at',
        'verification_data',
        'grace_period_days',
        'is_grace_period_active',
        'grace_period_started_at',
        'notes',
    ];

    protected $casts = [
        'activated_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_verified_at' => 'datetime',
        'grace_period_started_at' => 'datetime',
        'verification_data' => 'array',
        'is_grace_period_active' => 'boolean',
        'grace_period_days' => 'integer',
    ];

    /**
     * Get the current license for this installation
     */
    public static function current(): ?self
    {
        return static::where('domain', request()->getHost())
            ->where('status', '!=', 'suspended')
            ->first();
    }

    /**
     * Check if the license is valid
     */
    public function isValid(): bool
    {
        if ($this->status === 'suspended') {
            return false;
        }

        if ($this->status === 'expired') {
            return $this->isInGracePeriod();
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return $this->isInGracePeriod();
        }

        return in_array($this->status, ['active', 'pending_verification']);
    }

    /**
     * Check if the license is in grace period
     */
    public function isInGracePeriod(): bool
    {
        if (!$this->is_grace_period_active || !$this->grace_period_started_at) {
            return false;
        }

        $gracePeriodEnd = $this->grace_period_started_at->addDays($this->grace_period_days);
        return now()->isBefore($gracePeriodEnd);
    }

    /**
     * Start grace period
     */
    public function startGracePeriod(): void
    {
        $this->update([
            'is_grace_period_active' => true,
            'grace_period_started_at' => now(),
        ]);
    }

    /**
     * Verify license with remote server
     */
    public function verify(): bool
    {
        try {
            $response = Http::timeout(30)->post(config('license.verification_url'), [
                'license_key' => $this->license_key,
                'purchase_code' => $this->purchase_code,
                'domain' => $this->domain,
                'ip' => request()->ip(),
                'current_status' => $this->status,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                $this->update([
                    'status' => $data['status'] ?? $this->status,
                    'expires_at' => isset($data['expires_at']) ? Carbon::parse($data['expires_at']) : $this->expires_at,
                    'last_verified_at' => now(),
                    'verification_data' => $data,
                    'license_type' => $data['license_type'] ?? $this->license_type,
                ]);

                return $data['valid'] ?? false;
            }

            return false;
        } catch (\Exception $e) {
            // If verification fails due to network issues, start grace period if not already active
            if (!$this->is_grace_period_active) {
                $this->startGracePeriod();
            }

            return $this->isInGracePeriod();
        }
    }

    /**
     * Get days remaining in license
     */
    public function getDaysRemaining(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }

    /**
     * Get grace period days remaining
     */
    public function getGracePeriodDaysRemaining(): int
    {
        if (!$this->is_grace_period_active || !$this->grace_period_started_at) {
            return 0;
        }

        $gracePeriodEnd = $this->grace_period_started_at->addDays($this->grace_period_days);
        return max(0, now()->diffInDays($gracePeriodEnd, false));
    }

    /**
     * Check if license needs verification
     */
    public function needsVerification(): bool
    {
        if (!$this->last_verified_at) {
            return true;
        }

        // Verify every 24 hours
        return $this->last_verified_at->addHours(24)->isPast();
    }
}
