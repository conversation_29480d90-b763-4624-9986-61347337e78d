# GreenWeb - Professional SaaS Platform

## Overview

GreenWeb is a comprehensive, multi-tenant SaaS platform built with Laravel 11, designed for businesses looking to create scalable web applications with advanced customization capabilities. Perfect for agencies, developers, and entrepreneurs who need a robust foundation for their SaaS products.

## 🚀 Key Features

### Core Platform
- **Multi-Tenant Architecture**: Complete tenant isolation with separate databases and storage
- **Advanced License System**: CodeCanyon-compatible licensing with automatic verification
- **Automated Installer**: Web-based installation wizard with system requirements checking
- **Theme & Plugin System**: Extensible architecture for unlimited customization
- **White-Label Ready**: Complete branding customization for reseller opportunities

### Content Management
- **Dynamic Page Builder**: Create and manage pages with SEO optimization
- **Blog System**: Full-featured blog with categories, tags, and comments
- **Event Management**: Comprehensive event planning and management tools
- **Gallery System**: Advanced image galleries with bulk upload capabilities
- **Menu Management**: Drag-and-drop menu builder with unlimited nesting

### Business Features
- **Team Management**: Organize team members with roles and departments
- **Project Portfolio**: Showcase projects with detailed case studies
- **Partner Management**: Manage business partnerships and client relationships
- **Testimonial System**: Collect and display customer testimonials
- **Contact Management**: Advanced contact form with enquiry tracking

### Technical Excellence
- **Modern Laravel 11**: Built on the latest Laravel framework
- **Responsive Design**: Mobile-first, fully responsive interface
- **SEO Optimized**: Built-in SEO tools and meta management
- **Performance Optimized**: Caching, optimization, and CDN ready
- **Security First**: Advanced security features and regular updates

## 📋 System Requirements

### Server Requirements
- **PHP**: 8.2 or higher
- **Database**: MySQL 8.0+ or MariaDB 10.3+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: 512MB RAM minimum (2GB recommended)
- **Storage**: 1GB free disk space minimum

### PHP Extensions
- BCMath PHP Extension
- Ctype PHP Extension
- cURL PHP Extension
- DOM PHP Extension
- Fileinfo PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PCRE PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
- ZIP PHP Extension
- GD PHP Extension

### Additional Requirements
- Composer 2.0+
- Node.js 18+ (for asset compilation)
- SSL Certificate (recommended for production)

## 🛠️ Installation

### Quick Installation

1. **Download & Extract**
   ```bash
   # Extract the downloaded files to your web directory
   unzip greenweb-v1.0.0.zip
   cd greenweb
   ```

2. **Set Permissions**
   ```bash
   chmod -R 755 storage bootstrap/cache
   chown -R www-data:www-data storage bootstrap/cache
   ```

3. **Web-Based Installation**
   - Navigate to `https://yourdomain.com/installer`
   - Follow the step-by-step installation wizard
   - The installer will guide you through:
     - System requirements check
     - Database configuration
     - Environment setup
     - Admin account creation
     - License activation

### Manual Installation

1. **Install Dependencies**
   ```bash
   composer install --optimize-autoloader --no-dev
   npm install && npm run build
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Database Setup**
   ```bash
   php artisan migrate --seed
   ```

4. **Storage Link**
   ```bash
   php artisan storage:link
   ```

## 🔧 Configuration

### Environment Variables

```env
# Application
APP_NAME="GreenWeb"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=greenweb
DB_USERNAME=your_username
DB_PASSWORD=your_password

# License Configuration
LICENSE_VERIFICATION_URL=https://license.yourcompany.com/api/validate
LICENSE_KEY=your_license_key
CODECANYON_PURCHASE_CODE=your_purchase_code

# Multi-Tenant Configuration
TENANT_DATABASE_PREFIX=tenant_
MAIN_DOMAIN=yourdomain.com

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
```

### Multi-Tenant Setup

1. **Configure Main Domain**
   - Set your main domain in the environment file
   - Configure DNS to point subdomains to your server

2. **Create Tenants**
   - Access admin panel at `/admin/tenants`
   - Create new tenants with custom domains or subdomains
   - Each tenant gets isolated database and storage

3. **Tenant Access**
   - Tenants can be accessed via:
     - Custom domains: `https://client-domain.com`
     - Subdomains: `https://tenant.yourdomain.com`

## 🎨 Customization

### Theme Development

1. **Create Theme Directory**
   ```
   resources/themes/your-theme/
   ├── theme.json
   ├── views/
   ├── assets/
   └── functions.php
   ```

2. **Theme Manifest (theme.json)**
   ```json
   {
     "name": "Your Theme",
     "slug": "your-theme",
     "version": "1.0.0",
     "description": "Custom theme description",
     "author": "Your Name",
     "supported_features": [
       "customizer",
       "menus",
       "widgets"
     ]
   }
   ```

### Plugin Development

1. **Create Plugin Directory**
   ```
   plugins/your-plugin/
   ├── plugin.json
   ├── src/
   ├── views/
   └── assets/
   ```

2. **Plugin Manifest (plugin.json)**
   ```json
   {
     "name": "Your Plugin",
     "slug": "your-plugin",
     "version": "1.0.0",
     "description": "Plugin description",
     "main_file": "src/Plugin.php"
   }
   ```

## 📚 API Documentation

### Authentication
All API endpoints require authentication via Laravel Sanctum tokens.

### Tenant API
```php
// Get current tenant info
GET /api/tenant

// Update tenant settings
PUT /api/tenant/settings

// Get tenant statistics
GET /api/tenant/stats
```

### Content API
```php
// Pages
GET /api/pages
POST /api/pages
PUT /api/pages/{id}
DELETE /api/pages/{id}

// Events
GET /api/events
POST /api/events
PUT /api/events/{id}
DELETE /api/events/{id}
```

## 🔒 Security Features

- **License Verification**: Automatic license validation with grace periods
- **Multi-Factor Authentication**: Optional 2FA for admin accounts
- **Rate Limiting**: API and form submission rate limiting
- **CSRF Protection**: Cross-site request forgery protection
- **XSS Prevention**: Input sanitization and output escaping
- **SQL Injection Protection**: Eloquent ORM with prepared statements

## 🚀 Performance Optimization

- **Database Optimization**: Indexed queries and optimized relationships
- **Caching**: Redis/Memcached support with intelligent cache invalidation
- **Asset Optimization**: Minified CSS/JS with version control
- **Image Optimization**: Automatic image compression and WebP support
- **CDN Ready**: Easy integration with popular CDN services

## 📞 Support & Updates

### Getting Support
- **Documentation**: Comprehensive online documentation
- **Video Tutorials**: Step-by-step video guides
- **Community Forum**: Active community support
- **Priority Support**: Available for extended license holders

### Updates
- **Automatic Updates**: Built-in update system with one-click updates
- **Version Control**: Semantic versioning with detailed changelogs
- **Backward Compatibility**: Careful attention to backward compatibility
- **Migration Tools**: Automated migration tools for major updates

## 📄 License

This software is licensed under the CodeCanyon Standard License. Please read the license terms carefully before use.

### Standard License Includes:
- Use for single end product
- End product can be distributed for free or sold
- End product cannot be redistributed as-is

### Extended License Includes:
- Everything in Standard License
- Use for multiple end products
- End product can be sold to multiple clients
- White-label rights included

## 🤝 Credits

- **Framework**: Laravel 11
- **UI Framework**: Tailwind CSS
- **Icons**: Heroicons & Font Awesome
- **Charts**: Chart.js
- **Editor**: TinyMCE
- **File Manager**: Laravel File Manager

## 📋 Changelog

### Version 1.0.0 (Initial Release)
- Complete multi-tenant SaaS platform
- Advanced license management system
- Theme and plugin architecture
- Comprehensive admin panel
- Full documentation and support

---

**Thank you for choosing GreenWeb!** 

For the latest updates and documentation, visit our website or check the included documentation files.
