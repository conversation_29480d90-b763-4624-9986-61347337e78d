<?php $__env->startSection('content'); ?>
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">General Settings</h1>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('admin.settings.export')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Export Settings
                </a>
                <a href="<?php echo e(route('admin.settings.clear-cache')); ?>" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Clear Cache
                </a>
            </div>
        </div>

        <!-- Settings Tabs -->
        <div x-data="{ activeTab: 'general' }" class="space-y-6">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button @click="activeTab = 'general'" :class="{ 'border-green-500 text-green-600': activeTab === 'general', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'general' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        General
                    </button>
                    <button @click="activeTab = 'branding'" :class="{ 'border-green-500 text-green-600': activeTab === 'branding', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'branding' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Branding
                    </button>
                    <button @click="activeTab = 'contact'" :class="{ 'border-green-500 text-green-600': activeTab === 'contact', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'contact' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Contact
                    </button>
                    <button @click="activeTab = 'social'" :class="{ 'border-green-500 text-green-600': activeTab === 'social', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'social' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Social Media
                    </button>
                    <button @click="activeTab = 'email'" :class="{ 'border-green-500 text-green-600': activeTab === 'email', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'email' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Email
                    </button>
                    <button @click="activeTab = 'integrations'" :class="{ 'border-green-500 text-green-600': activeTab === 'integrations', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'integrations' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Integrations
                    </button>
                    <button @click="activeTab = 'seo'" :class="{ 'border-green-500 text-green-600': activeTab === 'seo', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'seo' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        SEO
                    </button>
                    <button @click="activeTab = 'popup'" :class="{ 'border-green-500 text-green-600': activeTab === 'popup', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'popup' }" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Newsletter Popup
                    </button>
                </nav>
            </div>

            <form action="<?php echo e(route('admin.settings.update')); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <!-- General Tab -->
                <div x-show="activeTab === 'general'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">General Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Application Name -->
                        <div>
                            <label for="application_name" class="block text-sm font-medium text-gray-700 mb-2">Application Name</label>
                            <input type="text" name="application_name" id="application_name" value="<?php echo e(old('application_name', $settings->application_name)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['application_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Tagline -->
                        <div>
                            <label for="tagline" class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                            <input type="text" name="tagline" id="tagline" value="<?php echo e(old('tagline', $settings->tagline)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['tagline'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Timezone -->
                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                            <select name="timezone" id="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php $__currentLoopData = timezone_identifiers_list(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $timezone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($timezone); ?>" <?php echo e(old('timezone', $settings->timezone) == $timezone ? 'selected' : ''); ?>>
                                        <?php echo e($timezone); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Currency -->
                        <div>
                            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                            <select name="currency" id="currency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php
                                    $currencies = [
                                        'USD' => 'US Dollar ($)',
                                        'EUR' => 'Euro (€)',
                                        'GBP' => 'British Pound (£)',
                                        'JPY' => 'Japanese Yen (¥)',
                                        'CAD' => 'Canadian Dollar (C$)',
                                        'AUD' => 'Australian Dollar (A$)',
                                        'KES' => 'Kenyan Shilling (KSh)',
                                        'NGN' => 'Nigerian Naira (₦)',
                                        'ZAR' => 'South African Rand (R)',
                                    ];
                                ?>
                                <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($code); ?>" <?php echo e(old('currency', $settings->currency) == $code ? 'selected' : ''); ?>>
                                        <?php echo e($name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Catchphrase -->
                    <div>
                        <label for="catchphrase" class="block text-sm font-medium text-gray-700 mb-2">Catchphrase</label>
                        <textarea name="catchphrase" id="catchphrase" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"><?php echo e(old('catchphrase', $settings->catchphrase)); ?></textarea>
                        <?php $__errorArgs = ['catchphrase'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Vision -->
                    <div>
                        <label for="vision" class="block text-sm font-medium text-gray-700 mb-2">Vision</label>
                        <textarea name="vision" id="vision" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"><?php echo e(old('vision', $settings->vision)); ?></textarea>
                        <?php $__errorArgs = ['vision'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Mission -->
                    <div>
                        <label for="mission" class="block text-sm font-medium text-gray-700 mb-2">Mission</label>
                        <textarea name="mission" id="mission" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"><?php echo e(old('mission', $settings->mission)); ?></textarea>
                        <?php $__errorArgs = ['mission'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- About Us -->
                    <div>
                        <label for="about_us" class="block text-sm font-medium text-gray-700 mb-2">About Us</label>
                        <textarea name="about_us" id="about_us" rows="6"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="Detailed information about your company, history, values, and what makes you unique..."><?php echo e(old('about_us', $settings->about_us)); ?></textarea>
                        <?php $__errorArgs = ['about_us'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500">This content will be used on both the homepage (summary) and the About page (full content).</p>
                    </div>

                    <!-- Copyright -->
                    <div>
                        <label for="copyright" class="block text-sm font-medium text-gray-700 mb-2">Copyright Text</label>
                        <input type="text" name="copyright" id="copyright" value="<?php echo e(old('copyright', $settings->copyright)); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <?php $__errorArgs = ['copyright'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Branding Tab -->
                <div x-show="activeTab === 'branding'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Branding & Images</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Logo -->
                        <div>
                            <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">Logo</label>
                            <?php if($settings->logo): ?>
                                <div class="mb-4">
                                    <img src="<?php echo e(asset('storage/' . $settings->logo)); ?>" alt="Current logo" class="h-16 object-contain border border-gray-200 rounded p-2">
                                    <p class="text-xs text-gray-500 mt-1">Current logo</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" name="logo" id="logo" accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-xs text-gray-500">Recommended: PNG, SVG, or JPG. Max size: 2MB</p>
                        </div>

                        <!-- Favicon -->
                        <div>
                            <label for="favicon" class="block text-sm font-medium text-gray-700 mb-2">Favicon</label>
                            <?php if($settings->favicon): ?>
                                <div class="mb-4">
                                    <img src="<?php echo e(asset('storage/' . $settings->favicon)); ?>" alt="Current favicon" class="h-8 w-8 object-contain border border-gray-200 rounded p-1">
                                    <p class="text-xs text-gray-500 mt-1">Current favicon</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" name="favicon" id="favicon" accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['favicon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-xs text-gray-500">Recommended: ICO or PNG. Size: 16x16 or 32x32 pixels</p>
                        </div>

                        <!-- Header Image -->
                        <div>
                            <label for="header_image" class="block text-sm font-medium text-gray-700 mb-2">Header Image</label>
                            <?php if($settings->header_image): ?>
                                <div class="mb-4">
                                    <img src="<?php echo e(asset('storage/' . $settings->header_image)); ?>" alt="Current header image" class="h-24 w-full object-cover rounded border border-gray-200">
                                    <p class="text-xs text-gray-500 mt-1">Current header image</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" name="header_image" id="header_image" accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['header_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-xs text-gray-500">Recommended: 1920x400 pixels. Max size: 5MB</p>
                        </div>

                        <!-- Banner Image -->
                        <div>
                            <label for="banner_image" class="block text-sm font-medium text-gray-700 mb-2">Banner Image</label>
                            <?php if($settings->banner_image): ?>
                                <div class="mb-4">
                                    <img src="<?php echo e(asset('storage/' . $settings->banner_image)); ?>" alt="Current banner image" class="h-24 w-full object-cover rounded border border-gray-200">
                                    <p class="text-xs text-gray-500 mt-1">Current banner image</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" name="banner_image" id="banner_image" accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['banner_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-xs text-gray-500">Recommended: 1920x600 pixels. Max size: 5MB</p>
                        </div>
                    </div>
                </div>

                <!-- Contact Tab -->
                <div x-show="activeTab === 'contact'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Phone Numbers -->
                        <div>
                            <label for="phone1" class="block text-sm font-medium text-gray-700 mb-2">Primary Phone</label>
                            <input type="text" name="phone1" id="phone1" value="<?php echo e(old('phone1', $settings->phone1)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['phone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="phone2" class="block text-sm font-medium text-gray-700 mb-2">Secondary Phone</label>
                            <input type="text" name="phone2" id="phone2" value="<?php echo e(old('phone2', $settings->phone2)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['phone2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Email Addresses -->
                        <div>
                            <label for="email1" class="block text-sm font-medium text-gray-700 mb-2">Primary Email</label>
                            <input type="email" name="email1" id="email1" value="<?php echo e(old('email1', $settings->email1)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['email1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="email2" class="block text-sm font-medium text-gray-700 mb-2">Secondary Email</label>
                            <input type="email" name="email2" id="email2" value="<?php echo e(old('email2', $settings->email2)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['email2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Address -->
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <textarea name="address" id="address" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"><?php echo e(old('address', $settings->address)); ?></textarea>
                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Map Embed -->
                    <div>
                        <label for="map" class="block text-sm font-medium text-gray-700 mb-2">Map Embed Code</label>
                        <textarea name="map" id="map" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="Paste Google Maps embed code here..."><?php echo e(old('map', $settings->map)); ?></textarea>
                        <?php $__errorArgs = ['map'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500">Get embed code from Google Maps → Share → Embed a map</p>
                    </div>
                </div>

                <!-- Social Media Tab -->
                <div x-show="activeTab === 'social'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Social Media Links</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Facebook -->
                        <div>
                            <label for="facebook_link" class="block text-sm font-medium text-gray-700 mb-2">Facebook</label>
                            <input type="url" name="facebook_link" id="facebook_link" value="<?php echo e(old('facebook_link', $settings->facebook_link)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="https://facebook.com/yourpage">
                            <?php $__errorArgs = ['facebook_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Twitter -->
                        <div>
                            <label for="twitter_link" class="block text-sm font-medium text-gray-700 mb-2">Twitter</label>
                            <input type="url" name="twitter_link" id="twitter_link" value="<?php echo e(old('twitter_link', $settings->twitter_link)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="https://twitter.com/youraccount">
                            <?php $__errorArgs = ['twitter_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- LinkedIn -->
                        <div>
                            <label for="linkedin_link" class="block text-sm font-medium text-gray-700 mb-2">LinkedIn</label>
                            <input type="url" name="linkedin_link" id="linkedin_link" value="<?php echo e(old('linkedin_link', $settings->linkedin_link)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="https://linkedin.com/company/yourcompany">
                            <?php $__errorArgs = ['linkedin_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Instagram -->
                        <div>
                            <label for="instagram_link" class="block text-sm font-medium text-gray-700 mb-2">Instagram</label>
                            <input type="url" name="instagram_link" id="instagram_link" value="<?php echo e(old('instagram_link', $settings->instagram_link)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="https://instagram.com/youraccount">
                            <?php $__errorArgs = ['instagram_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- YouTube -->
                        <div>
                            <label for="youtube_link" class="block text-sm font-medium text-gray-700 mb-2">YouTube</label>
                            <input type="url" name="youtube_link" id="youtube_link" value="<?php echo e(old('youtube_link', $settings->youtube_link)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="https://youtube.com/channel/yourchannel">
                            <?php $__errorArgs = ['youtube_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- WhatsApp -->
                        <div>
                            <label for="whatsapp_link" class="block text-sm font-medium text-gray-700 mb-2">WhatsApp</label>
                            <input type="text" name="whatsapp_link" id="whatsapp_link" value="<?php echo e(old('whatsapp_link', $settings->whatsapp_link)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="https://wa.me/1234567890">
                            <?php $__errorArgs = ['whatsapp_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-xs text-gray-500">Format: https://wa.me/phonenumber</p>
                        </div>
                    </div>
                </div>

                <!-- Email Tab -->
                <div x-show="activeTab === 'email'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Email Configuration</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Email From -->
                        <div>
                            <label for="email_from" class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                            <input type="email" name="email_from" id="email_from" value="<?php echo e(old('email_from', $settings->email_from)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['email_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- SMTP Host -->
                        <div>
                            <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                            <input type="text" name="smtp_host" id="smtp_host" value="<?php echo e(old('smtp_host', $settings->smtp_host)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="smtp.gmail.com">
                            <?php $__errorArgs = ['smtp_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- SMTP Port -->
                        <div>
                            <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                            <input type="number" name="smtp_port" id="smtp_port" value="<?php echo e(old('smtp_port', $settings->smtp_port)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                   placeholder="587">
                            <?php $__errorArgs = ['smtp_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- SMTP User -->
                        <div>
                            <label for="smtp_user" class="block text-sm font-medium text-gray-700 mb-2">SMTP Username</label>
                            <input type="text" name="smtp_user" id="smtp_user" value="<?php echo e(old('smtp_user', $settings->smtp_user)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['smtp_user'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- SMTP Password -->
                        <div>
                            <label for="smtp_pass" class="block text-sm font-medium text-gray-700 mb-2">SMTP Password</label>
                            <input type="password" name="smtp_pass" id="smtp_pass" value="<?php echo e(old('smtp_pass', $settings->smtp_pass)); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['smtp_pass'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Test Email -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Test Email Configuration</h4>
                        <div class="flex space-x-3">
                            <input type="email" id="test_email" placeholder="Enter test email address"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <button type="button" onclick="testEmail()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Send Test
                            </button>
                        </div>
                        <div id="test-email-result" class="mt-2"></div>
                    </div>
                </div>

                <!-- Integrations Tab -->
                <div x-show="activeTab === 'integrations'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Integrations & Security</h3>

                    <!-- reCAPTCHA -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Google reCAPTCHA</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="recaptcha_site_key" class="block text-sm font-medium text-gray-700 mb-2">Site Key</label>
                                <input type="text" name="recaptcha_site_key" id="recaptcha_site_key" value="<?php echo e(old('recaptcha_site_key', $settings->recaptcha_site_key)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php $__errorArgs = ['recaptcha_site_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label for="recaptcha_secret_key" class="block text-sm font-medium text-gray-700 mb-2">Secret Key</label>
                                <input type="password" name="recaptcha_secret_key" id="recaptcha_secret_key" value="<?php echo e(old('recaptcha_secret_key', $settings->recaptcha_secret_key)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php $__errorArgs = ['recaptcha_secret_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- SMS Configuration -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">SMS Configuration</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="sms_senderid" class="block text-sm font-medium text-gray-700 mb-2">Sender ID</label>
                                <input type="text" name="sms_senderid" id="sms_senderid" value="<?php echo e(old('sms_senderid', $settings->sms_senderid)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php $__errorArgs = ['sms_senderid'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label for="sms_username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                                <input type="text" name="sms_username" id="sms_username" value="<?php echo e(old('sms_username', $settings->sms_username)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php $__errorArgs = ['sms_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label for="sms_password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                <input type="password" name="sms_password" id="sms_password" value="<?php echo e(old('sms_password', $settings->sms_password)); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <?php $__errorArgs = ['sms_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Push Notifications -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Push Notifications</h4>
                        <div class="space-y-4">
                            <div>
                                <label for="push_public_key" class="block text-sm font-medium text-gray-700 mb-2">Public Key</label>
                                <textarea name="push_public_key" id="push_public_key" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"><?php echo e(old('push_public_key', $settings->push_public_key)); ?></textarea>
                                <?php $__errorArgs = ['push_public_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label for="push_private_key" class="block text-sm font-medium text-gray-700 mb-2">Private Key</label>
                                <textarea name="push_private_key" id="push_private_key" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"><?php echo e(old('push_private_key', $settings->push_private_key)); ?></textarea>
                                <?php $__errorArgs = ['push_private_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Tab -->
                <div x-show="activeTab === 'seo'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">SEO Settings</h3>

                    <!-- SEO Keywords -->
                    <div>
                        <label for="seo_keywords" class="block text-sm font-medium text-gray-700 mb-2">SEO Keywords</label>
                        <textarea name="seo_keywords" id="seo_keywords" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="keyword1, keyword2, keyword3, ..."><?php echo e(old('seo_keywords', $settings->seo_keywords)); ?></textarea>
                        <?php $__errorArgs = ['seo_keywords'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500">Separate keywords with commas. These will be used as default meta keywords for pages without specific keywords.</p>
                    </div>

                    <!-- SEO Preview -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">SEO Preview</h4>
                        <div class="border border-gray-200 rounded p-3 bg-white">
                            <div class="text-blue-600 text-lg font-medium"><?php echo e($settings->application_name ?: 'Your Site Name'); ?></div>
                            <div class="text-green-600 text-sm"><?php echo e(url('/')); ?></div>
                            <div class="text-gray-600 text-sm mt-1"><?php echo e($settings->tagline ?: 'Your site tagline will appear here'); ?></div>
                        </div>
                    </div>
                </div>

                <!-- Newsletter Popup Tab -->
                <div x-show="activeTab === 'popup'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Newsletter Popup Settings</h3>

                    <!-- Enable/Disable Popup -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" name="newsletter_popup_enabled" id="newsletter_popup_enabled" value="1"
                                   <?php echo e(old('newsletter_popup_enabled', $settings->newsletter_popup_enabled) ? 'checked' : ''); ?>

                                   class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                            <label for="newsletter_popup_enabled" class="ml-2 block text-sm font-medium text-gray-700">
                                Enable Newsletter Popup
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Show newsletter signup popup to website visitors</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Popup Delay -->
                        <div>
                            <label for="newsletter_popup_delay" class="block text-sm font-medium text-gray-700 mb-2">Popup Delay (seconds)</label>
                            <input type="number" name="newsletter_popup_delay" id="newsletter_popup_delay"
                                   value="<?php echo e(old('newsletter_popup_delay', $settings->newsletter_popup_delay ?? 5)); ?>"
                                   min="1" max="60"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['newsletter_popup_delay'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-xs text-gray-500">How many seconds to wait before showing the popup</p>
                        </div>

                        <!-- Popup Title -->
                        <div>
                            <label for="newsletter_popup_title" class="block text-sm font-medium text-gray-700 mb-2">Popup Title</label>
                            <input type="text" name="newsletter_popup_title" id="newsletter_popup_title"
                                   value="<?php echo e(old('newsletter_popup_title', $settings->newsletter_popup_title ?? 'Stay Updated!')); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <?php $__errorArgs = ['newsletter_popup_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Popup Message -->
                    <div>
                        <label for="newsletter_popup_message" class="block text-sm font-medium text-gray-700 mb-2">Popup Message</label>
                        <textarea name="newsletter_popup_message" id="newsletter_popup_message" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="Subscribe to our newsletter to get the latest updates and exclusive offers!"><?php echo e(old('newsletter_popup_message', $settings->newsletter_popup_message ?? 'Subscribe to our newsletter to get the latest updates and exclusive offers!')); ?></textarea>
                        <?php $__errorArgs = ['newsletter_popup_message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Popup Preview -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Popup Preview</h4>
                        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 border">
                            <div class="text-center">
                                <h3 class="text-xl font-bold text-gray-900 mb-2" id="preview-title">
                                    <?php echo e($settings->newsletter_popup_title ?? 'Stay Updated!'); ?>

                                </h3>
                                <p class="text-gray-600 mb-4" id="preview-message">
                                    <?php echo e($settings->newsletter_popup_message ?? 'Subscribe to our newsletter to get the latest updates and exclusive offers!'); ?>

                                </p>
                                <div class="space-y-3">
                                    <input type="email" placeholder="Enter your email" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" disabled>
                                    <button class="w-full bg-green-600 text-white py-2 px-4 rounded-md text-sm font-medium" disabled>Subscribe</button>
                                </div>
                                <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" disabled>×</button>
                            </div>
                        </div>
                        <p class="mt-2 text-xs text-gray-500 text-center">This is how the popup will appear to visitors</p>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4 pt-6 border-t">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function testEmail() {
    const testEmail = document.getElementById('test_email').value;
    const resultDiv = document.getElementById('test-email-result');

    if (!testEmail) {
        resultDiv.innerHTML = '<div class="text-red-600 text-sm">Please enter a test email address.</div>';
        return;
    }

    resultDiv.innerHTML = '<div class="text-blue-600 text-sm">Sending test email...</div>';

    fetch('<?php echo e(route("admin.settings.test-email")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify({
            test_email: testEmail
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="text-green-600 text-sm">' + data.message + '</div>';
        } else {
            resultDiv.innerHTML = '<div class="text-red-600 text-sm">' + data.message + '</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="text-red-600 text-sm">Error: ' + error.message + '</div>';
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>