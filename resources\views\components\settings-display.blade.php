<!-- Settings Display Component for Testing -->
<div class="bg-gray-50 p-4 rounded-lg">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Settings</h3>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
            <strong>Application Name:</strong>
            <span class="text-gray-700">{{ $globalSettings->application_name ?? 'Not set' }}</span>
        </div>

        <div>
            <strong>Tagline:</strong>
            <span class="text-gray-700">{{ $globalSettings->tagline ?? 'Not set' }}</span>
        </div>

        <div>
            <strong>Primary Email:</strong>
            <span class="text-gray-700">{{ $globalSettings->email1 ?? 'Not set' }}</span>
        </div>

        <div>
            <strong>Primary Phone:</strong>
            <span class="text-gray-700">{{ $globalSettings->phone1 ?? 'Not set' }}</span>
        </div>

        <div>
            <strong>Currency:</strong>
            <span class="text-gray-700">{{ $globalSettings->currency ?? 'Not set' }}</span>
        </div>

        <div>
            <strong>Timezone:</strong>
            <span class="text-gray-700">{{ $globalSettings->timezone ?? 'Not set' }}</span>
        </div>

        <div>
            <strong>Logo:</strong>
            @if($globalSettings->logo)
                <img src="{{ asset('storage/' . $globalSettings->logo) }}" alt="Logo" class="h-8 inline-block border border-gray-200 rounded">
            @else
                <span class="text-gray-500">Not uploaded</span>
            @endif
        </div>

        <div>
            <strong>Favicon:</strong>
            @if($globalSettings->favicon)
                <img src="{{ asset('storage/' . $globalSettings->favicon) }}" alt="Favicon" class="h-4 w-4 inline-block border border-gray-200 rounded">
            @else
                <span class="text-gray-500">Not uploaded</span>
            @endif
        </div>
    </div>

    <div class="mt-4 pt-4 border-t border-gray-200">
        <div class="text-xs text-gray-500">
            <strong>Last Updated:</strong> {{ $globalSettings->updated_at ? $globalSettings->updated_at->format('M d, Y g:i A') : 'Never' }}
        </div>
    </div>
</div>
