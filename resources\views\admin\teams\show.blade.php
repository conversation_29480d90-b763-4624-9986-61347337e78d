@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Team Member Details</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.teams.edit', $team) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Edit
            </a>
            <a href="{{ route('admin.teams.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Teams
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Profile Image -->
                <div class="lg:col-span-1">
                    @if($team->image_url)
                        <img src="{{ $team->image_url }}" alt="{{ $team->name }}" class="w-full max-w-sm mx-auto rounded-lg shadow-md">
                    @else
                        <div class="w-full max-w-sm mx-auto bg-gray-200 rounded-lg shadow-md flex items-center justify-center h-64">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-gray-400 rounded-full mx-auto mb-4 flex items-center justify-center">
                                    <span class="text-2xl font-bold text-white">{{ substr($team->name, 0, 1) }}</span>
                                </div>
                                <p class="text-gray-500">No image uploaded</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Team Member Info -->
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <!-- Basic Info -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $team->name }}</h2>
                            <p class="text-lg text-gray-600 mb-4">{{ $team->position }}</p>
                            
                            <div class="flex flex-wrap gap-4 mb-4">
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $team->department === 'executive' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800' }}">
                                    {{ $team->department_name }}
                                </span>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $team->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $team->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        @if($team->email || $team->phone)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Contact Information</h3>
                            <div class="space-y-2">
                                @if($team->email)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <a href="mailto:{{ $team->email }}" class="text-green-600 hover:text-green-800">{{ $team->email }}</a>
                                </div>
                                @endif
                                @if($team->phone)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <a href="tel:{{ $team->phone }}" class="text-green-600 hover:text-green-800">{{ $team->phone }}</a>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif

                        <!-- Social Links -->
                        @if($team->linkedin || $team->twitter)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Social Media</h3>
                            <div class="flex space-x-4">
                                @if($team->linkedin)
                                <a href="{{ $team->linkedin }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                @endif
                                @if($team->twitter)
                                <a href="{{ $team->twitter }}" target="_blank" class="text-blue-400 hover:text-blue-600">
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                                @endif
                            </div>
                        </div>
                        @endif

                        <!-- Bio -->
                        @if($team->bio)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Biography</h3>
                            <div class="prose max-w-none">
                                <p class="text-gray-700 leading-relaxed">{{ $team->bio }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Metadata -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                    <span class="font-medium">Sort Order:</span> {{ $team->sort_order }}
                                </div>
                                <div>
                                    <span class="font-medium">Status:</span> {{ $team->is_active ? 'Active' : 'Inactive' }}
                                </div>
                                <div>
                                    <span class="font-medium">Created:</span> {{ $team->created_at->format('M j, Y g:i A') }}
                                </div>
                                <div>
                                    <span class="font-medium">Updated:</span> {{ $team->updated_at->format('M j, Y g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-gray-50 px-6 py-4 border-t">
            <div class="flex justify-between items-center">
                <div class="flex space-x-2">
                    <form action="{{ route('admin.teams.toggle-status', $team) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                            {{ $team->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>
                </div>
                
                <form action="{{ route('admin.teams.destroy', $team) }}" method="POST" class="inline" 
                      onsubmit="return confirm('Are you sure you want to delete this team member? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Delete Team Member
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
