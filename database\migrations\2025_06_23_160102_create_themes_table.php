<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('themes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('version')->default('1.0.0');
            $table->text('description')->nullable();
            $table->string('author')->nullable();
            $table->string('author_url')->nullable();
            $table->string('preview_image')->nullable();
            $table->json('screenshots')->nullable();
            $table->json('config')->nullable(); // Theme configuration options
            $table->json('customizer_settings')->nullable(); // Customizer panel settings
            $table->boolean('is_active')->default(false);
            $table->boolean('is_default')->default(false);
            $table->string('parent_theme')->nullable(); // For child themes
            $table->json('supported_features')->nullable(); // Features this theme supports
            $table->string('min_version')->nullable(); // Minimum app version required
            $table->string('max_version')->nullable(); // Maximum app version supported
            $table->text('changelog')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('themes');
    }
};
