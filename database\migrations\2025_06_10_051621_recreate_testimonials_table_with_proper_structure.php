<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Backup existing data
        $testimonials = DB::table('testimonials')->get();

        // Drop and recreate the table
        Schema::dropIfExists('testimonials');

        Schema::create('testimonials', function (Blueprint $table) {
            $table->id();
            $table->string('client_name', 500);
            $table->string('client_position', 500)->nullable();
            $table->string('client_company', 500)->nullable();
            $table->text('testimonial');
            $table->string('client_image')->nullable();
            $table->integer('rating')->default(5);
            $table->string('project_title', 500)->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_approved')->default(false);
            $table->timestamps();
        });

        // Restore data
        foreach ($testimonials as $testimonial) {
            DB::table('testimonials')->insert([
                'id' => $testimonial->id,
                'client_name' => $testimonial->client_name,
                'client_position' => $testimonial->client_position ?: null,
                'client_company' => $testimonial->client_company ?: null,
                'testimonial' => $testimonial->testimonial,
                'client_image' => $testimonial->client_image ?: null,
                'rating' => $testimonial->rating,
                'project_title' => $testimonial->project_title ?: null,
                'sort_order' => $testimonial->sort_order,
                'is_featured' => $testimonial->is_featured,
                'is_active' => $testimonial->is_active,
                'is_approved' => $testimonial->is_approved,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
