<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['variant' => 'default', 'opacity' => '0.1']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['variant' => 'default', 'opacity' => '0.1']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$patterns = [
    'default' => [
        'viewBox' => '0 0 1200 800',
        'elements' => [
            // Geometric shapes and dots
            '<circle cx="100" cy="100" r="2" fill="currentColor" opacity="0.3"/>',
            '<circle cx="200" cy="150" r="1.5" fill="currentColor" opacity="0.4"/>',
            '<circle cx="300" cy="80" r="2.5" fill="currentColor" opacity="0.2"/>',
            '<circle cx="500" cy="200" r="1" fill="currentColor" opacity="0.5"/>',
            '<circle cx="700" cy="120" r="2" fill="currentColor" opacity="0.3"/>',
            '<circle cx="900" cy="180" r="1.5" fill="currentColor" opacity="0.4"/>',
            '<circle cx="1100" cy="90" r="2.5" fill="currentColor" opacity="0.2"/>',
            
            // Curved lines and paths
            '<path d="M0,400 Q300,350 600,400 T1200,400" stroke="currentColor" stroke-width="1" fill="none" opacity="0.15"/>',
            '<path d="M0,500 Q400,450 800,500 T1200,500" stroke="currentColor" stroke-width="0.5" fill="none" opacity="0.1"/>',
            
            // Abstract geometric shapes
            '<polygon points="150,300 170,280 190,300 170,320" fill="currentColor" opacity="0.2"/>',
            '<polygon points="450,350 480,330 510,350 480,370" fill="currentColor" opacity="0.15"/>',
            '<polygon points="750,250 780,230 810,250 780,270" fill="currentColor" opacity="0.25"/>',
            
            // Floating rectangles
            '<rect x="250" y="400" width="30" height="4" rx="2" fill="currentColor" opacity="0.2" transform="rotate(15 265 402)"/>',
            '<rect x="550" y="300" width="25" height="3" rx="1.5" fill="currentColor" opacity="0.3" transform="rotate(-20 562.5 301.5)"/>',
            '<rect x="850" y="450" width="35" height="5" rx="2.5" fill="currentColor" opacity="0.15" transform="rotate(30 867.5 452.5)"/>',
            
            // Grid dots pattern
            '<g opacity="0.1">',
                '<circle cx="400" cy="600" r="1" fill="currentColor"/>',
                '<circle cx="420" cy="600" r="1" fill="currentColor"/>',
                '<circle cx="440" cy="600" r="1" fill="currentColor"/>',
                '<circle cx="460" cy="600" r="1" fill="currentColor"/>',
                '<circle cx="400" cy="620" r="1" fill="currentColor"/>',
                '<circle cx="420" cy="620" r="1" fill="currentColor"/>',
                '<circle cx="440" cy="620" r="1" fill="currentColor"/>',
                '<circle cx="460" cy="620" r="1" fill="currentColor"/>',
            '</g>',
            
            // Larger accent elements
            '<circle cx="1000" cy="300" r="40" fill="none" stroke="currentColor" stroke-width="1" opacity="0.08"/>',
            '<circle cx="200" cy="600" r="60" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.06"/>',
        ]
    ],
    'hero' => [
        'viewBox' => '0 0 1400 900',
        'elements' => [
            // Large background circles
            '<circle cx="1200" cy="100" r="150" fill="currentColor" opacity="0.03"/>',
            '<circle cx="100" cy="700" r="200" fill="currentColor" opacity="0.02"/>',
            '<circle cx="700" cy="200" r="100" fill="currentColor" opacity="0.04"/>',
            
            // Flowing curves
            '<path d="M0,300 Q350,250 700,300 Q1050,350 1400,300" stroke="currentColor" stroke-width="2" fill="none" opacity="0.1"/>',
            '<path d="M0,600 Q350,550 700,600 Q1050,650 1400,600" stroke="currentColor" stroke-width="1" fill="none" opacity="0.08"/>',
            
            // Scattered elements
            '<circle cx="300" cy="400" r="3" fill="currentColor" opacity="0.4"/>',
            '<circle cx="800" cy="500" r="2" fill="currentColor" opacity="0.5"/>',
            '<circle cx="1100" cy="300" r="4" fill="currentColor" opacity="0.3"/>',
            '<circle cx="500" cy="700" r="2.5" fill="currentColor" opacity="0.4"/>',
            
            // Abstract shapes
            '<polygon points="600,100 620,80 640,100 620,120" fill="currentColor" opacity="0.2"/>',
            '<polygon points="200,500 230,480 260,500 230,520" fill="currentColor" opacity="0.15"/>',
            
            // Floating lines
            '<line x1="400" y1="150" x2="450" y2="180" stroke="currentColor" stroke-width="1" opacity="0.2"/>',
            '<line x1="900" y1="400" x2="950" y2="430" stroke="currentColor" stroke-width="1" opacity="0.15"/>',
            '<line x1="150" y1="300" x2="200" y2="330" stroke="currentColor" stroke-width="1" opacity="0.25"/>',
        ]
    ],
    'subtle' => [
        'viewBox' => '0 0 800 600',
        'elements' => [
            // Minimal dots
            '<circle cx="100" cy="100" r="1" fill="currentColor" opacity="0.2"/>',
            '<circle cx="300" cy="200" r="1" fill="currentColor" opacity="0.15"/>',
            '<circle cx="500" cy="150" r="1" fill="currentColor" opacity="0.25"/>',
            '<circle cx="700" cy="300" r="1" fill="currentColor" opacity="0.2"/>',
            '<circle cx="200" cy="400" r="1" fill="currentColor" opacity="0.18"/>',
            '<circle cx="600" cy="450" r="1" fill="currentColor" opacity="0.22"/>',
            
            // Subtle lines
            '<path d="M0,200 Q200,180 400,200 Q600,220 800,200" stroke="currentColor" stroke-width="0.5" fill="none" opacity="0.1"/>',
            '<path d="M0,400 Q200,380 400,400 Q600,420 800,400" stroke="currentColor" stroke-width="0.5" fill="none" opacity="0.08"/>',
            
            // Small geometric elements
            '<rect x="150" y="250" width="2" height="20" fill="currentColor" opacity="0.15"/>',
            '<rect x="450" y="350" width="2" height="15" fill="currentColor" opacity="0.12"/>',
            '<rect x="650" y="180" width="2" height="25" fill="currentColor" opacity="0.18"/>',
        ]
    ]
];

$selectedPattern = $patterns[$variant] ?? $patterns['default'];
?>

<div class="absolute inset-0 overflow-hidden pointer-events-none" style="opacity: <?php echo e($opacity); ?>">
    <svg 
        class="absolute inset-0 w-full h-full text-green-600" 
        viewBox="<?php echo e($selectedPattern['viewBox']); ?>" 
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="xMidYMid slice"
    >
        <?php $__currentLoopData = $selectedPattern['elements']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $element; ?>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </svg>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/components/background-patterns.blade.php ENDPATH**/ ?>