<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;

class Theme extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'version',
        'description',
        'author',
        'author_url',
        'preview_image',
        'screenshots',
        'config',
        'customizer_settings',
        'is_active',
        'is_default',
        'parent_theme',
        'supported_features',
        'min_version',
        'max_version',
        'changelog',
    ];

    protected $casts = [
        'screenshots' => 'array',
        'config' => 'array',
        'customizer_settings' => 'array',
        'supported_features' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get the active theme
     */
    public static function active(): ?self
    {
        return static::where('is_active', true)->first();
    }

    /**
     * Get the default theme
     */
    public static function default(): ?self
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Activate this theme
     */
    public function activate(): bool
    {
        // Deactivate all other themes
        static::where('is_active', true)->update(['is_active' => false]);

        // Activate this theme
        $this->update(['is_active' => true]);

        // Register theme views
        $this->registerViews();

        return true;
    }

    /**
     * Register theme views with Laravel
     */
    public function registerViews(): void
    {
        $themePath = resource_path("themes/{$this->slug}");

        if (File::exists($themePath)) {
            View::addNamespace('theme', $themePath);

            // Add theme views to the view finder
            $viewPaths = config('view.paths', []);
            array_unshift($viewPaths, $themePath);
            config(['view.paths' => $viewPaths]);
        }
    }

    /**
     * Get theme path
     */
    public function getPath(): string
    {
        return resource_path("themes/{$this->slug}");
    }

    /**
     * Get theme URL
     */
    public function getUrl(): string
    {
        return asset("themes/{$this->slug}");
    }

    /**
     * Get theme asset URL
     */
    public function asset(string $path): string
    {
        return asset("themes/{$this->slug}/assets/{$path}");
    }

    /**
     * Check if theme has a specific feature
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->supported_features ?? []);
    }

    /**
     * Get theme configuration value
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * Set theme configuration value
     */
    public function setConfig(string $key, $value): void
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->update(['config' => $config]);
    }

    /**
     * Get customizer setting value
     */
    public function getCustomizerSetting(string $key, $default = null)
    {
        return data_get($this->customizer_settings, $key, $default);
    }

    /**
     * Set customizer setting value
     */
    public function setCustomizerSetting(string $key, $value): void
    {
        $settings = $this->customizer_settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['customizer_settings' => $settings]);
    }

    /**
     * Check if theme is compatible with current app version
     */
    public function isCompatible(): bool
    {
        $appVersion = config('app.version', '1.0.0');

        if ($this->min_version && version_compare($appVersion, $this->min_version, '<')) {
            return false;
        }

        if ($this->max_version && version_compare($appVersion, $this->max_version, '>')) {
            return false;
        }

        return true;
    }

    /**
     * Get theme manifest data
     */
    public function getManifest(): ?array
    {
        $manifestPath = $this->getPath() . '/theme.json';

        if (File::exists($manifestPath)) {
            return json_decode(File::get($manifestPath), true);
        }

        return null;
    }

    /**
     * Install theme from uploaded file
     */
    public static function install(string $zipPath): array
    {
        try {
            $zip = new \ZipArchive();

            if ($zip->open($zipPath) !== true) {
                return ['success' => false, 'message' => 'Invalid theme file.'];
            }

            // Extract to temporary directory
            $tempDir = storage_path('app/temp/theme_' . uniqid());
            $zip->extractTo($tempDir);
            $zip->close();

            // Read theme manifest
            $manifestPath = $tempDir . '/theme.json';
            if (!File::exists($manifestPath)) {
                File::deleteDirectory($tempDir);
                return ['success' => false, 'message' => 'Theme manifest not found.'];
            }

            $manifest = json_decode(File::get($manifestPath), true);

            if (!$manifest || !isset($manifest['slug'])) {
                File::deleteDirectory($tempDir);
                return ['success' => false, 'message' => 'Invalid theme manifest.'];
            }

            // Check if theme already exists
            if (static::where('slug', $manifest['slug'])->exists()) {
                File::deleteDirectory($tempDir);
                return ['success' => false, 'message' => 'Theme already installed.'];
            }

            // Move theme to themes directory
            $themePath = resource_path("themes/{$manifest['slug']}");
            File::moveDirectory($tempDir, $themePath);

            // Create theme record
            $theme = static::create([
                'name' => $manifest['name'],
                'slug' => $manifest['slug'],
                'version' => $manifest['version'] ?? '1.0.0',
                'description' => $manifest['description'] ?? '',
                'author' => $manifest['author'] ?? '',
                'author_url' => $manifest['author_url'] ?? '',
                'preview_image' => $manifest['preview_image'] ?? '',
                'screenshots' => $manifest['screenshots'] ?? [],
                'supported_features' => $manifest['supported_features'] ?? [],
                'min_version' => $manifest['min_version'] ?? null,
                'max_version' => $manifest['max_version'] ?? null,
            ]);

            return ['success' => true, 'message' => 'Theme installed successfully.', 'theme' => $theme];

        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'Theme installation failed: ' . $e->getMessage()];
        }
    }
}
