<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Fix in the correct order (dependencies first)
        $this->fixBlogPostTagTable(); // Remove pivot table first
        $this->fixBlogPostsTable();   // Remove blog posts (has foreign keys)
        $this->fixCategoriesTable();  // Fix categories
        $this->fixTagsTable();        // Fix tags
        $this->recreateBlogPostsTable(); // Recreate blog posts with proper foreign keys
        $this->recreateBlogPostTagTable(); // Recreate pivot table

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    private function fixCategoriesTable()
    {
        // Backup data if table exists
        $categories = collect();
        if (Schema::hasTable('categories')) {
            $categories = DB::table('categories')->get();
            Schema::drop('categories');
        }

        // Create with proper structure
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#3B82F6'); // Default blue color
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Restore data
        foreach ($categories as $category) {
            if ($category->id) {
                DB::table('categories')->insert([
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'color' => $category->color ?: '#3B82F6',
                    'sort_order' => $category->sort_order ?: 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function fixTagsTable()
    {
        // Backup data if table exists
        $tags = collect();
        if (Schema::hasTable('tags')) {
            $tags = DB::table('tags')->get();
            Schema::drop('tags');
        }

        // Create with proper structure
        Schema::create('tags', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('color', 7)->default('#10B981'); // Default green color
            $table->timestamps();
        });

        // Restore data
        foreach ($tags as $tag) {
            if ($tag->id) {
                DB::table('tags')->insert([
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'slug' => $tag->slug,
                    'color' => $tag->color ?: '#10B981',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function fixBlogPostsTable()
    {
        // Backup data if table exists
        if (Schema::hasTable('blog_posts')) {
            $this->blogPostsBackup = DB::table('blog_posts')->get();
            Schema::drop('blog_posts');
        } else {
            $this->blogPostsBackup = collect();
        }
    }

    private function fixBlogPostTagTable()
    {
        // Backup data if table exists
        if (Schema::hasTable('blog_post_tag')) {
            $this->pivotBackup = DB::table('blog_post_tag')->get();
            Schema::drop('blog_post_tag');
        } else {
            $this->pivotBackup = collect();
        }
    }

    private $blogPostsBackup;
    private $pivotBackup;

    private function recreateBlogPostsTable()
    {
        // Create with proper structure (without foreign key constraints for now)
        Schema::create('blog_posts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('excerpt')->nullable();
            $table->longText('content');
            $table->string('featured_image')->nullable();
            $table->unsignedBigInteger('category_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_published')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->unsignedInteger('views_count')->default(0);
            $table->unsignedInteger('reading_time')->default(1); // in minutes
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_published', 'published_at']);
            $table->index(['category_id']);
            $table->index(['user_id']);
            $table->index(['is_featured']);
        });

        // Restore data
        foreach ($this->blogPostsBackup as $post) {
            if ($post->id) {
                DB::table('blog_posts')->insert([
                    'id' => $post->id,
                    'title' => $post->title,
                    'slug' => $post->slug,
                    'excerpt' => $post->excerpt,
                    'content' => $post->content,
                    'featured_image' => $post->featured_image,
                    'category_id' => $post->category_id ?: null,
                    'user_id' => $post->user_id ?: 1, // Default to first user
                    'is_featured' => $post->is_featured ? true : false,
                    'is_published' => $post->is_published ? true : false,
                    'published_at' => $post->is_published ? now() : null,
                    'meta_title' => $post->meta_title,
                    'meta_description' => $post->meta_description,
                    'meta_keywords' => $post->meta_keywords,
                    'views_count' => $post->views_count ?: 0,
                    'reading_time' => $post->reading_time ?: 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    private function recreateBlogPostTagTable()
    {
        // Create with proper structure (without foreign key constraints for now)
        Schema::create('blog_post_tag', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('blog_post_id');
            $table->unsignedBigInteger('tag_id');
            $table->timestamps();

            // Unique constraint to prevent duplicates
            $table->unique(['blog_post_id', 'tag_id']);

            // Indexes for performance
            $table->index(['blog_post_id']);
            $table->index(['tag_id']);
        });

        // Restore data
        foreach ($this->pivotBackup as $pivot) {
            if ($pivot->blog_post_id && $pivot->tag_id) {
                // Check if both blog post and tag exist
                $postExists = DB::table('blog_posts')->where('id', $pivot->blog_post_id)->exists();
                $tagExists = DB::table('tags')->where('id', $pivot->tag_id)->exists();

                if ($postExists && $tagExists) {
                    DB::table('blog_post_tag')->insert([
                        'blog_post_id' => $pivot->blog_post_id,
                        'tag_id' => $pivot->tag_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
