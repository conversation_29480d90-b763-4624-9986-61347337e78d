<?php

namespace App\Livewire;

use App\Models\ContactEnquiry;
use Illuminate\Support\Facades\Mail;
use Livewire\Attributes\Validate;
use Livewire\Component;

class ContactForm extends Component
{
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|email|max:255')]
    public $email = '';

    #[Validate('nullable|string|max:20')]
    public $phone = '';

    #[Validate('nullable|string|max:255')]
    public $company = '';

    #[Validate('required|string|max:255')]
    public $subject = '';

    #[Validate('required|string|min:10')]
    public $message = '';

    public $submitted = false;

    public function submit()
    {
        $this->validate();

        $enquiry = ContactEnquiry::create([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'company' => $this->company,
            'subject' => $this->subject,
            'message' => $this->message,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'source_page' => request()->url(),
        ]);

        // Send notification email to admin
        // Mail::to(config('mail.admin_email', '<EMAIL>'))
        //     ->send(new ContactEnquiryNotification($enquiry));

        $this->submitted = true;
        $this->reset(['name', 'email', 'phone', 'company', 'subject', 'message']);

        session()->flash('success', 'Thank you for your message! We will get back to you soon.');
    }

    public function render()
    {
        return view('livewire.contact-form');
    }
}
