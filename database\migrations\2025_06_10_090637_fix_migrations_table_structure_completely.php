<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Backup existing migration data
        $migrations = DB::table('migrations')->get();

        // Drop the existing migrations table
        Schema::drop('migrations');

        // Recreate with proper Laravel structure
        Schema::create('migrations', function (Blueprint $table) {
            $table->id();
            $table->string('migration');
            $table->integer('batch');
        });

        // Restore migration data
        foreach ($migrations as $migration) {
            DB::table('migrations')->insert([
                'id' => $migration->id,
                'migration' => $migration->migration,
                'batch' => $migration->batch ?: 1,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
