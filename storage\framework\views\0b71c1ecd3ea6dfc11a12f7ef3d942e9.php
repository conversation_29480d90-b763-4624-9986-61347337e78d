<?php $__env->startSection('title', 'Events'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Upcoming Events</h1>
            <p class="text-xl text-green-100 max-w-3xl mx-auto">
                Join us for exciting events, workshops, and networking opportunities. Stay connected with our community.
            </p>
        </div>
    </div>
</section>

<!-- Featured Events -->
<?php if($featuredEvents->count() > 0): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Events</h2>
            <p class="text-lg text-gray-600">Don't miss these highlighted events</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $featuredEvents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <?php if($event->featured_image): ?>
                    <div class="h-48 bg-cover bg-center" style="background-image: url('<?php echo e(asset('storage/' . $event->featured_image)); ?>')"></div>
                <?php else: ?>
                    <div class="h-48 bg-gradient-to-r from-green-400 to-green-600 flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-white text-4xl"></i>
                    </div>
                <?php endif; ?>
                
                <div class="p-6">
                    <div class="flex items-center text-sm text-gray-500 mb-2">
                        <i class="fas fa-calendar mr-2"></i>
                        <?php echo e($event->formatted_event_date_short); ?>

                        <?php if($event->location): ?>
                            <i class="fas fa-map-marker-alt ml-4 mr-2"></i>
                            <?php echo e(Str::limit($event->location, 20)); ?>

                        <?php endif; ?>
                    </div>
                    
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        <a href="<?php echo e(route('events.show', $event)); ?>" class="hover:text-green-600 transition-colors">
                            <?php echo e($event->title); ?>

                        </a>
                    </h3>
                    
                    <?php if($event->description): ?>
                        <p class="text-gray-600 mb-4"><?php echo e(Str::limit($event->description, 100)); ?></p>
                    <?php endif; ?>
                    
                    <div class="flex items-center justify-between">
                        <?php if($event->price): ?>
                            <span class="text-lg font-semibold text-green-600">$<?php echo e(number_format($event->price, 2)); ?></span>
                        <?php else: ?>
                            <span class="text-lg font-semibold text-green-600">Free</span>
                        <?php endif; ?>
                        
                        <a href="<?php echo e(route('events.show', $event)); ?>" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                            Learn More
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Search and Filter Section -->
<section class="py-8 bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <form method="GET" action="<?php echo e(route('events.index')); ?>" class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" placeholder="Search events..." 
                       value="<?php echo e(request('search')); ?>" 
                       class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500">
            </div>
            
            <select name="filter" class="px-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500">
                <option value="">All Events</option>
                <option value="upcoming" <?php echo e(request('filter') == 'upcoming' ? 'selected' : ''); ?>>Upcoming</option>
                <option value="past" <?php echo e(request('filter') == 'past' ? 'selected' : ''); ?>>Past Events</option>
            </select>
            
            <label class="flex items-center">
                <input type="checkbox" name="featured" value="1" <?php echo e(request('featured') ? 'checked' : ''); ?> 
                       class="mr-2 text-green-600 focus:ring-green-500">
                Featured Only
            </label>
            
            <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            
            <?php if(request()->hasAny(['search', 'filter', 'featured'])): ?>
                <a href="<?php echo e(route('events.index')); ?>" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-times mr-1"></i>Clear
                </a>
            <?php endif; ?>
        </form>
    </div>
</section>

<!-- Events Grid -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if($events->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <?php if($event->featured_image): ?>
                        <div class="h-48 bg-cover bg-center" style="background-image: url('<?php echo e(asset('storage/' . $event->featured_image)); ?>')"></div>
                    <?php else: ?>
                        <div class="h-48 bg-gradient-to-r from-gray-400 to-gray-600 flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-white text-3xl"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-6">
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-calendar mr-2"></i>
                                <?php echo e($event->formatted_event_date_short); ?>

                            </div>
                            <?php if($event->is_featured): ?>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                    Featured
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if($event->location): ?>
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <?php echo e($event->location); ?>

                            </div>
                        <?php endif; ?>
                        
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <a href="<?php echo e(route('events.show', $event)); ?>" class="hover:text-green-600 transition-colors">
                                <?php echo e($event->title); ?>

                            </a>
                        </h3>
                        
                        <?php if($event->description): ?>
                            <p class="text-gray-600 mb-4"><?php echo e(Str::limit($event->description, 120)); ?></p>
                        <?php endif; ?>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <?php if($event->price): ?>
                                    <span class="text-lg font-semibold text-green-600">$<?php echo e(number_format($event->price, 2)); ?></span>
                                <?php else: ?>
                                    <span class="text-lg font-semibold text-green-600">Free</span>
                                <?php endif; ?>
                                
                                <?php if($event->max_attendees && $event->is_full): ?>
                                    <span class="block text-sm text-red-600 font-medium">Event Full</span>
                                <?php elseif($event->max_attendees): ?>
                                    <span class="block text-sm text-gray-500"><?php echo e($event->available_spots); ?> spots left</span>
                                <?php endif; ?>
                            </div>
                            
                            <a href="<?php echo e(route('events.show', $event)); ?>" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <div class="mt-12 flex justify-center">
                <?php echo e($events->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-16">
                <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-2xl font-semibold text-gray-900 mb-2">No Events Found</h3>
                <p class="text-gray-600 mb-6">
                    <?php if(request()->hasAny(['search', 'filter', 'featured'])): ?>
                        No events match your search criteria. Try adjusting your filters.
                    <?php else: ?>
                        There are no events scheduled at the moment. Check back soon!
                    <?php endif; ?>
                </p>
                <?php if(request()->hasAny(['search', 'filter', 'featured'])): ?>
                    <a href="<?php echo e(route('events.index')); ?>" class="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors">
                        View All Events
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Upcoming Events Sidebar -->
<?php if($upcomingEvents->count() > 0 && !request()->hasAny(['search', 'filter', 'featured'])): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Coming Up Next</h2>
            <p class="text-lg text-gray-600">Don't miss these upcoming events</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $upcomingEvents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                <div class="flex items-center text-sm text-gray-500 mb-2">
                    <i class="fas fa-calendar mr-2"></i>
                    <?php echo e($event->formatted_event_date_short); ?>

                </div>
                
                <h4 class="text-lg font-semibold text-gray-900 mb-2">
                    <a href="<?php echo e(route('events.show', $event)); ?>" class="hover:text-green-600 transition-colors">
                        <?php echo e($event->title); ?>

                    </a>
                </h4>
                
                <?php if($event->location): ?>
                    <div class="flex items-center text-sm text-gray-500 mb-3">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <?php echo e(Str::limit($event->location, 30)); ?>

                    </div>
                <?php endif; ?>
                
                <a href="<?php echo e(route('events.show', $event)); ?>" class="text-green-600 hover:text-green-700 font-medium">
                    Learn More <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/events/index.blade.php ENDPATH**/ ?>