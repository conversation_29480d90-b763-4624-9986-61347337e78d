<?php

namespace App\Http\Controllers;

use App\Models\Testimonial;
use Illuminate\Http\Request;

class TestimonialController extends Controller
{
    public function index()
    {
        $testimonials = Testimonial::active()->approved()->ordered()->paginate(12);
        return view('testimonials.index', compact('testimonials'));
    }

    public function create()
    {
        return view('testimonials.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'client_name' => 'required|string|max:500',
            'client_position' => 'nullable|string|max:500',
            'client_company' => 'nullable|string|max:500',
            'testimonial' => 'required|string|max:5000',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'required|integer|min:1|max:5',
            'project_title' => 'nullable|string|max:500',
        ]);

        // Handle image upload
        if ($request->hasFile('client_image')) {
            $validated['client_image'] = $request->file('client_image')->store('testimonials', 'public');
        }

        // Set defaults for public submissions
        $validated['is_active'] = true;
        $validated['is_approved'] = false; // Requires admin approval
        $validated['is_featured'] = false;
        $validated['sort_order'] = 0;

        Testimonial::create($validated);

        return redirect()->route('testimonials.create')
            ->with('success', 'Thank you for your testimonial! It will be reviewed and published soon.');
    }
}
