@extends('installer.layout')

@section('title', 'File Permissions')
@section('step', '3')

@section('content')
<div class="bg-white rounded-lg shadow-lg p-8">
    <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
            <i class="fas fa-shield-alt text-purple-600 text-2xl"></i>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">File Permissions Check</h1>
        <p class="text-gray-600">
            We're checking if the required directories and files have the correct permissions.
        </p>
    </div>

    <div class="space-y-4 mb-8">
        @php
            $allPassed = true;
        @endphp

        @foreach($permissions as $key => $permission)
            @php
                if (!$permission['status']) {
                    $allPassed = false;
                }
            @endphp
            
            <div class="flex items-center justify-between p-4 border rounded-lg {{ $permission['status'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50' }}">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        @if($permission['status'])
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        @else
                            <i class="fas fa-times-circle text-red-500 text-xl"></i>
                        @endif
                    </div>
                    <div>
                        <h3 class="font-medium {{ $permission['status'] ? 'text-green-800' : 'text-red-800' }}">
                            {{ $permission['path'] }}
                        </h3>
                        <p class="text-sm {{ $permission['status'] ? 'text-green-600' : 'text-red-600' }}">
                            Required: {{ $permission['required'] }}
                        </p>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $permission['status'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $permission['status'] ? 'Writable' : 'Not Writable' }}
                    </span>
                </div>
            </div>
        @endforeach
    </div>

    @if(!$allPassed)
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-red-800 mb-2">Permission Issues Found</h3>
                    <div class="text-red-700">
                        <p class="mb-3">
                            Some directories or files don't have the correct permissions. Please fix these issues before proceeding.
                        </p>
                        <div class="bg-red-100 rounded-lg p-4">
                            <h4 class="font-medium text-red-800 mb-2">How to fix permissions:</h4>
                            <div class="text-sm space-y-2">
                                <p><strong>Via SSH/Terminal:</strong></p>
                                <div class="bg-gray-800 text-green-400 p-3 rounded font-mono text-xs overflow-x-auto">
                                    chmod -R 755 storage/<br>
                                    chmod -R 755 bootstrap/cache/<br>
                                    chmod -R 755 public/storage/<br>
                                    chmod 644 .env
                                </div>
                                <p class="mt-3"><strong>Via FTP/File Manager:</strong></p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Right-click on each directory/file</li>
                                    <li>Select "Permissions" or "CHMOD"</li>
                                    <li>Set directories to 755 and files to 644</li>
                                    <li>Apply recursively for directories</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-green-800 mb-2">All Permissions Correct!</h3>
                    <p class="text-green-700">
                        Great! All required directories and files have the correct permissions. 
                        You can proceed to the next step.
                    </p>
                </div>
            </div>
        </div>
    @endif

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-blue-800 mb-2">About File Permissions</h3>
                <div class="text-blue-700 text-sm space-y-2">
                    <p>
                        File permissions control who can read, write, and execute files on your server. 
                        This application needs write access to certain directories to function properly.
                    </p>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>storage/</strong> - For logs, cache, and uploaded files</li>
                        <li><strong>bootstrap/cache/</strong> - For framework cache files</li>
                        <li><strong>public/storage/</strong> - For public file access</li>
                        <li><strong>.env</strong> - For configuration settings</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="flex items-center justify-between">
        <a href="{{ route('installer.requirements') }}" 
           class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors duration-300">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>

        @if($allPassed)
            <a href="{{ route('installer.environment') }}" 
               class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-300">
                Continue
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        @else
            <button type="button" 
                    onclick="window.location.reload()" 
                    class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-300">
                <i class="fas fa-sync-alt mr-2"></i>
                Recheck Permissions
            </button>
        @endif
    </div>

    <div class="mt-8 text-center">
        <p class="text-sm text-gray-500">
            Need help with permissions? Check our 
            <a href="#" class="text-green-600 hover:text-green-700 font-medium">file permissions guide</a> 
            or contact your hosting provider.
        </p>
    </div>
</div>
@endsection
