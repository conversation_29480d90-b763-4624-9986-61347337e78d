<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class Tenant extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'domain',
        'subdomain',
        'database_name',
        'database_host',
        'database_username',
        'database_password',
        'storage_path',
        'config',
        'settings',
        'is_active',
        'is_suspended',
        'trial_ends_at',
        'subscription_ends_at',
        'plan_id',
        'owner_id',
        'created_by',
        'notes',
    ];

    protected $casts = [
        'config' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'is_suspended' => 'boolean',
        'trial_ends_at' => 'datetime',
        'subscription_ends_at' => 'datetime',
    ];

    /**
     * Get the current tenant based on domain/subdomain
     */
    public static function current(): ?self
    {
        $host = request()->getHost();

        // Check for exact domain match first
        $tenant = static::where('domain', $host)
            ->where('is_active', true)
            ->where('is_suspended', false)
            ->first();

        if ($tenant) {
            return $tenant;
        }

        // Check for subdomain match
        $subdomain = explode('.', $host)[0];
        return static::where('subdomain', $subdomain)
            ->where('is_active', true)
            ->where('is_suspended', false)
            ->first();
    }

    /**
     * Get tenant by identifier (slug, domain, or subdomain)
     */
    public static function findByIdentifier(string $identifier): ?self
    {
        return static::where('slug', $identifier)
            ->orWhere('domain', $identifier)
            ->orWhere('subdomain', $identifier)
            ->first();
    }

    /**
     * Create tenant database
     */
    public function createDatabase(): bool
    {
        try {
            $connection = config('database.default');
            $host = $this->database_host ?: config("database.connections.{$connection}.host");
            $username = $this->database_username ?: config("database.connections.{$connection}.username");
            $password = $this->database_password ?: config("database.connections.{$connection}.password");

            // Create database connection
            $pdo = new \PDO(
                "mysql:host={$host}",
                $username,
                $password,
                [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                ]
            );

            // Create database
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$this->database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to create tenant database', [
                'tenant_id' => $this->id,
                'database_name' => $this->database_name,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Configure tenant database connection
     */
    public function configureDatabaseConnection(): void
    {
        $connection = config('database.default');
        $baseConfig = config("database.connections.{$connection}");

        config([
            "database.connections.tenant" => array_merge($baseConfig, [
                'database' => $this->database_name,
                'host' => $this->database_host ?: $baseConfig['host'],
                'username' => $this->database_username ?: $baseConfig['username'],
                'password' => $this->database_password ?: $baseConfig['password'],
            ])
        ]);

        // Purge the connection to force reconnection
        DB::purge('tenant');
    }

    /**
     * Run migrations for tenant
     */
    public function runMigrations(): bool
    {
        try {
            $this->configureDatabaseConnection();

            // Run migrations on tenant database
            \Artisan::call('migrate', [
                '--database' => 'tenant',
                '--force' => true,
            ]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to run tenant migrations', [
                'tenant_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Seed tenant database
     */
    public function seedDatabase(): bool
    {
        try {
            $this->configureDatabaseConnection();

            // Run seeders on tenant database
            \Artisan::call('db:seed', [
                '--database' => 'tenant',
                '--force' => true,
            ]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to seed tenant database', [
                'tenant_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create tenant storage directory
     */
    public function createStorageDirectory(): bool
    {
        try {
            $storagePath = storage_path("app/tenants/{$this->slug}");

            if (!\File::exists($storagePath)) {
                \File::makeDirectory($storagePath, 0755, true);

                // Create subdirectories
                $subdirs = ['uploads', 'themes', 'plugins', 'backups', 'logs'];
                foreach ($subdirs as $subdir) {
                    \File::makeDirectory("{$storagePath}/{$subdir}", 0755, true);
                }
            }

            $this->update(['storage_path' => $storagePath]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to create tenant storage directory', [
                'tenant_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get tenant storage path
     */
    public function getStoragePath(string $path = ''): string
    {
        $basePath = $this->storage_path ?: storage_path("app/tenants/{$this->slug}");

        return $path ? "{$basePath}/{$path}" : $basePath;
    }

    /**
     * Get tenant storage URL
     */
    public function getStorageUrl(string $path = ''): string
    {
        $baseUrl = asset("storage/tenants/{$this->slug}");

        return $path ? "{$baseUrl}/{$path}" : $baseUrl;
    }

    /**
     * Get tenant configuration value
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * Set tenant configuration value
     */
    public function setConfig(string $key, $value): void
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->update(['config' => $config]);
    }

    /**
     * Get tenant setting value
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Set tenant setting value
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['settings' => $settings]);
    }

    /**
     * Check if tenant is active and not suspended
     */
    public function isAccessible(): bool
    {
        if (!$this->is_active || $this->is_suspended) {
            return false;
        }

        // Check subscription status
        if ($this->subscription_ends_at && $this->subscription_ends_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Check if tenant is in trial period
     */
    public function isInTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    /**
     * Get days remaining in trial
     */
    public function getTrialDaysRemaining(): int
    {
        if (!$this->isInTrial()) {
            return 0;
        }

        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }

    /**
     * Suspend tenant
     */
    public function suspend(string $reason = null): void
    {
        $this->update([
            'is_suspended' => true,
            'notes' => $reason ? "Suspended: {$reason}" : 'Suspended',
        ]);
    }

    /**
     * Unsuspend tenant
     */
    public function unsuspend(): void
    {
        $this->update([
            'is_suspended' => false,
            'notes' => 'Unsuspended',
        ]);
    }

    /**
     * Relationships
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
