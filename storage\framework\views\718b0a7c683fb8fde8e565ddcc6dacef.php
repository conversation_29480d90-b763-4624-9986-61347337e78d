<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gallery Management</h1>
        <a href="<?php echo e(route('admin.galleries.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create New Gallery
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.galleries.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search" placeholder="Search galleries..." value="<?php echo e(request('search')); ?>">
                </div>
                <div class="col-md-2">
                    <select name="category" class="form-control">
                        <option value="">All Categories</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category); ?>" <?php echo e(request('category') == $category ? 'selected' : ''); ?>><?php echo e($category); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>Published</option>
                        <option value="unpublished" <?php echo e(request('status') == 'unpublished' ? 'selected' : ''); ?>>Unpublished</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-check-label">
                        <input type="checkbox" name="featured" value="1" <?php echo e(request('featured') ? 'checked' : ''); ?> class="form-check-input">
                        Featured Only
                    </label>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                    <a href="<?php echo e(route('admin.galleries.index')); ?>" class="btn btn-outline-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Galleries Grid -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Galleries</h6>
        </div>
        <div class="card-body">
            <?php if($galleries->count() > 0): ?>
                <!-- Bulk Actions -->
                <form id="bulk-action-form" method="POST" action="<?php echo e(route('admin.galleries.bulk-action')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <select name="action" class="form-control" required>
                                    <option value="">Select Action</option>
                                    <option value="publish">Publish</option>
                                    <option value="unpublish">Unpublish</option>
                                    <option value="feature">Feature</option>
                                    <option value="unfeature">Unfeature</option>
                                    <option value="delete">Delete</option>
                                </select>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <?php $__currentLoopData = $galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 col-lg-3 mb-4">
                            <div class="card h-100 gallery-card">
                                <div class="position-relative">
                                    <input type="checkbox" name="galleries[]" value="<?php echo e($gallery->id); ?>" class="gallery-checkbox position-absolute" style="top: 10px; left: 10px; z-index: 10;">
                                    
                                    <?php if($gallery->cover_image_url): ?>
                                        <img src="<?php echo e($gallery->cover_image_url); ?>" alt="<?php echo e($gallery->title); ?>" class="card-img-top" style="height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                            <i class="fas fa-images text-muted fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Status badges -->
                                    <div class="position-absolute" style="top: 10px; right: 10px;">
                                        <?php if($gallery->is_featured): ?>
                                            <span class="badge badge-primary mb-1">Featured</span><br>
                                        <?php endif; ?>
                                        <?php if($gallery->is_published): ?>
                                            <span class="badge badge-success">Published</span>
                                        <?php else: ?>
                                            <span class="badge badge-warning">Draft</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo e($gallery->title); ?></h6>
                                    <?php if($gallery->category): ?>
                                        <p class="text-muted small mb-1">
                                            <i class="fas fa-folder mr-1"></i><?php echo e($gallery->category); ?>

                                        </p>
                                    <?php endif; ?>
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-images mr-1"></i><?php echo e($gallery->images_count); ?> images
                                    </p>
                                    <?php if($gallery->description): ?>
                                        <p class="card-text small text-muted"><?php echo e(Str::limit($gallery->description, 60)); ?></p>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100" role="group">
                                        <a href="<?php echo e(route('admin.galleries.show', $gallery)); ?>" class="btn btn-info btn-sm" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.galleries.edit', $gallery)); ?>" class="btn btn-warning btn-sm" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="<?php echo e(route('admin.galleries.destroy', $gallery)); ?>" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this gallery and all its images?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </form>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    <?php echo e($galleries->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No galleries found</h5>
                    <p class="text-muted">Start by creating your first gallery.</p>
                    <a href="<?php echo e(route('admin.galleries.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Gallery
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.gallery-card {
    transition: transform 0.2s;
}
.gallery-card:hover {
    transform: translateY(-2px);
}
.gallery-checkbox {
    width: 20px;
    height: 20px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const galleryCheckboxes = document.querySelectorAll('.gallery-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            galleryCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Bulk action form validation
    document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.gallery-checkbox:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one gallery.');
            return false;
        }
        
        const action = document.querySelector('select[name="action"]').value;
        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected galleries and all their images?')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/admin/galleries/index.blade.php ENDPATH**/ ?>