<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugins', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('version')->default('1.0.0');
            $table->text('description')->nullable();
            $table->string('author')->nullable();
            $table->string('author_url')->nullable();
            $table->string('plugin_url')->nullable();
            $table->json('config')->nullable(); // Plugin configuration
            $table->json('settings')->nullable(); // Plugin settings
            $table->boolean('is_active')->default(false);
            $table->boolean('is_installed')->default(false);
            $table->string('main_file')->nullable(); // Main plugin file
            $table->json('dependencies')->nullable(); // Required plugins/packages
            $table->json('hooks')->nullable(); // Registered hooks
            $table->string('min_version')->nullable(); // Minimum app version
            $table->string('max_version')->nullable(); // Maximum app version
            $table->integer('priority')->default(10); // Loading priority
            $table->text('installation_notes')->nullable();
            $table->timestamp('installed_at')->nullable();
            $table->timestamp('activated_at')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'priority']);
            $table->index(['is_installed', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugins');
    }
};
