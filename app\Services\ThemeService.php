<?php

namespace App\Services;

use App\Models\Theme;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class ThemeService
{
    protected $activeTheme;

    public function __construct()
    {
        $this->activeTheme = $this->getActiveTheme();
    }

    /**
     * Get the currently active theme
     */
    public function getActiveTheme(): ?Theme
    {
        if ($this->activeTheme) {
            return $this->activeTheme;
        }

        return Cache::remember('active_theme', 3600, function () {
            return Theme::active() ?? Theme::default();
        });
    }

    /**
     * Set the active theme
     */
    public function setActiveTheme(Theme $theme): bool
    {
        if (!$theme->isCompatible()) {
            return false;
        }

        $result = $theme->activate();
        
        if ($result) {
            $this->activeTheme = $theme;
            Cache::forget('active_theme');
            
            // Clear view cache
            Artisan::call('view:clear');
        }

        return $result;
    }

    /**
     * Get all available themes
     */
    public function getAvailableThemes(): \Illuminate\Database\Eloquent\Collection
    {
        return Theme::orderBy('name')->get();
    }

    /**
     * Scan for themes in the themes directory
     */
    public function scanForThemes(): array
    {
        $themesPath = resource_path('themes');
        $foundThemes = [];

        if (!File::exists($themesPath)) {
            File::makeDirectory($themesPath, 0755, true);
            return $foundThemes;
        }

        $directories = File::directories($themesPath);

        foreach ($directories as $directory) {
            $slug = basename($directory);
            $manifestPath = $directory . '/theme.json';

            if (File::exists($manifestPath)) {
                $manifest = json_decode(File::get($manifestPath), true);
                
                if ($manifest && isset($manifest['name'])) {
                    $existingTheme = Theme::where('slug', $slug)->first();
                    
                    if (!$existingTheme) {
                        $foundThemes[] = [
                            'slug' => $slug,
                            'path' => $directory,
                            'manifest' => $manifest,
                        ];
                    }
                }
            }
        }

        return $foundThemes;
    }

    /**
     * Register discovered themes
     */
    public function registerDiscoveredThemes(): int
    {
        $discoveredThemes = $this->scanForThemes();
        $registered = 0;

        foreach ($discoveredThemes as $themeData) {
            $manifest = $themeData['manifest'];
            
            Theme::create([
                'name' => $manifest['name'],
                'slug' => $themeData['slug'],
                'version' => $manifest['version'] ?? '1.0.0',
                'description' => $manifest['description'] ?? '',
                'author' => $manifest['author'] ?? '',
                'author_url' => $manifest['author_url'] ?? '',
                'preview_image' => $manifest['preview_image'] ?? '',
                'screenshots' => $manifest['screenshots'] ?? [],
                'supported_features' => $manifest['supported_features'] ?? [],
                'min_version' => $manifest['min_version'] ?? null,
                'max_version' => $manifest['max_version'] ?? null,
            ]);
            
            $registered++;
        }

        return $registered;
    }

    /**
     * Get theme asset URL
     */
    public function asset(string $path): string
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            return $theme->asset($path);
        }

        return asset($path);
    }

    /**
     * Get theme configuration
     */
    public function getConfig(string $key, $default = null)
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            return $theme->getConfig($key, $default);
        }

        return $default;
    }

    /**
     * Set theme configuration
     */
    public function setConfig(string $key, $value): void
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            $theme->setConfig($key, $value);
        }
    }

    /**
     * Get customizer setting
     */
    public function getCustomizerSetting(string $key, $default = null)
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            return $theme->getCustomizerSetting($key, $default);
        }

        return $default;
    }

    /**
     * Set customizer setting
     */
    public function setCustomizerSetting(string $key, $value): void
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            $theme->setCustomizerSetting($key, $value);
        }
    }

    /**
     * Check if current theme has a feature
     */
    public function hasFeature(string $feature): bool
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            return $theme->hasFeature($feature);
        }

        return false;
    }

    /**
     * Render a theme view
     */
    public function view(string $view, array $data = []): \Illuminate\Contracts\View\View
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            $themeView = "theme::{$view}";
            
            if (View::exists($themeView)) {
                return view($themeView, $data);
            }
        }

        // Fallback to default view
        return view($view, $data);
    }

    /**
     * Check if a theme view exists
     */
    public function viewExists(string $view): bool
    {
        $theme = $this->getActiveTheme();
        
        if ($theme) {
            return View::exists("theme::{$view}");
        }

        return View::exists($view);
    }

    /**
     * Get theme customizer panels
     */
    public function getCustomizerPanels(): array
    {
        $theme = $this->getActiveTheme();
        
        if (!$theme) {
            return [];
        }

        $manifest = $theme->getManifest();
        
        return $manifest['customizer']['panels'] ?? [];
    }

    /**
     * Create default theme
     */
    public function createDefaultTheme(): Theme
    {
        return Theme::firstOrCreate(
            ['slug' => 'default'],
            [
                'name' => 'Default Theme',
                'version' => '1.0.0',
                'description' => 'Default theme for the application',
                'author' => 'System',
                'is_default' => true,
                'is_active' => true,
                'supported_features' => [
                    'customizer',
                    'menus',
                    'widgets',
                    'post-thumbnails',
                    'custom-header',
                    'custom-background',
                ],
            ]
        );
    }

    /**
     * Delete a theme
     */
    public function deleteTheme(Theme $theme): bool
    {
        if ($theme->is_default || $theme->is_active) {
            return false;
        }

        // Delete theme files
        $themePath = $theme->getPath();
        if (File::exists($themePath)) {
            File::deleteDirectory($themePath);
        }

        // Delete theme record
        $theme->delete();

        return true;
    }

    /**
     * Export theme
     */
    public function exportTheme(Theme $theme): string
    {
        $themePath = $theme->getPath();
        $exportPath = storage_path("app/exports/{$theme->slug}-{$theme->version}.zip");
        
        // Create export directory if it doesn't exist
        File::ensureDirectoryExists(dirname($exportPath));
        
        $zip = new \ZipArchive();
        $zip->open($exportPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);
        
        // Add theme files to zip
        $this->addDirectoryToZip($zip, $themePath, '');
        
        $zip->close();
        
        return $exportPath;
    }

    /**
     * Add directory to zip recursively
     */
    private function addDirectoryToZip(\ZipArchive $zip, string $directory, string $localPath): void
    {
        $files = File::allFiles($directory);
        
        foreach ($files as $file) {
            $relativePath = $localPath . '/' . $file->getRelativePathname();
            $zip->addFile($file->getRealPath(), ltrim($relativePath, '/'));
        }
    }
}
