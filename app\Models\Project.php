<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'content',
        'client',
        'category',
        'start_date',
        'end_date',
        'status',
        'featured_image',
        'gallery',
        'project_url',
        'technologies',
        'sort_order',
        'is_featured',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'gallery' => 'array',
        'technologies' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Boot method to auto-generate slug
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($project) {
            if (empty($project->slug)) {
                $project->slug = Str::slug($project->title);
            }
        });

        static::updating(function ($project) {
            if ($project->isDirty('title') && empty($project->slug)) {
                $project->slug = Str::slug($project->title);
            }
        });
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOngoing($query)
    {
        return $query->where('status', 'ongoing');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image && Storage::disk('public')->exists($this->featured_image)) {
            return asset('storage/' . $this->featured_image);
        }
        return null;
    }

    public function getGalleryUrlsAttribute()
    {
        if (!$this->gallery) {
            return [];
        }

        return collect($this->gallery)->map(function ($image) {
            if (Storage::disk('public')->exists($image)) {
                return asset('storage/' . $image);
            }
            return null;
        })->filter()->values()->toArray();
    }

    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'completed' => '<span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Completed</span>',
            'ongoing' => '<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Ongoing</span>',
            'planned' => '<span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">Planned</span>',
            default => '<span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">' . ucfirst($this->status) . '</span>',
        };
    }

    public function getDurationAttribute()
    {
        if (!$this->start_date) {
            return null;
        }

        $end = $this->end_date ?? now();
        return $this->start_date->diffInDays($end) + 1;
    }

    // Static methods
    public static function getFeaturedProjects($limit = 6)
    {
        return static::active()->featured()->ordered()->limit($limit)->get();
    }

    public static function getRecentProjects($limit = 3)
    {
        return static::active()->ordered()->limit($limit)->get();
    }
}
