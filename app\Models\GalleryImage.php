<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class GalleryImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'gallery_id',
        'title',
        'caption',
        'image_path',
        'thumbnail_path',
        'alt_text',
        'file_size',
        'mime_type',
        'width',
        'height',
        'sort_order',
        'is_featured',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($image) {
            // Delete image files when model is deleted
            if ($image->image_path) {
                Storage::disk('public')->delete($image->image_path);
            }
            if ($image->thumbnail_path) {
                Storage::disk('public')->delete($image->thumbnail_path);
            }
        });
    }

    // Relationships
    public function gallery()
    {
        return $this->belongsTo(Gallery::class);
    }

    // Scopes
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if ($this->image_path) {
            return asset('storage/' . $this->image_path);
        }
        
        return asset('images/default-image.jpg');
    }

    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail_path) {
            return asset('storage/' . $this->thumbnail_path);
        }
        
        // Fallback to main image if no thumbnail
        return $this->image_url;
    }

    public function getFileSizeHumanAttribute()
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getDimensionsAttribute()
    {
        if ($this->width && $this->height) {
            return $this->width . ' × ' . $this->height;
        }
        
        return 'Unknown';
    }

    public function getAspectRatioAttribute()
    {
        if ($this->width && $this->height) {
            return $this->width / $this->height;
        }
        
        return 1; // Default square aspect ratio
    }

    public function getIsLandscapeAttribute()
    {
        return $this->aspect_ratio > 1;
    }

    public function getIsPortraitAttribute()
    {
        return $this->aspect_ratio < 1;
    }

    public function getIsSquareAttribute()
    {
        return abs($this->aspect_ratio - 1) < 0.1; // Allow small tolerance
    }

    /**
     * Generate thumbnail path based on image path
     */
    public function generateThumbnailPath()
    {
        if (!$this->image_path) {
            return null;
        }

        $pathInfo = pathinfo($this->image_path);
        return $pathInfo['dirname'] . '/thumbs/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
    }

    /**
     * Get next image in the same gallery
     */
    public function getNextImageAttribute()
    {
        return $this->gallery->images()
            ->where('sort_order', '>', $this->sort_order)
            ->orderBy('sort_order')
            ->first();
    }

    /**
     * Get previous image in the same gallery
     */
    public function getPreviousImageAttribute()
    {
        return $this->gallery->images()
            ->where('sort_order', '<', $this->sort_order)
            ->orderBy('sort_order', 'desc')
            ->first();
    }
}
