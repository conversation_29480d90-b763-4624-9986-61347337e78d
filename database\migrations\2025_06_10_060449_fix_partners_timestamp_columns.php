<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, backup existing data
        $partners = DB::table('partners')->get();

        Schema::table('partners', function (Blueprint $table) {
            // Drop the existing VARCHAR timestamp columns
            $table->dropColumn(['created_at', 'updated_at']);
        });

        Schema::table('partners', function (Blueprint $table) {
            // Add proper timestamp columns
            $table->timestamps();
        });

        // Restore data with proper timestamps
        foreach ($partners as $partner) {
            DB::table('partners')
                ->where('id', $partner->id)
                ->update([
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('partners', function (Blueprint $table) {
            // This migration cannot be easily reversed
            // You would need to restore from backup
        });
    }
};
