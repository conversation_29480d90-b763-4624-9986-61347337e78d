<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use ZipArchive;
use Exception;

class UpdateService
{
    protected $updateServerUrl;
    protected $currentVersion;
    protected $updatePath;

    public function __construct()
    {
        $this->updateServerUrl = config('app.update_server_url', 'https://updates.yourcompany.com');
        $this->currentVersion = config('app.version', '1.0.0');
        $this->updatePath = storage_path('app/updates');
    }

    /**
     * Check for available updates
     */
    public function checkForUpdates(): array
    {
        try {
            $response = Http::timeout(30)->get("{$this->updateServerUrl}/api/check-update", [
                'current_version' => $this->currentVersion,
                'license_key' => config('license.key'),
                'domain' => request()->getHost(),
            ]);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'message' => 'Unable to check for updates. Please try again later.',
                ];
            }

            $data = $response->json();

            return [
                'success' => true,
                'has_update' => $data['has_update'] ?? false,
                'latest_version' => $data['latest_version'] ?? $this->currentVersion,
                'current_version' => $this->currentVersion,
                'changelog' => $data['changelog'] ?? '',
                'download_url' => $data['download_url'] ?? '',
                'file_size' => $data['file_size'] ?? 0,
                'release_date' => $data['release_date'] ?? '',
                'is_critical' => $data['is_critical'] ?? false,
            ];

        } catch (Exception $e) {
            Log::error('Failed to check for updates', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to check for updates: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Download update
     */
    public function downloadUpdate(string $downloadUrl, string $version): array
    {
        try {
            // Ensure update directory exists
            if (!File::exists($this->updatePath)) {
                File::makeDirectory($this->updatePath, 0755, true);
            }

            $updateFile = "{$this->updatePath}/update-{$version}.zip";

            // Download the update file
            $response = Http::timeout(300)->get($downloadUrl);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'message' => 'Failed to download update file.',
                ];
            }

            File::put($updateFile, $response->body());

            // Verify the downloaded file
            if (!File::exists($updateFile) || File::size($updateFile) === 0) {
                return [
                    'success' => false,
                    'message' => 'Downloaded update file is invalid.',
                ];
            }

            return [
                'success' => true,
                'file_path' => $updateFile,
                'message' => 'Update downloaded successfully.',
            ];

        } catch (Exception $e) {
            Log::error('Failed to download update', [
                'version' => $version,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to download update: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Install update
     */
    public function installUpdate(string $updateFile, string $version): array
    {
        try {
            // Create backup before updating
            $backupResult = $this->createBackup();
            if (!$backupResult['success']) {
                return $backupResult;
            }

            // Extract update files
            $extractResult = $this->extractUpdate($updateFile);
            if (!$extractResult['success']) {
                return $extractResult;
            }

            // Run pre-update scripts
            $this->runPreUpdateScripts($version);

            // Apply file updates
            $applyResult = $this->applyFileUpdates($extractResult['extract_path']);
            if (!$applyResult['success']) {
                // Restore backup on failure
                $this->restoreBackup($backupResult['backup_path']);
                return $applyResult;
            }

            // Run database migrations
            $migrationResult = $this->runMigrations();
            if (!$migrationResult['success']) {
                // Restore backup on failure
                $this->restoreBackup($backupResult['backup_path']);
                return $migrationResult;
            }

            // Run post-update scripts
            $this->runPostUpdateScripts($version);

            // Update version in config
            $this->updateVersion($version);

            // Clear caches
            $this->clearCaches();

            // Cleanup
            $this->cleanupUpdateFiles($updateFile, $extractResult['extract_path']);

            Log::info('Update installed successfully', [
                'from_version' => $this->currentVersion,
                'to_version' => $version,
            ]);

            return [
                'success' => true,
                'message' => "Successfully updated from version {$this->currentVersion} to {$version}.",
                'backup_path' => $backupResult['backup_path'],
            ];

        } catch (Exception $e) {
            Log::error('Failed to install update', [
                'version' => $version,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to install update: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create backup before update
     */
    protected function createBackup(): array
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!File::exists($backupPath)) {
                File::makeDirectory($backupPath, 0755, true);
            }

            $backupFile = "{$backupPath}/backup-" . date('Y-m-d-H-i-s') . '.zip';
            $zip = new ZipArchive();

            if ($zip->open($backupFile, ZipArchive::CREATE) !== true) {
                return [
                    'success' => false,
                    'message' => 'Failed to create backup file.',
                ];
            }

            // Add application files to backup
            $this->addDirectoryToZip($zip, base_path(), '', [
                'vendor',
                'node_modules',
                'storage/logs',
                'storage/app/backups',
                'storage/app/updates',
                '.git',
            ]);

            $zip->close();

            return [
                'success' => true,
                'backup_path' => $backupFile,
                'message' => 'Backup created successfully.',
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create backup: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Extract update files
     */
    protected function extractUpdate(string $updateFile): array
    {
        try {
            $extractPath = "{$this->updatePath}/extract-" . uniqid();
            
            $zip = new ZipArchive();
            if ($zip->open($updateFile) !== true) {
                return [
                    'success' => false,
                    'message' => 'Failed to open update file.',
                ];
            }

            $zip->extractTo($extractPath);
            $zip->close();

            return [
                'success' => true,
                'extract_path' => $extractPath,
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to extract update: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Apply file updates
     */
    protected function applyFileUpdates(string $extractPath): array
    {
        try {
            $updateManifest = "{$extractPath}/update-manifest.json";
            
            if (!File::exists($updateManifest)) {
                return [
                    'success' => false,
                    'message' => 'Update manifest not found.',
                ];
            }

            $manifest = json_decode(File::get($updateManifest), true);
            
            // Copy new/updated files
            if (isset($manifest['files'])) {
                foreach ($manifest['files'] as $file) {
                    $sourcePath = "{$extractPath}/{$file}";
                    $targetPath = base_path($file);
                    
                    if (File::exists($sourcePath)) {
                        // Ensure target directory exists
                        File::ensureDirectoryExists(dirname($targetPath));
                        File::copy($sourcePath, $targetPath);
                    }
                }
            }

            // Delete removed files
            if (isset($manifest['deleted_files'])) {
                foreach ($manifest['deleted_files'] as $file) {
                    $filePath = base_path($file);
                    if (File::exists($filePath)) {
                        File::delete($filePath);
                    }
                }
            }

            return [
                'success' => true,
                'message' => 'Files updated successfully.',
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to apply file updates: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Run database migrations
     */
    protected function runMigrations(): array
    {
        try {
            Artisan::call('migrate', ['--force' => true]);
            
            return [
                'success' => true,
                'message' => 'Database migrations completed.',
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to run migrations: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update version in configuration
     */
    protected function updateVersion(string $version): void
    {
        $configPath = config_path('app.php');
        $config = File::get($configPath);
        
        $config = preg_replace(
            "/'version'\s*=>\s*'[^']*'/",
            "'version' => '{$version}'",
            $config
        );
        
        File::put($configPath, $config);
    }

    /**
     * Clear application caches
     */
    protected function clearCaches(): void
    {
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        Artisan::call('cache:clear');
    }

    /**
     * Run pre-update scripts
     */
    protected function runPreUpdateScripts(string $version): void
    {
        $scriptPath = base_path("updates/pre-update-{$version}.php");
        if (File::exists($scriptPath)) {
            include $scriptPath;
        }
    }

    /**
     * Run post-update scripts
     */
    protected function runPostUpdateScripts(string $version): void
    {
        $scriptPath = base_path("updates/post-update-{$version}.php");
        if (File::exists($scriptPath)) {
            include $scriptPath;
        }
    }

    /**
     * Add directory to zip recursively
     */
    protected function addDirectoryToZip(ZipArchive $zip, string $directory, string $localPath, array $excludes = []): void
    {
        $files = File::allFiles($directory);
        
        foreach ($files as $file) {
            $relativePath = $file->getRelativePathname();
            
            // Skip excluded paths
            $skip = false;
            foreach ($excludes as $exclude) {
                if (str_starts_with($relativePath, $exclude)) {
                    $skip = true;
                    break;
                }
            }
            
            if (!$skip) {
                $zipPath = $localPath ? "{$localPath}/{$relativePath}" : $relativePath;
                $zip->addFile($file->getRealPath(), $zipPath);
            }
        }
    }

    /**
     * Restore backup
     */
    protected function restoreBackup(string $backupPath): bool
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($backupPath) !== true) {
                return false;
            }

            $zip->extractTo(base_path());
            $zip->close();

            return true;

        } catch (Exception $e) {
            Log::error('Failed to restore backup', [
                'backup_path' => $backupPath,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Cleanup update files
     */
    protected function cleanupUpdateFiles(string $updateFile, string $extractPath): void
    {
        try {
            if (File::exists($updateFile)) {
                File::delete($updateFile);
            }
            
            if (File::exists($extractPath)) {
                File::deleteDirectory($extractPath);
            }
        } catch (Exception $e) {
            Log::warning('Failed to cleanup update files', [
                'error' => $e->getMessage(),
            ]);
        }
    }
}
