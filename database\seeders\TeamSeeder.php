<?php

namespace Database\Seeders;

use App\Models\Team;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = [
            [
                'name' => 'YUSUFU A. MODIBBO',
                'slug' => 'yusufu-a-modibbo',
                'position' => 'CHAIRMAN',
                'department' => 'executive',
                'sort_order' => 1,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'name' => 'JA\'AFARU A. ABUBAKAR',
                'slug' => 'jaafaru-a-abubakar',
                'position' => 'GENERAL MANAGER',
                'department' => 'executive',
                'sort_order' => 2,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'name' => 'ISA ALIYU MODIBBO',
                'slug' => 'isa-aliyu-modibbo',
                'position' => 'PROJECT OPERATION MANAGER ABUJA/NORTH EAST',
                'department' => 'executive',
                'sort_order' => 3,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'name' => 'MAHMOUD LIMAN BELLO',
                'slug' => 'mahmoud-liman-bello',
                'position' => 'ENGINEER',
                'department' => 'project',
                'sort_order' => 1,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'name' => 'HABIB MUSTAPHA',
                'slug' => 'habib-mustapha',
                'position' => 'ENGINEER',
                'department' => 'project',
                'sort_order' => 2,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'name' => 'ADAMU GIDADO (MNSE)',
                'slug' => 'adamu-gidado-mnse',
                'position' => 'ENGINEER',
                'department' => 'project',
                'sort_order' => 3,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'name' => 'GAMBO AHMADU',
                'slug' => 'gambo-ahmadu',
                'position' => 'ENGINEER',
                'department' => 'project',
                'sort_order' => 4,
                'is_featured' => false,
                'is_active' => true,
            ],
        ];

        foreach ($teams as $team) {
            Team::firstOrCreate(
                ['slug' => $team['slug']],
                $team
            );
        }
    }
}
