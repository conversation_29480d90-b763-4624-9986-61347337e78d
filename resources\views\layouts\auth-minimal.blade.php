<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($title) ? $title . ' - ' : '' }}{{ $globalSettings->application_name ?? config('app.name', 'Fair Price Ventures') }}</title>

    <!-- Favicon -->
    @if($globalSettings->favicon ?? null)
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . $globalSettings->favicon) }}">
    @endif

    <!-- SEO Meta Tags -->
    <meta name="description" content="{{ $globalSettings->tagline ?? 'Your trusted business partner' }}">
    @if($globalSettings->seo_keywords ?? null)
        <meta name="keywords" content="{{ $globalSettings->seo_keywords }}">
    @endif

    <!-- Open Graph -->
    <meta property="og:title" content="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}">
    <meta property="og:description" content="{{ $globalSettings->tagline ?? 'Your trusted business partner' }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url('/') }}">
    @if($globalSettings->logo ?? null)
        <meta property="og:image" content="{{ asset('storage/' . $globalSettings->logo) }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:300,400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    @stack('styles')
</head>
<body class="font-sans antialiased bg-gradient-to-br from-green-50 via-white to-green-50 min-h-screen">
    <!-- Authentication Content -->
    <main class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Back to Website Link -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="inline-flex items-center text-green-600 hover:text-green-800 font-medium transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Website
                </a>
            </div>

            <!-- Authentication Card -->
            <div class="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6 text-center">
                    <div class="flex justify-center mb-4">
                        @if($globalSettings->logo ?? null)
                            <img src="{{ asset('storage/' . $globalSettings->logo) }}" alt="{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}" class="h-16 w-auto">
                        @else
                            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-user-circle text-green-600 text-3xl"></i>
                            </div>
                        @endif
                    </div>
                    <h1 class="text-2xl font-bold text-white">{{ $globalSettings->application_name ?? 'Fair Price Ventures' }}</h1>
                    <p class="text-green-100 mt-2">{{ isset($subtitle) ? $subtitle : 'Welcome back' }}</p>
                </div>

                <!-- Form Content -->
                <div class="px-8 py-8">
                    {{ $slot }}
                </div>
            </div>

            <!-- Footer Links -->
            <div class="text-center">
                <p class="text-gray-600 text-sm">
                    © {{ date('Y') }} {{ $globalSettings->application_name ?? 'Fair Price Ventures' }}. All rights reserved.
                </p>
            </div>
        </div>
    </main>

    @livewireScripts
    @stack('scripts')

    <!-- Global Loading Overlay -->
    <x-loading-overlay />

    <!-- Flash Messages -->
    @if(session('success'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('error') }}
        </div>
    @endif

    @if(session('info'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)" class="fixed bottom-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('info') }}
        </div>
    @endif

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</body>
</html>
