<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactEnquiry;
use Illuminate\Http\Request;

class ContactEnquiryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ContactEnquiry::query();

        // Filter by read status
        if ($request->filled('status')) {
            if ($request->status === 'unread') {
                $query->unread();
            } elseif ($request->status === 'read') {
                $query->read();
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $enquiries = $query->latest()->paginate(15);

        return view('admin.contact-enquiries.index', compact('enquiries'));
    }

    /**
     * Display the specified resource.
     */
    public function show(ContactEnquiry $contactEnquiry)
    {
        // Mark as read when viewing
        if (!$contactEnquiry->is_read) {
            $contactEnquiry->markAsRead();
        }

        return view('admin.contact-enquiries.show', compact('contactEnquiry'));
    }

    /**
     * Mark enquiry as read/unread
     */
    public function markAsRead(ContactEnquiry $contactEnquiry)
    {
        if ($contactEnquiry->is_read) {
            $contactEnquiry->markAsUnread();
            $message = 'Enquiry marked as unread.';
        } else {
            $contactEnquiry->markAsRead();
            $message = 'Enquiry marked as read.';
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContactEnquiry $contactEnquiry)
    {
        $contactEnquiry->delete();

        return redirect()->route('admin.contact-enquiries.index')->with('success', 'Contact enquiry deleted successfully.');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:mark_read,mark_unread,delete',
            'enquiries' => 'required|array',
            'enquiries.*' => 'exists:contact_enquiries,id',
        ]);

        $enquiries = ContactEnquiry::whereIn('id', $request->enquiries);

        switch ($request->action) {
            case 'mark_read':
                $enquiries->update(['is_read' => true, 'read_at' => now()]);
                $message = 'Selected enquiries marked as read.';
                break;
            case 'mark_unread':
                $enquiries->update(['is_read' => false, 'read_at' => null]);
                $message = 'Selected enquiries marked as unread.';
                break;
            case 'delete':
                $enquiries->delete();
                $message = 'Selected enquiries deleted successfully.';
                break;
        }

        return redirect()->route('admin.contact-enquiries.index')->with('success', $message);
    }
}
