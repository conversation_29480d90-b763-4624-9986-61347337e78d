<?php

namespace Database\Seeders;

use App\Models\Project;
use Illuminate\Database\Seeder;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $projects = [
            [
                'title' => 'Kaduna Eastern Byepass',
                'slug' => 'kaduna-eastern-byepass',
                'description' => 'Kaduna Eastern Byepass dual carriage way up to 1550',
                'client' => 'Eksiogullari Nigeria Limited',
                'category' => 'Roads, Drainages, Bridges',
                'status' => 'completed',
                'sort_order' => 1,
                'is_featured' => false,
                'is_active' => true,
            ],
        ];

        foreach ($projects as $project) {
            Project::firstOrCreate(
                ['slug' => $project['slug']],
                $project
            );
        }
    }
}
