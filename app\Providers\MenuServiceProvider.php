<?php

namespace App\Providers;

use App\Models\MenuItem;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class MenuServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share menu items with all views
        View::composer('*', function ($view) {
            // Get main navigation menu
            $mainMenu = MenuItem::getMenuTree('main', true);

            // Get footer menu
            $footerMenu = MenuItem::getMenuTree('footer', true);

            // Share with all views
            $view->with([
                'mainMenu' => $mainMenu,
                'footerMenu' => $footerMenu,
            ]);
        });
    }
}
