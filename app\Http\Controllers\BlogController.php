<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    public function index(Request $request)
    {
        $query = BlogPost::published()->with('user', 'category', 'tags')->latest();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Tag filter
        if ($request->filled('tag')) {
            $query->whereHas('tags', function ($q) use ($request) {
                $q->where('slug', $request->tag);
            });
        }

        $posts = $query->paginate(12);
        $categories = Category::ordered()->get();
        $tags = Tag::orderBy('name')->get();

        return view('blog.index', compact('posts', 'categories', 'tags'));
    }

    public function show(BlogPost $post)
    {
        if (!$post->is_published || $post->published_at > now()) {
            abort(404);
        }

        // Increment views
        $post->incrementViews();

        $relatedPosts = BlogPost::published()
            ->where('id', '!=', $post->id)
            ->where('category_id', $post->category_id)
            ->limit(3)
            ->get();

        return view('blog.show', compact('post', 'relatedPosts'));
    }

    public function category(Category $category)
    {
        $posts = $category->publishedBlogPosts()->with('user', 'tags')->latest()->paginate(12);

        return view('blog.category', compact('category', 'posts'));
    }

    public function tag(Tag $tag)
    {
        $posts = $tag->publishedBlogPosts()->with('user', 'category')->latest()->paginate(12);

        return view('blog.tag', compact('tag', 'posts'));
    }
}
