<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class MenuController extends Controller
{
    /**
     * Display menu management interface
     */
    public function index(Request $request)
    {
        $location = $request->get('location', 'main');
        $menuItems = MenuItem::getMenuTree($location, false); // Include inactive items for admin
        $locations = MenuItem::getAvailableLocations();
        $availableRoutes = $this->getAvailableRoutes();

        return view('admin.menus.index', compact('menuItems', 'locations', 'location', 'availableRoutes'));
    }

    /**
     * Store a new menu item
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'menu_location' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'url' => 'nullable|string|max:255',
            'route_name' => 'nullable|string|max:255',
            'route_params' => 'nullable|array',
            'target' => 'required|in:_self,_blank,_parent,_top',
            'css_class' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        // Validate that either URL or route_name is provided
        if (empty($validated['url']) && empty($validated['route_name'])) {
            return response()->json([
                'success' => false,
                'message' => 'Either URL or Route Name must be provided.'
            ], 422);
        }

        // Get next sort order
        $maxOrder = MenuItem::where('menu_location', $validated['menu_location'])
            ->where('parent_id', $validated['parent_id'])
            ->max('sort_order') ?? 0;

        $validated['sort_order'] = $maxOrder + 1;
        $validated['is_active'] = $request->has('is_active');

        $menuItem = MenuItem::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Menu item created successfully.',
            'item' => $menuItem->load('children')
        ]);
    }

    /**
     * Update a menu item
     */
    public function update(Request $request, MenuItem $menuItem)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'url' => 'nullable|string|max:255',
            'route_name' => 'nullable|string|max:255',
            'route_params' => 'nullable|array',
            'target' => 'required|in:_self,_blank,_parent,_top',
            'css_class' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        // Validate that either URL or route_name is provided
        if (empty($validated['url']) && empty($validated['route_name'])) {
            return response()->json([
                'success' => false,
                'message' => 'Either URL or Route Name must be provided.'
            ], 422);
        }

        $validated['is_active'] = $request->has('is_active');

        $menuItem->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Menu item updated successfully.',
            'item' => $menuItem->fresh()->load('children')
        ]);
    }

    /**
     * Delete a menu item
     */
    public function destroy(MenuItem $menuItem)
    {
        // Check if item has children
        if ($menuItem->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete menu item with children. Please delete or move children first.'
            ], 422);
        }

        $menuItem->delete();

        return response()->json([
            'success' => true,
            'message' => 'Menu item deleted successfully.'
        ]);
    }

    /**
     * Update menu structure (drag and drop reordering)
     */
    public function updateStructure(Request $request)
    {
        $request->validate([
            'location' => 'required|string',
            'items' => 'required|array',
        ]);

        $this->updateMenuItems($request->items, null, $request->location);

        return response()->json([
            'success' => true,
            'message' => 'Menu structure updated successfully.'
        ]);
    }

    /**
     * Recursively update menu items structure
     */
    private function updateMenuItems($items, $parentId = null, $location = 'main')
    {
        foreach ($items as $index => $item) {
            $menuItem = MenuItem::find($item['id']);
            if ($menuItem) {
                $menuItem->update([
                    'parent_id' => $parentId,
                    'sort_order' => $index,
                    'menu_location' => $location,
                ]);

                // Update children if they exist
                if (isset($item['children']) && is_array($item['children'])) {
                    $this->updateMenuItems($item['children'], $menuItem->id, $location);
                }
            }
        }
    }

    /**
     * Get available routes for dropdown
     */
    private function getAvailableRoutes()
    {
        $routes = [];
        $routeCollection = Route::getRoutes();

        foreach ($routeCollection as $route) {
            $name = $route->getName();
            if ($name && !str_starts_with($name, 'admin.') && !str_starts_with($name, 'api.')) {
                $routes[$name] = $name;
            }
        }

        // Add some common custom routes
        $commonRoutes = [
            'home' => 'Home',
            'about' => 'About',
            'contact' => 'Contact',
            'services.index' => 'Services',
            'projects.index' => 'Projects',
            'events.index' => 'Events',
            'gallery.index' => 'Gallery',
        ];

        return array_merge($commonRoutes, $routes);
    }

    /**
     * Toggle menu item active status
     */
    public function toggleActive(MenuItem $menuItem)
    {
        $menuItem->update(['is_active' => !$menuItem->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Menu item status updated.',
            'is_active' => $menuItem->is_active
        ]);
    }

    /**
     * Duplicate a menu item
     */
    public function duplicate(MenuItem $menuItem)
    {
        $newItem = $menuItem->replicate();
        $newItem->title = $menuItem->title . ' (Copy)';
        $newItem->sort_order = MenuItem::where('menu_location', $menuItem->menu_location)
            ->where('parent_id', $menuItem->parent_id)
            ->max('sort_order') + 1;
        $newItem->save();

        return response()->json([
            'success' => true,
            'message' => 'Menu item duplicated successfully.',
            'item' => $newItem->load('children')
        ]);
    }
}
