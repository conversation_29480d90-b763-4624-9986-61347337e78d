@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Edit Subscriber: {{ $newsletterSubscriber->email }}</h1>
            <div class="flex space-x-2">
                <a href="{{ route('admin.newsletter-subscribers.show', $newsletterSubscriber) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    View Subscriber
                </a>
                <a href="{{ route('admin.newsletter-subscribers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Subscribers
                </a>
            </div>
        </div>

        <form action="{{ route('admin.newsletter-subscribers.update', $newsletterSubscriber) }}" method="POST" class="max-w-2xl">
            @csrf
            @method('PUT')

            <div class="space-y-6">
                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input type="email" name="email" id="email" value="{{ old('email', $newsletterSubscriber->email) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name (Optional)</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $newsletterSubscriber->name) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Subscriber's full name">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subscription Source -->
                <div>
                    <label for="subscription_source" class="block text-sm font-medium text-gray-700 mb-2">Subscription Source</label>
                    <select name="subscription_source" id="subscription_source" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">Select Source</option>
                        <option value="website" {{ old('subscription_source', $newsletterSubscriber->subscription_source) == 'website' ? 'selected' : '' }}>Website Form</option>
                        <option value="popup" {{ old('subscription_source', $newsletterSubscriber->subscription_source) == 'popup' ? 'selected' : '' }}>Newsletter Popup</option>
                        <option value="manual" {{ old('subscription_source', $newsletterSubscriber->subscription_source) == 'manual' ? 'selected' : '' }}>Manual Entry</option>
                        <option value="import" {{ old('subscription_source', $newsletterSubscriber->subscription_source) == 'import' ? 'selected' : '' }}>CSV Import</option>
                        <option value="api" {{ old('subscription_source', $newsletterSubscriber->subscription_source) == 'api' ? 'selected' : '' }}>API</option>
                        <option value="other" {{ old('subscription_source', $newsletterSubscriber->subscription_source) == 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('subscription_source')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', $newsletterSubscriber->is_active) ? 'checked' : '' }}
                               class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Active Subscription</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500">Uncheck to deactivate this subscription</p>
                </div>

                <!-- Preview -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subscriber Preview</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                <span id="initial-preview">{{ $newsletterSubscriber->name ? substr($newsletterSubscriber->name, 0, 1) : substr($newsletterSubscriber->email, 0, 1) }}</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900" id="email-preview">{{ $newsletterSubscriber->email }}</div>
                                <div class="text-sm text-gray-500" id="name-preview">{{ $newsletterSubscriber->name ?: 'No name provided' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Info -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Information</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Subscribed:</span>
                                <div class="text-gray-900 font-medium">{{ $newsletterSubscriber->subscribed_at->format('M d, Y g:i A') }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Duration:</span>
                                <div class="text-gray-900 font-medium">{{ $newsletterSubscriber->subscribed_at->diffForHumans() }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Last Updated:</span>
                                <div class="text-gray-900 font-medium">{{ $newsletterSubscriber->updated_at->format('M d, Y g:i A') }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Current Status:</span>
                                <div class="text-gray-900 font-medium">
                                    @if($newsletterSubscriber->is_active)
                                        <span class="text-green-600">Active</span>
                                    @else
                                        <span class="text-gray-600">Inactive</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-4 pt-6">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                        Update Subscriber
                    </button>
                    <a href="{{ route('admin.newsletter-subscribers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Update preview
function updatePreview() {
    const email = document.getElementById('email').value || '{{ $newsletterSubscriber->email }}';
    const name = document.getElementById('name').value || 'No name provided';
    
    document.getElementById('email-preview').textContent = email;
    document.getElementById('name-preview').textContent = name;
    
    // Update initial
    const initial = name !== 'No name provided' ? name.charAt(0).toUpperCase() : email.charAt(0).toUpperCase();
    document.getElementById('initial-preview').textContent = initial;
}

document.getElementById('email').addEventListener('input', updatePreview);
document.getElementById('name').addEventListener('input', updatePreview);
</script>
@endsection
