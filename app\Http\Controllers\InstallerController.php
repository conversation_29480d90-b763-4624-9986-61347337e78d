<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Services\InstallerService;
use Exception;

class InstallerController extends Controller
{
    protected $installerService;

    public function __construct(InstallerService $installerService)
    {
        $this->installerService = $installerService;

        // Redirect if already installed
        if ($this->installerService->isInstalled()) {
            abort(404);
        }
    }

    /**
     * Show the installer welcome page
     */
    public function welcome()
    {
        return view('installer.welcome');
    }

    /**
     * Check system requirements
     */
    public function requirements()
    {
        $requirements = $this->installerService->checkRequirements();
        return view('installer.requirements', compact('requirements'));
    }

    /**
     * Check file permissions
     */
    public function permissions()
    {
        $permissions = $this->installerService->checkPermissions();
        return view('installer.permissions', compact('permissions'));
    }

    /**
     * Show environment configuration form
     */
    public function environment()
    {
        return view('installer.environment');
    }

    /**
     * Save environment configuration
     */
    public function environmentSave(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'db_host' => 'required|string',
            'db_port' => 'required|integer',
            'db_database' => 'required|string',
            'db_username' => 'required|string',
            'db_password' => 'nullable|string',
            'mail_driver' => 'required|string',
            'mail_host' => 'nullable|string',
            'mail_port' => 'nullable|integer',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $this->installerService->saveEnvironmentFile($request->all());
            return redirect()->route('installer.database');
        } catch (Exception $e) {
            return back()->withErrors(['error' => 'Failed to save environment file: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Show database configuration and run migrations
     */
    public function database()
    {
        return view('installer.database');
    }

    /**
     * Run database migrations and seeders
     */
    public function databaseInstall(Request $request)
    {
        try {
            // Test database connection
            DB::connection()->getPdo();

            // Run migrations
            Artisan::call('migrate', ['--force' => true]);

            // Run seeders
            Artisan::call('db:seed', ['--force' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Database installed successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Database installation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show admin user creation form
     */
    public function admin()
    {
        return view('installer.admin');
    }

    /**
     * Create admin user
     */
    public function adminSave(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);

            return redirect()->route('installer.license');
        } catch (Exception $e) {
            return back()->withErrors(['error' => 'Failed to create admin user: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Show license activation form
     */
    public function license()
    {
        return view('installer.license');
    }

    /**
     * Validate and activate license
     */
    public function licenseSave(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'license_key' => 'required|string',
            'purchase_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $licenseValid = $this->installerService->validateLicense(
                $request->license_key,
                $request->purchase_code
            );

            if (!$licenseValid) {
                return back()->withErrors(['license_key' => 'Invalid license key or purchase code.'])->withInput();
            }

            return redirect()->route('installer.final');
        } catch (Exception $e) {
            return back()->withErrors(['error' => 'License validation failed: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Show final installation step
     */
    public function final()
    {
        return view('installer.final');
    }

    /**
     * Complete installation
     */
    public function complete(Request $request)
    {
        try {
            // Mark installation as complete
            $this->installerService->markAsInstalled();

            // Clear caches
            Artisan::call('config:cache');
            Artisan::call('route:cache');
            Artisan::call('view:cache');

            return response()->json([
                'success' => true,
                'message' => 'Installation completed successfully!',
                'redirect' => route('admin.dashboard')
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Installation completion failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
