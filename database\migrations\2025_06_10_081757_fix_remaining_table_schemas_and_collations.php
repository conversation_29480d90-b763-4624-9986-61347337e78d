<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix database collation to use compatible version
        $databaseName = config('database.connections.mysql.database');
        DB::statement("ALTER DATABASE `{$databaseName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

        // Fix cache table structure
        $this->fixCacheTable();

        // Fix cache_locks table structure
        $this->fixCacheLocksTable();

        // Fix sessions table structure
        $this->fixSessionsTable();

        // Fix other system tables
        $this->fixJobsTable();
        $this->fixJobBatchesTable();
        $this->fixFailedJobsTable();
    }

    private function fixCacheTable()
    {
        if (Schema::hasTable('cache')) {
            Schema::drop('cache');
        }

        Schema::create('cache', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->mediumText('value');
            $table->integer('expiration');
        });
    }

    private function fixCacheLocksTable()
    {
        if (Schema::hasTable('cache_locks')) {
            Schema::drop('cache_locks');
        }

        Schema::create('cache_locks', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->string('owner');
            $table->integer('expiration');
        });
    }

    private function fixSessionsTable()
    {
        if (Schema::hasTable('sessions')) {
            Schema::drop('sessions');
        }

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    private function fixJobsTable()
    {
        if (Schema::hasTable('jobs')) {
            Schema::drop('jobs');
        }

        Schema::create('jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
        });
    }

    private function fixJobBatchesTable()
    {
        if (Schema::hasTable('job_batches')) {
            Schema::drop('job_batches');
        }

        Schema::create('job_batches', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->integer('total_jobs');
            $table->integer('pending_jobs');
            $table->integer('failed_jobs');
            $table->longText('failed_job_ids');
            $table->mediumText('options')->nullable();
            $table->integer('cancelled_at')->nullable();
            $table->integer('created_at');
            $table->integer('finished_at')->nullable();
        });
    }

    private function fixFailedJobsTable()
    {
        if (Schema::hasTable('failed_jobs')) {
            Schema::drop('failed_jobs');
        }

        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
