<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PartnerController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\TeamController;
use Illuminate\Support\Facades\Route;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [PageController::class, 'show'])->defaults('slug', 'about')->name('about');
Route::get('/contact', [PageController::class, 'show'])->defaults('slug', 'contact')->name('contact');

// Events Routes
Route::get('/events', [\App\Http\Controllers\EventController::class, 'index'])->name('events.index');
Route::get('/events/upcoming', [\App\Http\Controllers\EventController::class, 'upcoming'])->name('events.upcoming');
Route::get('/events/past', [\App\Http\Controllers\EventController::class, 'past'])->name('events.past');
Route::get('/events/featured', [\App\Http\Controllers\EventController::class, 'featured'])->name('events.featured');
Route::get('/events/search', [\App\Http\Controllers\EventController::class, 'search'])->name('events.search');
Route::get('/events/{event}', [\App\Http\Controllers\EventController::class, 'show'])->name('events.show');

// Gallery Routes
Route::get('/gallery', [\App\Http\Controllers\GalleryController::class, 'index'])->name('gallery.index');
Route::get('/gallery/featured', [\App\Http\Controllers\GalleryController::class, 'featured'])->name('gallery.featured');
Route::get('/gallery/category/{category}', [\App\Http\Controllers\GalleryController::class, 'category'])->name('gallery.category');
Route::get('/gallery/search', [\App\Http\Controllers\GalleryController::class, 'search'])->name('gallery.search');
Route::get('/gallery/{gallery}/images', [\App\Http\Controllers\GalleryController::class, 'images'])->name('gallery.images');
Route::get('/gallery/{gallery}', [\App\Http\Controllers\GalleryController::class, 'show'])->name('gallery.show');

// Services Routes
Route::get('/services', [ServiceController::class, 'index'])->name('services.index');
Route::get('/services/{service}', [ServiceController::class, 'show'])->name('services.show');

// Projects Routes
Route::get('/projects', [ProjectController::class, 'index'])->name('projects.index');
Route::get('/projects/{project}', [ProjectController::class, 'show'])->name('projects.show');

// Team Routes
Route::get('/team', [TeamController::class, 'index'])->name('team.index');
Route::get('/team/executive', [TeamController::class, 'executive'])->name('team.executive');
Route::get('/team/project', [TeamController::class, 'project'])->name('team.project');
Route::get('/team/{team}', [TeamController::class, 'show'])->name('team.show');

// Partner and Client Routes
Route::get('/partners', [PartnerController::class, 'partners'])->name('partners.index');
Route::get('/clients', [PartnerController::class, 'clients'])->name('clients.index');
Route::get('/partners/{partner}', [PartnerController::class, 'showPartner'])->name('partners.show');
Route::get('/clients/{client}', [PartnerController::class, 'showClient'])->name('clients.show');

// Blog Routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{post}', [BlogController::class, 'show'])->name('blog.show');
Route::get('/blog/category/{category}', [BlogController::class, 'category'])->name('blog.category');
Route::get('/blog/tag/{tag}', [BlogController::class, 'tag'])->name('blog.tag');

// FAQ Routes
Route::get('/faqs', [\App\Http\Controllers\FaqController::class, 'index'])->name('faqs.index');

// Testimonial Routes
Route::get('/testimonials', [\App\Http\Controllers\TestimonialController::class, 'index'])->name('testimonials.index');
Route::get('/testimonials/create', [\App\Http\Controllers\TestimonialController::class, 'create'])->name('testimonials.create');
Route::post('/testimonials', [\App\Http\Controllers\TestimonialController::class, 'store'])->name('testimonials.store');

// User Dashboard (from Breeze)
Route::get('/dashboard', function () {
    return view('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Image Upload for TinyMCE
    Route::post('/upload-image', [\App\Http\Controllers\Admin\ImageUploadController::class, 'upload'])->name('upload-image');

    // Pages Management
    Route::resource('pages', \App\Http\Controllers\Admin\PageController::class);
    Route::delete('pages/{page}/remove-image', [\App\Http\Controllers\Admin\PageController::class, 'removeImage'])->name('pages.remove-image');

    // Services Management
    Route::resource('services', \App\Http\Controllers\Admin\ServiceController::class);
    Route::delete('services/{service}/remove-image', [\App\Http\Controllers\Admin\ServiceController::class, 'removeImage'])->name('services.remove-image');

    // Events Management
    Route::resource('events', \App\Http\Controllers\Admin\EventController::class);
    Route::delete('events/{event}/remove-image', [\App\Http\Controllers\Admin\EventController::class, 'removeImage'])->name('events.remove-image');
    Route::post('events/bulk-action', [\App\Http\Controllers\Admin\EventController::class, 'bulkAction'])->name('events.bulk-action');

    // Gallery Management
    Route::resource('galleries', \App\Http\Controllers\Admin\GalleryController::class);
    Route::delete('galleries/{gallery}/remove-cover-image', [\App\Http\Controllers\Admin\GalleryController::class, 'removeCoverImage'])->name('galleries.remove-cover-image');
    Route::post('galleries/bulk-action', [\App\Http\Controllers\Admin\GalleryController::class, 'bulkAction'])->name('galleries.bulk-action');

    // Gallery Images Management
    Route::put('gallery-images/{galleryImage}', [\App\Http\Controllers\Admin\GalleryImageController::class, 'update'])->name('gallery-images.update');
    Route::delete('gallery-images/{galleryImage}', [\App\Http\Controllers\Admin\GalleryImageController::class, 'destroy'])->name('gallery-images.destroy');
    Route::post('galleries/{gallery}/images/update-order', [\App\Http\Controllers\Admin\GalleryImageController::class, 'updateOrder'])->name('gallery-images.update-order');
    Route::post('galleries/{gallery}/images/{galleryImage}/set-cover', [\App\Http\Controllers\Admin\GalleryImageController::class, 'setCover'])->name('gallery-images.set-cover');
    Route::post('galleries/{gallery}/images/bulk-action', [\App\Http\Controllers\Admin\GalleryImageController::class, 'bulkAction'])->name('gallery-images.bulk-action');

    // Menu Management
    Route::get('menus', [\App\Http\Controllers\Admin\MenuController::class, 'index'])->name('menus.index');
    Route::post('menus', [\App\Http\Controllers\Admin\MenuController::class, 'store'])->name('menus.store');
    Route::put('menus/{menuItem}', [\App\Http\Controllers\Admin\MenuController::class, 'update'])->name('menus.update');
    Route::delete('menus/{menuItem}', [\App\Http\Controllers\Admin\MenuController::class, 'destroy'])->name('menus.destroy');
    Route::post('menus/update-structure', [\App\Http\Controllers\Admin\MenuController::class, 'updateStructure'])->name('menus.update-structure');
    Route::post('menus/{menuItem}/toggle-active', [\App\Http\Controllers\Admin\MenuController::class, 'toggleActive'])->name('menus.toggle-active');
    Route::post('menus/{menuItem}/duplicate', [\App\Http\Controllers\Admin\MenuController::class, 'duplicate'])->name('menus.duplicate');

    // Blog Management
    Route::resource('blog-posts', \App\Http\Controllers\Admin\BlogPostController::class);
    Route::resource('categories', \App\Http\Controllers\Admin\CategoryController::class);
    Route::resource('tags', \App\Http\Controllers\Admin\TagController::class);

    // Newsletter Management
    Route::resource('newsletter-subscribers', \App\Http\Controllers\Admin\NewsletterSubscriberController::class);
    Route::get('newsletter-subscribers-export', [\App\Http\Controllers\Admin\NewsletterSubscriberController::class, 'export'])->name('newsletter-subscribers.export');

    // Popup Management
    Route::resource('popups', \App\Http\Controllers\Admin\PopupController::class);
    Route::patch('popups/{popup}/toggle-status', [\App\Http\Controllers\Admin\PopupController::class, 'toggleStatus'])->name('popups.toggle-status');

    // Contact Enquiries
    Route::resource('contact-enquiries', \App\Http\Controllers\Admin\ContactEnquiryController::class);
    Route::patch('contact-enquiries/{contact_enquiry}/mark-read', [\App\Http\Controllers\Admin\ContactEnquiryController::class, 'markAsRead'])->name('contact-enquiries.mark-read');
    Route::post('contact-enquiries/bulk-action', [\App\Http\Controllers\Admin\ContactEnquiryController::class, 'bulkAction'])->name('contact-enquiries.bulk-action');

    // User Management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
    Route::patch('users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Settings Management
    Route::get('settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::put('settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
    Route::post('settings/test-email', [\App\Http\Controllers\Admin\SettingsController::class, 'testEmail'])->name('settings.test-email');
    Route::get('settings/clear-cache', [\App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::get('settings/export', [\App\Http\Controllers\Admin\SettingsController::class, 'export'])->name('settings.export');

    // Team Management
    Route::resource('teams', \App\Http\Controllers\Admin\TeamController::class);
    Route::patch('teams/{team}/toggle-status', [\App\Http\Controllers\Admin\TeamController::class, 'toggleStatus'])->name('teams.toggle-status');

    // Project Management
    Route::resource('projects', \App\Http\Controllers\Admin\ProjectController::class);
    Route::patch('projects/{project}/toggle-status', [\App\Http\Controllers\Admin\ProjectController::class, 'toggleStatus'])->name('projects.toggle-status');
    Route::patch('projects/{project}/toggle-featured', [\App\Http\Controllers\Admin\ProjectController::class, 'toggleFeatured'])->name('projects.toggle-featured');

    // Partner Management
    Route::resource('partners', \App\Http\Controllers\Admin\PartnerController::class);
    Route::patch('partners/{partner}/toggle-status', [\App\Http\Controllers\Admin\PartnerController::class, 'toggleStatus'])->name('partners.toggle-status');
    Route::patch('partners/{partner}/toggle-featured', [\App\Http\Controllers\Admin\PartnerController::class, 'toggleFeatured'])->name('partners.toggle-featured');

    // Testimonial Management
    Route::resource('testimonials', \App\Http\Controllers\Admin\TestimonialController::class);
    Route::patch('testimonials/{testimonial}/toggle-status', [\App\Http\Controllers\Admin\TestimonialController::class, 'toggleStatus'])->name('testimonials.toggle-status');
    Route::patch('testimonials/{testimonial}/toggle-featured', [\App\Http\Controllers\Admin\TestimonialController::class, 'toggleFeatured'])->name('testimonials.toggle-featured');
    Route::patch('testimonials/{testimonial}/toggle-approval', [\App\Http\Controllers\Admin\TestimonialController::class, 'toggleApproval'])->name('testimonials.toggle-approval');

    // FAQ Management
    Route::resource('faqs', \App\Http\Controllers\Admin\FaqController::class);
    Route::patch('faqs/{faq}/toggle-status', [\App\Http\Controllers\Admin\FaqController::class, 'toggleStatus'])->name('faqs.toggle-status');
});

require __DIR__.'/auth.php';

// Dynamic Pages (must be last)
Route::get('/{page}', [PageController::class, 'show'])
    ->where('page', '^(?!login|register|password|dashboard|admin).*$')
    ->name('pages.show');
