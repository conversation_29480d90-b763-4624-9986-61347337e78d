@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-3">
                <h1 class="text-2xl font-bold text-gray-900">FAQ Details</h1>
                @if($faq->is_active)
                    <span class="bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded">Active</span>
                @else
                    <span class="bg-red-100 text-red-800 text-sm font-semibold px-3 py-1 rounded">Inactive</span>
                @endif
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('admin.faqs.edit', $faq) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit FAQ
                </a>
                <a href="{{ route('admin.faqs.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to FAQs
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Question -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Question</h3>
                    <p class="text-gray-700 text-lg">{{ $faq->question }}</p>
                </div>

                <!-- Answer -->
                <div class="prose prose-lg max-w-none">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Answer</h3>
                    <div class="text-gray-700">
                        {!! $faq->answer !!}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">FAQ Information</h3>
                    
                    <div class="space-y-4">
                        <!-- Service -->
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Related Service</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                @if($faq->service)
                                    <a href="{{ route('admin.services.show', $faq->service) }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $faq->service->title }}
                                    </a>
                                @else
                                    <span class="text-gray-500">General FAQ</span>
                                @endif
                            </dd>
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Sort Order</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $faq->sort_order }}</dd>
                        </div>

                        <!-- Status -->
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="mt-1">
                                @if($faq->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                @endif
                            </dd>
                        </div>

                        <!-- Created Date -->
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $faq->created_at->format('M d, Y \a\t g:i A') }}</dd>
                        </div>

                        <!-- Updated Date -->
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $faq->updated_at->format('M d, Y \a\t g:i A') }}</dd>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Actions</h4>
                        <div class="space-y-2">
                            <form action="{{ route('admin.faqs.toggle-status', $faq) }}" method="POST" class="w-full">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="w-full text-left px-3 py-2 text-sm text-yellow-700 hover:bg-yellow-50 rounded">
                                    {{ $faq->is_active ? 'Deactivate FAQ' : 'Activate FAQ' }}
                                </button>
                            </form>
                            
                            <form action="{{ route('admin.faqs.destroy', $faq) }}" method="POST" class="w-full" 
                                  onsubmit="return confirm('Are you sure you want to delete this FAQ? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full text-left px-3 py-2 text-sm text-red-700 hover:bg-red-50 rounded">
                                    Delete FAQ
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
