@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Events Management</h1>
        <a href="{{ route('admin.events.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Event
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.events.index') }}" class="row g-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search" placeholder="Search events..." value="{{ request('search') }}">
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_from" placeholder="From Date" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_to" placeholder="To Date" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                    <a href="{{ route('admin.events.index') }}" class="btn btn-outline-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Events Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Events List</h6>
        </div>
        <div class="card-body">
            @if($events->count() > 0)
                <!-- Bulk Actions -->
                <form id="bulk-action-form" method="POST" action="{{ route('admin.events.bulk-action') }}">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <select name="action" class="form-control" required>
                                    <option value="">Select Action</option>
                                    <option value="publish">Publish</option>
                                    <option value="unpublish">Unpublish</option>
                                    <option value="feature">Feature</option>
                                    <option value="unfeature">Unfeature</option>
                                    <option value="delete">Delete</option>
                                </select>
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th width="80">Image</th>
                                    <th>Title</th>
                                    <th>Date</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Featured</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($events as $event)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="events[]" value="{{ $event->id }}" class="event-checkbox">
                                    </td>
                                    <td>
                                        @if($event->featured_image)
                                            <img src="{{ asset('storage/' . $event->featured_image) }}" alt="{{ $event->title }}" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                <i class="fas fa-calendar text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $event->title }}</strong>
                                        @if($event->description)
                                            <br><small class="text-muted">{{ Str::limit($event->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $event->formatted_event_date }}</small>
                                        @if($event->event_end_date)
                                            <br><small class="text-muted">to {{ $event->event_end_date->format('M d, Y g:i A') }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $event->location ?? 'N/A' }}</td>
                                    <td>
                                        @if($event->status == 'published')
                                            <span class="badge badge-success">Published</span>
                                        @elseif($event->status == 'draft')
                                            <span class="badge badge-warning">Draft</span>
                                        @else
                                            <span class="badge badge-danger">Cancelled</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($event->is_featured)
                                            <span class="badge badge-primary">Featured</span>
                                        @else
                                            <span class="badge badge-secondary">Regular</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.events.show', $event) }}" class="btn btn-info btn-sm" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.events.edit', $event) }}" class="btn btn-warning btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="{{ route('admin.events.destroy', $event) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this event?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </form>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $events->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No events found</h5>
                    <p class="text-muted">Start by creating your first event.</p>
                    <a href="{{ route('admin.events.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Event
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const eventCheckboxes = document.querySelectorAll('.event-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
        eventCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Bulk action form validation
    document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.event-checkbox:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one event.');
            return false;
        }
        
        const action = document.querySelector('select[name="action"]').value;
        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected events?')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
@endpush
@endsection
