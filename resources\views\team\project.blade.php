@extends('layouts.public')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Project Management Team</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Meet our skilled project managers and specialists who ensure seamless execution, timely delivery, and exceptional quality in every project we undertake.
            </p>
        </div>

        <!-- Team Navigation -->
        <div class="flex justify-center space-x-4 mb-8" data-aos="fade-up" data-aos-delay="200">
            <a href="{{ route('team.executive') }}" class="bg-white text-green-600 border-2 border-green-600 px-6 py-3 rounded-full font-semibold hover:bg-green-600 hover:text-white transition-all duration-300">
                Executive Team
            </a>
            <a href="{{ route('team.project') }}" class="bg-green-600 text-white px-6 py-3 rounded-full font-semibold">
                Project Team
            </a>
            <a href="{{ route('team.index') }}" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-full font-semibold hover:bg-gray-200 transition-all duration-300">
                All Team
            </a>
        </div>
    </div>
</section>

<!-- Project Team Section -->
@if($projectTeam->count() > 0)
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($projectTeam as $index => $member)
                <div class="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($member->image_url)
                        <img src="{{ $member->image_url }}" alt="{{ $member->name }}" class="w-24 h-24 rounded-full mx-auto mb-4 object-cover shadow-lg">
                    @else
                        <div class="w-24 h-24 rounded-full mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                            <span class="text-xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                        </div>
                    @endif
                    
                    <h3 class="text-lg font-bold text-gray-900 mb-2">{{ $member->name }}</h3>
                    <p class="text-green-600 font-semibold text-sm mb-4">{{ $member->position }}</p>
                    
                    @if($member->linkedin || $member->twitter || $member->email)
                        <div class="flex justify-center space-x-3 mb-4">
                            @if($member->email)
                                <a href="mailto:{{ $member->email }}" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            @endif
                            @if($member->linkedin)
                                <a href="{{ $member->linkedin }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                            @endif
                            @if($member->twitter)
                                <a href="{{ $member->twitter }}" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            @endif
                        </div>
                    @endif
                    
                    <a href="{{ route('team.show', $member) }}" class="inline-block bg-green-600 text-white px-3 py-1 rounded-full text-xs font-semibold hover:bg-green-700 transition-colors duration-300">
                        View Profile
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@else
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-2xl font-bold text-gray-900 mb-2">No Project Team Members</h3>
        <p class="text-gray-600">Project team information will be available soon.</p>
    </div>
</section>
@endif

<!-- Project Management Approach -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Project Management Approach</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We follow proven methodologies and best practices to ensure project success from initiation to completion.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-search text-2xl text-green-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Discovery</h3>
                <p class="text-gray-600">Understanding requirements, goals, and constraints to define project scope.</p>
            </div>

            <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-drafting-compass text-2xl text-green-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Planning</h3>
                <p class="text-gray-600">Creating detailed project plans, timelines, and resource allocation strategies.</p>
            </div>

            <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-cogs text-2xl text-green-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Execution</h3>
                <p class="text-gray-600">Implementing solutions with continuous monitoring and quality assurance.</p>
            </div>

            <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check-circle text-2xl text-green-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Delivery</h3>
                <p class="text-gray-600">Ensuring successful project completion and ongoing support.</p>
            </div>
        </div>
    </div>
</section>

<!-- Skills & Expertise -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Team Expertise</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our project team brings diverse skills and certifications to handle projects of any complexity.
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            @php
                $skills = [
                    ['name' => 'Agile/Scrum', 'icon' => 'fas fa-sync-alt'],
                    ['name' => 'PMP Certified', 'icon' => 'fas fa-certificate'],
                    ['name' => 'Risk Management', 'icon' => 'fas fa-shield-alt'],
                    ['name' => 'Quality Assurance', 'icon' => 'fas fa-check-double'],
                    ['name' => 'Stakeholder Management', 'icon' => 'fas fa-users'],
                    ['name' => 'Budget Control', 'icon' => 'fas fa-chart-line']
                ];
            @endphp

            @foreach($skills as $index => $skill)
                <div class="text-center" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="{{ $skill['icon'] }} text-green-600"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-900">{{ $skill['name'] }}</h4>
                </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-20 bg-green-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div data-aos="fade-up">
            <h2 class="text-4xl font-bold text-white mb-4">Start Your Next Project</h2>
            <p class="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
                Our project management team is ready to help you bring your ideas to life. Let's discuss your project requirements and create a roadmap for success.
            </p>
            <a href="{{ route('contact') }}" class="bg-white text-green-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 hover-scale shadow-lg">
                Discuss Your Project
            </a>
        </div>
    </div>
</section>
@endsection

@push('styles')
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
@endpush

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
@endpush
