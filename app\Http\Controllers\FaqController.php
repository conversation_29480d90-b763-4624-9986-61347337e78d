<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use App\Models\Service;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    public function index()
    {
        $generalFaqs = Faq::active()->general()->ordered()->get();
        $serviceFaqs = Faq::active()->with('service')->whereNotNull('service_id')->ordered()->get()->groupBy('service.title');
        
        return view('faqs.index', compact('generalFaqs', 'serviceFaqs'));
    }
}
