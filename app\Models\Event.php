<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'content',
        'event_date',
        'event_end_date',
        'location',
        'featured_image',
        'status',
        'is_featured',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'event_url',
        'price',
        'max_attendees',
        'current_attendees',
        'organizer_info',
    ];

    protected $casts = [
        'event_date' => 'datetime',
        'event_end_date' => 'datetime',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
        'organizer_info' => 'array',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
        });

        static::updating(function ($event) {
            if ($event->isDirty('title') && empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
        });

        static::deleting(function ($event) {
            // Delete featured image when event is deleted
            if ($event->featured_image) {
                Storage::disk('public')->delete($event->featured_image);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('event_date', '>=', now());
    }

    public function scopePast($query)
    {
        return $query->where('event_date', '<', now());
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('event_date', 'asc');
    }

    // Accessors
    public function getFormattedEventDateAttribute()
    {
        return $this->event_date->format('M d, Y \a\t g:i A');
    }

    public function getFormattedEventDateShortAttribute()
    {
        return $this->event_date->format('M d, Y');
    }

    public function getIsUpcomingAttribute()
    {
        return $this->event_date >= now();
    }

    public function getIsPastAttribute()
    {
        return $this->event_date < now();
    }

    public function getHasEndDateAttribute()
    {
        return !is_null($this->event_end_date);
    }

    public function getDurationAttribute()
    {
        if (!$this->event_end_date) {
            return null;
        }

        return $this->event_date->diffForHumans($this->event_end_date, true);
    }

    public function getAvailableSpotsAttribute()
    {
        if (!$this->max_attendees) {
            return null;
        }

        return $this->max_attendees - $this->current_attendees;
    }

    public function getIsFullAttribute()
    {
        if (!$this->max_attendees) {
            return false;
        }

        return $this->current_attendees >= $this->max_attendees;
    }

    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return asset('storage/' . $this->featured_image);
        }

        return asset('images/default-event.jpg'); // Default event image
    }
}
