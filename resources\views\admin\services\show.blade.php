@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-3">
                @if($service->icon)
                    <div class="text-3xl">{{ $service->icon }}</div>
                @endif
                <h1 class="text-2xl font-bold text-gray-900">{{ $service->title }}</h1>
            </div>
            <div class="flex space-x-2">
                @if($service->is_published)
                    <a href="{{ route('services.show', $service->slug) }}" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        View Live Service
                    </a>
                @endif
                <a href="{{ route('admin.services.edit', $service) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Service
                </a>
                <a href="{{ route('admin.services.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Services
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Featured Image -->
                @if($service->featured_image)
                    <div class="mb-6">
                        <img src="{{ asset('storage/' . $service->featured_image) }}" alt="{{ $service->title }}" class="w-full h-64 object-cover rounded-lg">
                    </div>
                @endif

                <!-- Description -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Description</h3>
                    <p class="text-gray-700">{{ $service->description }}</p>
                </div>

                <!-- Content -->
                <div class="prose prose-lg max-w-none">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Full Content</h3>
                    {!! $service->content !!}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Service Status -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Service Status</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Status:</span>
                            @if($service->is_published)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Published
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    Draft
                                </span>
                            @endif
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Featured:</span>
                            @if($service->is_featured)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Featured
                                </span>
                            @else
                                <span class="text-sm text-gray-500">No</span>
                            @endif
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Sort Order:</span>
                            <span class="text-sm text-gray-900">{{ $service->sort_order }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Slug:</span>
                            <span class="text-sm text-gray-900 font-mono">{{ $service->slug }}</span>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing</h3>
                    <div class="space-y-3">
                        @if($service->price)
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">{{ $service->formatted_price }}</div>
                                @if($service->price_type)
                                    <div class="text-sm text-gray-500">{{ ucfirst($service->price_type) }}</div>
                                @endif
                            </div>
                        @else
                            <div class="text-center">
                                <div class="text-lg text-gray-600">Contact for pricing</div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Service Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Service Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Created:</span>
                            <div class="text-gray-900">{{ $service->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $service->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @if($service->is_published)
                            <div>
                                <span class="text-gray-600">URL:</span>
                                <div class="text-blue-600 hover:text-blue-800">
                                    <a href="{{ route('services.show', $service->slug) }}" target="_blank">/services/{{ $service->slug }}</a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- SEO Information -->
                @if($service->meta_title || $service->meta_description)
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Information</h3>
                    <div class="space-y-3 text-sm">
                        @if($service->meta_title)
                        <div>
                            <span class="text-gray-600">Meta Title:</span>
                            <div class="text-gray-900">{{ $service->meta_title }}</div>
                        </div>
                        @endif
                        
                        @if($service->meta_description)
                        <div>
                            <span class="text-gray-600">Meta Description:</span>
                            <div class="text-gray-900">{{ $service->meta_description }}</div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.services.edit', $service) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Service
                        </a>
                        
                        <form action="{{ route('admin.services.destroy', $service) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Delete Service
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
