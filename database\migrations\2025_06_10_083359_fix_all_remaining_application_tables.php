<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Fix all remaining application tables (recreate without data restoration)
        $this->recreateContactEnquiriesTable();
        $this->recreateGeneralSettingsTable();
        $this->recreateNewsletterSubscribersTable();
        $this->recreatePagesTable();
        $this->recreatePasswordResetTokensTable();
        $this->recreatePopupsTable();
        $this->recreateUsersTable();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    private function recreateContactEnquiriesTable()
    {
        // Drop if exists
        if (Schema::hasTable('contact_enquiries')) {
            Schema::drop('contact_enquiries');
        }

        // Create with proper structure
        Schema::create('contact_enquiries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->string('subject');
            $table->text('message');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('source_page')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['is_read']);
            $table->index(['created_at']);
        });
    }

    private function recreateGeneralSettingsTable()
    {
        // Drop if exists
        if (Schema::hasTable('general_settings')) {
            Schema::drop('general_settings');
        }

        // Create with proper structure
        Schema::create('general_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->longText('value')->nullable();
            $table->string('type')->default('text'); // text, textarea, image, boolean, etc.
            $table->string('group')->default('general'); // general, contact, social, seo, etc.
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['key']);
            $table->index(['group']);
        });
    }

    private function recreateNewsletterSubscribersTable()
    {
        // Drop if exists
        if (Schema::hasTable('newsletter_subscribers')) {
            Schema::drop('newsletter_subscribers');
        }

        // Create with proper structure
        Schema::create('newsletter_subscribers', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->string('name')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('subscribed_at')->nullable();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->string('source')->nullable(); // popup, footer, contact_form, etc.
            $table->timestamps();

            // Indexes
            $table->index(['is_active']);
            $table->index(['subscribed_at']);
        });
    }

    private function recreatePagesTable()
    {
        // Drop if exists
        if (Schema::hasTable('pages')) {
            Schema::drop('pages');
        }

        // Create with proper structure
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->longText('content');
            $table->text('excerpt')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('featured_image')->nullable();
            $table->boolean('is_published')->default(true);
            $table->boolean('show_in_menu')->default(false);
            $table->integer('sort_order')->default(0);
            $table->string('template')->default('default'); // default, custom, etc.
            $table->timestamps();

            // Indexes
            $table->index(['is_published']);
            $table->index(['show_in_menu']);
            $table->index(['slug']);
        });
    }

    private function recreatePasswordResetTokensTable()
    {
        // Drop if exists
        if (Schema::hasTable('password_reset_tokens')) {
            Schema::drop('password_reset_tokens');
        }

        // Create with proper structure
        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });
    }

    private function recreatePopupsTable()
    {
        // Drop if exists
        if (Schema::hasTable('popups')) {
            Schema::drop('popups');
        }

        // Create with proper structure
        Schema::create('popups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type')->default('newsletter'); // newsletter, announcement, etc.
            $table->string('title');
            $table->text('content');
            $table->string('button_text')->default('Subscribe');
            $table->string('button_color')->default('#10B981');
            $table->boolean('is_active')->default(true);
            $table->timestamp('start_date')->nullable(); // When popup should start showing
            $table->timestamp('end_date')->nullable(); // When popup should stop showing
            $table->integer('delay_seconds')->default(5);
            $table->integer('display_frequency')->default(1); // 1=every visit, 7=weekly, etc.
            $table->json('display_pages')->nullable(); // which pages to show on
            $table->timestamps();

            // Indexes
            $table->index(['is_active']);
            $table->index(['type']);
            $table->index(['start_date']);
            $table->index(['end_date']);
        });
    }

    private function recreateUsersTable()
    {
        // Drop if exists
        if (Schema::hasTable('users')) {
            Schema::drop('users');
        }

        // Create with proper structure
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('role')->default('user'); // admin, editor, user
            $table->string('avatar')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip', 45)->nullable();
            $table->rememberToken();
            $table->timestamps();

            // Indexes
            $table->index(['email']);
            $table->index(['role']);
            $table->index(['is_active']);
        });

        // Create default admin user
        DB::table('users')->insert([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
            'role' => 'admin',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be easily reversed
        // You would need to restore from backup
    }
};
