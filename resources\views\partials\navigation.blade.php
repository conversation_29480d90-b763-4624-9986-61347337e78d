{{-- Main Navigation Menu --}}
@if(isset($mainMenu) && $mainMenu && $mainMenu->count() > 0)
    @foreach($mainMenu as $item)
        @if($item->children->count() > 0)
            {{-- Dropdown Menu --}}
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" @click.away="open = false"
                        class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold transition-colors duration-300 flex items-center">
                    @if($item->icon)
                        <i class="{{ $item->icon }} mr-2"></i>
                    @endif
                    {{ $item->title }}
                    <i class="fas fa-chevron-down ml-1 text-sm"></i>
                </button>

                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                    <div class="py-1" role="menu">
                        @foreach($item->children as $child)
                            @php
                                try {
                                    $childUrl = $child->full_url;
                                } catch (\Exception $e) {
                                    $childUrl = '#';
                                }
                            @endphp
                            <a href="{{ $childUrl }}" target="{{ $child->target ?? '_self' }}"
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition duration-150 ease-in-out {{ $child->css_class ?? '' }}"
                               role="menuitem">
                                @if($child->icon)
                                    <i class="{{ $child->icon }} mr-2"></i>
                                @endif
                                {{ $child->title }}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            {{-- Single Menu Item --}}
            @php
                try {
                    $itemUrl = $item->full_url;
                } catch (\Exception $e) {
                    $itemUrl = '#';
                }
            @endphp
            <a href="{{ $itemUrl }}" target="{{ $item->target ?? '_self' }}"
               class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold transition-colors duration-300 {{ $item->css_class ?? '' }}">
                @if($item->icon)
                    <i class="{{ $item->icon }} mr-2"></i>
                @endif
                {{ $item->title }}
            </a>
        @endif
    @endforeach
@else
    {{-- Fallback static menu if no dynamic menu items exist --}}
    <a href="{{ route('home') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('home') ? 'text-green-600' : '' }} transition-colors duration-300">
        Home
    </a>
    <a href="{{ route('about') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('about') ? 'text-green-600' : '' }} transition-colors duration-300">
        About
    </a>
    <a href="{{ route('services.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('services.*') ? 'text-green-600' : '' }} transition-colors duration-300">
        Services
    </a>
    <a href="{{ route('projects.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('projects.*') ? 'text-green-600' : '' }} transition-colors duration-300">
        Projects
    </a>
    <a href="{{ route('events.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('events.*') ? 'text-green-600' : '' }} transition-colors duration-300">
        Events
    </a>
    <a href="{{ route('gallery.index') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('gallery.*') ? 'text-green-600' : '' }} transition-colors duration-300">
        Gallery
    </a>
    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-green-600 px-3 py-2 rounded-md text-base font-semibold {{ request()->routeIs('contact') ? 'text-green-600' : '' }} transition-colors duration-300">
        Contact
    </a>
@endif
