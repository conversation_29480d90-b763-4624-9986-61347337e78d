<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Exception;

class TenantService
{
    protected $currentTenant;

    /**
     * Get the current tenant
     */
    public function getCurrentTenant(): ?Tenant
    {
        return $this->currentTenant;
    }

    /**
     * Set the current tenant
     */
    public function setCurrentTenant(?Tenant $tenant): void
    {
        $this->currentTenant = $tenant;
        
        if ($tenant) {
            // Set tenant context in config
            config(['app.current_tenant' => $tenant]);
            
            // Configure tenant-specific settings
            $this->configureTenantSettings($tenant);
        }
    }

    /**
     * Create a new tenant
     */
    public function createTenant(array $data): array
    {
        try {
            DB::beginTransaction();

            // Generate unique identifiers
            $slug = $this->generateUniqueSlug($data['name']);
            $databaseName = Tenant::generateDatabaseName($slug);
            $subdomain = $data['subdomain'] ?? Tenant::generateSubdomain($data['name']);

            // Create tenant record
            $tenant = Tenant::create([
                'name' => $data['name'],
                'slug' => $slug,
                'domain' => $data['domain'] ?? null,
                'subdomain' => $subdomain,
                'database_name' => $databaseName,
                'database_host' => $data['database_host'] ?? null,
                'database_username' => $data['database_username'] ?? null,
                'database_password' => $data['database_password'] ?? null,
                'config' => $data['config'] ?? [],
                'settings' => $data['settings'] ?? [],
                'trial_ends_at' => $data['trial_ends_at'] ?? now()->addDays(30),
                'plan_id' => $data['plan_id'] ?? null,
                'owner_id' => $data['owner_id'] ?? null,
                'created_by' => auth()->id(),
            ]);

            // Create tenant database
            if (!$tenant->createDatabase()) {
                throw new Exception('Failed to create tenant database');
            }

            // Run migrations
            if (!$tenant->runMigrations()) {
                throw new Exception('Failed to run tenant migrations');
            }

            // Seed database
            if (!$tenant->seedDatabase()) {
                throw new Exception('Failed to seed tenant database');
            }

            // Create storage directory
            if (!$tenant->createStorageDirectory()) {
                throw new Exception('Failed to create tenant storage directory');
            }

            // Create tenant admin user if provided
            if (isset($data['admin_user'])) {
                $this->createTenantAdminUser($tenant, $data['admin_user']);
            }

            DB::commit();

            return [
                'success' => true,
                'tenant' => $tenant,
                'message' => 'Tenant created successfully',
            ];

        } catch (Exception $e) {
            DB::rollBack();

            // Cleanup on failure
            if (isset($tenant)) {
                $this->cleanupFailedTenant($tenant);
            }

            return [
                'success' => false,
                'message' => 'Failed to create tenant: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Update tenant
     */
    public function updateTenant(Tenant $tenant, array $data): array
    {
        try {
            $tenant->update($data);

            return [
                'success' => true,
                'tenant' => $tenant,
                'message' => 'Tenant updated successfully',
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update tenant: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Delete tenant
     */
    public function deleteTenant(Tenant $tenant): array
    {
        try {
            $result = $tenant->deleteTenant();

            if ($result) {
                return [
                    'success' => true,
                    'message' => 'Tenant deleted successfully',
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to delete tenant',
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to delete tenant: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Suspend tenant
     */
    public function suspendTenant(Tenant $tenant, string $reason = null): array
    {
        try {
            $tenant->suspend($reason);

            return [
                'success' => true,
                'message' => 'Tenant suspended successfully',
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to suspend tenant: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Unsuspend tenant
     */
    public function unsuspendTenant(Tenant $tenant): array
    {
        try {
            $tenant->unsuspend();

            return [
                'success' => true,
                'message' => 'Tenant unsuspended successfully',
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to unsuspend tenant: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Switch to tenant context
     */
    public function switchToTenant(Tenant $tenant): void
    {
        $this->setCurrentTenant($tenant);
        $tenant->configureDatabaseConnection();
    }

    /**
     * Execute callback in tenant context
     */
    public function runInTenantContext(Tenant $tenant, callable $callback)
    {
        $originalTenant = $this->currentTenant;
        
        try {
            $this->switchToTenant($tenant);
            return $callback($tenant);
        } finally {
            $this->setCurrentTenant($originalTenant);
            
            if ($originalTenant) {
                $originalTenant->configureDatabaseConnection();
            }
        }
    }

    /**
     * Get tenant statistics
     */
    public function getTenantStatistics(): array
    {
        return [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('is_active', true)->count(),
            'suspended_tenants' => Tenant::where('is_suspended', true)->count(),
            'trial_tenants' => Tenant::whereNotNull('trial_ends_at')
                ->where('trial_ends_at', '>', now())
                ->count(),
            'expired_trials' => Tenant::whereNotNull('trial_ends_at')
                ->where('trial_ends_at', '<=', now())
                ->count(),
        ];
    }

    /**
     * Generate unique slug
     */
    protected function generateUniqueSlug(string $name): string
    {
        $slug = Str::slug($name);
        $counter = 1;
        
        while (Tenant::where('slug', $slug)->exists()) {
            $slug = Str::slug($name) . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Configure tenant-specific settings
     */
    protected function configureTenantSettings(Tenant $tenant): void
    {
        // Set tenant-specific configuration
        if ($tenant->config) {
            foreach ($tenant->config as $key => $value) {
                config([$key => $value]);
            }
        }

        // Set tenant-specific storage disk
        config([
            'filesystems.disks.tenant' => [
                'driver' => 'local',
                'root' => $tenant->getStoragePath(),
                'url' => $tenant->getStorageUrl(),
                'visibility' => 'public',
            ]
        ]);
    }

    /**
     * Create tenant admin user
     */
    protected function createTenantAdminUser(Tenant $tenant, array $userData): User
    {
        return $this->runInTenantContext($tenant, function () use ($userData) {
            return User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => bcrypt($userData['password']),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);
        });
    }

    /**
     * Cleanup failed tenant creation
     */
    protected function cleanupFailedTenant(Tenant $tenant): void
    {
        try {
            // Delete database if it was created
            if ($tenant->database_name) {
                $connection = config('database.default');
                $host = $tenant->database_host ?: config("database.connections.{$connection}.host");
                $username = $tenant->database_username ?: config("database.connections.{$connection}.username");
                $password = $tenant->database_password ?: config("database.connections.{$connection}.password");
                
                $pdo = new \PDO(
                    "mysql:host={$host}",
                    $username,
                    $password
                );
                
                $pdo->exec("DROP DATABASE IF EXISTS `{$tenant->database_name}`");
            }

            // Delete storage directory
            if ($tenant->storage_path && File::exists($tenant->storage_path)) {
                File::deleteDirectory($tenant->storage_path);
            }

            // Delete tenant record
            $tenant->delete();

        } catch (Exception $e) {
            \Log::error('Failed to cleanup failed tenant', [
                'tenant_id' => $tenant->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
