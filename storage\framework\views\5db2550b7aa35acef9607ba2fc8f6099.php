<?php $__env->startSection('content'); ?>
<!-- About Page Content -->
<?php if($page->slug === 'about'): ?>
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-green-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-16" data-aos="fade-up">
                <h1 class="text-center text-4xl md:text-6xl font-bold text-gray-900 mb-6"><?php echo e($page->title); ?></h1>
                <?php if($globalSettings->about_us): ?>
                    <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed text-justify">
                        <?php echo e($globalSettings->about_us); ?>

                    </p>
                <?php endif; ?>
            </div>

            <?php if($page->featured_image): ?>
                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <img src="<?php echo e(asset('storage/' . $page->featured_image)); ?>" alt="<?php echo e($page->title); ?>" class="w-full max-w-4xl mx-auto h-96 object-cover rounded-lg shadow-xl">
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <?php if($globalSettings->mission || $globalSettings->vision): ?>
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <?php if($globalSettings->mission): ?>
                <div class="text-center lg:text-left" data-aos="fade-right">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
                        <i class="fas fa-bullseye text-2xl text-green-600"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        <?php echo e($globalSettings->mission); ?>

                    </p>
                </div>
                <?php endif; ?>

                <?php if($globalSettings->vision): ?>
                <div class="text-center lg:text-left" data-aos="fade-left">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
                        <i class="fas fa-eye text-2xl text-green-600"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Our Vision</h2>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        <?php echo e($globalSettings->vision); ?>

                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Additional Page Content -->
    <?php if($page->content): ?>
    <section class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose prose-lg max-w-none" data-aos="fade-up">
                <?php echo $page->content; ?>

            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Team Section -->
    <?php if((isset($executiveTeam) && $executiveTeam->count() > 0) || (isset($projectTeam) && $projectTeam->count() > 0)): ?>
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our experienced professionals are dedicated to delivering exceptional results and driving your success.
                </p>
            </div>

            <!-- Executive Team -->
            <?php if(isset($executiveTeam) && $executiveTeam->count() > 0): ?>
            <div class="mb-20">
                <h3 class="text-3xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Executive Management</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php $__currentLoopData = $executiveTeam; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                            <?php if($member->image_url): ?>
                                <img src="<?php echo e($member->image_url); ?>" alt="<?php echo e($member->name); ?>" class="w-32 h-32 rounded-full mx-auto mb-6 object-cover shadow-lg">
                            <?php else: ?>
                                <div class="w-32 h-32 rounded-full mx-auto mb-6 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                    <span class="text-4xl font-bold text-white"><?php echo e(substr($member->name, 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>

                            <h4 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($member->name); ?></h4>
                            <p class="text-green-600 font-semibold mb-4"><?php echo e($member->position); ?></p>

                            <?php if($member->bio): ?>
                                <p class="text-gray-600 mb-6 leading-relaxed"><?php echo e($member->bio); ?></p>
                            <?php endif; ?>

                            <?php if($member->linkedin || $member->twitter || $member->email): ?>
                                <div class="flex justify-center space-x-4">
                                    <?php if($member->email): ?>
                                        <a href="mailto:<?php echo e($member->email); ?>" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fas fa-envelope text-lg"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if($member->linkedin): ?>
                                        <a href="<?php echo e($member->linkedin); ?>" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-linkedin text-lg"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if($member->twitter): ?>
                                        <a href="<?php echo e($member->twitter); ?>" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-twitter text-lg"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Project Team -->
            <?php if(isset($projectTeam) && $projectTeam->count() > 0): ?>
            <div>
                <h3 class="text-3xl font-bold text-gray-900 text-center mb-12" data-aos="fade-up">Project Management</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php $__currentLoopData = $projectTeam; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                            <?php if($member->image_url): ?>
                                <img src="<?php echo e($member->image_url); ?>" alt="<?php echo e($member->name); ?>" class="w-24 h-24 rounded-full mx-auto mb-4 object-cover shadow-lg">
                            <?php else: ?>
                                <div class="w-24 h-24 rounded-full mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                    <span class="text-xl font-bold text-white"><?php echo e(substr($member->name, 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>

                            <h4 class="text-lg font-bold text-gray-900 mb-2"><?php echo e($member->name); ?></h4>
                            <p class="text-green-600 font-semibold text-sm mb-4"><?php echo e($member->position); ?></p>

                            <?php if($member->linkedin || $member->twitter || $member->email): ?>
                                <div class="flex justify-center space-x-3">
                                    <?php if($member->email): ?>
                                        <a href="mailto:<?php echo e($member->email); ?>" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if($member->linkedin): ?>
                                        <a href="<?php echo e($member->linkedin); ?>" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-linkedin"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if($member->twitter): ?>
                                        <a href="<?php echo e($member->twitter); ?>" target="_blank" class="text-gray-400 hover:text-green-600 transition-colors duration-300">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>
    <?php endif; ?>

<?php else: ?>
    <!-- Default Page Layout -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4"><?php echo e($page->title); ?></h1>
        </div>

        <!-- Page Content -->
        <div class="prose prose-lg max-w-none">
            <?php if($page->featured_image): ?>
                <img src="<?php echo e(asset('storage/' . $page->featured_image)); ?>" alt="<?php echo e($page->title); ?>" class="w-full h-64 object-cover rounded-lg mb-8">
            <?php endif; ?>

            <?php echo $page->content; ?>

        </div>

        <!-- Contact Page Specific Content -->
        <?php if($page->slug === 'contact'): ?>
            <!-- Contact Information Section -->
            <div class="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Details -->
                <div class="space-y-8">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Get in Touch</h2>
                        <p class="text-lg text-gray-600 mb-8">
                            We'd love to hear from you. Send us a message and we'll respond as soon as possible.
                        </p>
                    </div>

                    <!-- Contact Information Cards -->
                    <div class="space-y-6">
                        <?php if($globalSettings->address): ?>
                            <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-map-marker-alt text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Our Address</h3>
                                    <p class="text-gray-700"><?php echo e($globalSettings->address); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($globalSettings->phone1 || $globalSettings->phone2): ?>
                            <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-phone text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Phone Numbers</h3>
                                    <div class="space-y-1">
                                        <?php if($globalSettings->phone1): ?>
                                            <p class="text-gray-700">
                                                <a href="tel:<?php echo e($globalSettings->phone1); ?>" class="hover:text-green-600 transition-colors duration-200">
                                                    <?php echo e($globalSettings->phone1); ?>

                                                </a>
                                            </p>
                                        <?php endif; ?>
                                        <?php if($globalSettings->phone2): ?>
                                            <p class="text-gray-700">
                                                <a href="tel:<?php echo e($globalSettings->phone2); ?>" class="hover:text-green-600 transition-colors duration-200">
                                                    <?php echo e($globalSettings->phone2); ?>

                                                </a>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($globalSettings->email1 || $globalSettings->email2): ?>
                            <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Addresses</h3>
                                    <div class="space-y-1">
                                        <?php if($globalSettings->email1): ?>
                                            <p class="text-gray-700">
                                                <a href="mailto:<?php echo e($globalSettings->email1); ?>" class="hover:text-green-600 transition-colors duration-200">
                                                    <?php echo e($globalSettings->email1); ?>

                                                </a>
                                            </p>
                                        <?php endif; ?>
                                        <?php if($globalSettings->email2): ?>
                                            <p class="text-gray-700">
                                                <a href="mailto:<?php echo e($globalSettings->email2); ?>" class="hover:text-green-600 transition-colors duration-200">
                                                    <?php echo e($globalSettings->email2); ?>

                                                </a>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Business Hours -->
                        <div class="flex items-start space-x-4 p-6 bg-green-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <i class="fas fa-clock text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Business Hours</h3>
                                <div class="space-y-1 text-gray-700">
                                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                                    <p>Saturday: 10:00 AM - 4:00 PM</p>
                                    <p>Sunday: Closed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Send us a Message</h2>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('contact-form');

$__html = app('livewire')->mount($__name, $__params, 'lw-1617108058-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>
            </div>

            <!-- Map Section -->
            <?php if($globalSettings->map): ?>
                <div class="mt-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Find Us</h2>
                    <div class="bg-gray-100 rounded-lg overflow-hidden shadow-lg">
                        <div class="aspect-w-16 aspect-h-9" style="height: 400px;">
                            <?php echo $globalSettings->map; ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php if($page->slug === 'about'): ?>
<?php $__env->startPush('styles'); ?>
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/pages/show.blade.php ENDPATH**/ ?>