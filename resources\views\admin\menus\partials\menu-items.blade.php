@foreach($items as $item)
    <div class="bg-white border border-gray-200 rounded-lg mb-2 p-3 cursor-move {{ !$item->is_active ? 'opacity-60' : '' }}" data-id="{{ $item->id }}">
        <div class="flex items-center justify-between">
            <div class="flex items-center flex-1">
                <div class="mr-3 text-gray-400">
                    ⋮⋮
                </div>

                @if($item->icon)
                    <i class="{{ $item->icon }} text-gray-600 mr-2"></i>
                @endif

                <div>
                    <div class="font-medium text-gray-900">{{ $item->title }}</div>
                    <div class="text-sm text-gray-500">
                        @if($item->route_name)
                            Route: {{ $item->route_name }}
                        @else
                            {{ $item->url ?: '#' }}
                        @endif

                        @if($item->target !== '_self')
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 ml-1">{{ $item->target }}</span>
                        @endif
                    </div>
                </div>
            </div>

            <div class="flex gap-1">
                <button type="button" class="text-blue-600 hover:text-blue-900 p-1" onclick="editMenuItem({{ $item->id }})" title="Edit">
                    ✏️
                </button>

                <button type="button" class="text-{{ $item->is_active ? 'yellow' : 'green' }}-600 hover:text-{{ $item->is_active ? 'yellow' : 'green' }}-900 p-1"
                        onclick="toggleMenuItem({{ $item->id }})"
                        title="{{ $item->is_active ? 'Hide' : 'Show' }}">
                    {{ $item->is_active ? '👁️' : '🙈' }}
                </button>

                <div class="relative">
                    <button type="button" class="text-gray-600 hover:text-gray-900 p-1" onclick="toggleDropdown({{ $item->id }})">
                        ⋯
                    </button>
                    <div id="dropdown-{{ $item->id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                        <a href="#" onclick="duplicateMenuItem({{ $item->id }})" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            📋 Duplicate
                        </a>
                        <a href="#" onclick="deleteMenuItem({{ $item->id }})" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                            🗑️ Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>

        @if($item->children->count() > 0)
            <div class="ml-8 mt-2 border-l-2 border-gray-200 pl-4">
                @include('admin.menus.partials.menu-items', ['items' => $item->children, 'level' => $level + 1])
            </div>
        @endif
    </div>
@endforeach
