@foreach($items as $item)
    <div class="menu-item {{ !$item->is_active ? 'menu-item-inactive' : '' }}" data-id="{{ $item->id }}">
        <div class="menu-item-content">
            <div class="menu-item-info">
                <div class="menu-item-drag-handle">
                    <i class="fas fa-grip-vertical text-muted"></i>
                </div>
                
                @if($item->icon)
                    <i class="{{ $item->icon }} menu-item-icon"></i>
                @endif
                
                <div>
                    <div class="menu-item-title">{{ $item->title }}</div>
                    <div class="menu-item-url">
                        @if($item->route_name)
                            Route: {{ $item->route_name }}
                        @else
                            {{ $item->url ?: '#' }}
                        @endif
                        
                        @if($item->target !== '_self')
                            <span class="badge badge-info badge-sm ml-1">{{ $item->target }}</span>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="menu-item-actions">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editMenuItem({{ $item->id }})" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                
                <button type="button" class="btn btn-sm btn-outline-{{ $item->is_active ? 'warning' : 'success' }}" 
                        onclick="toggleMenuItem({{ $item->id }})" 
                        title="{{ $item->is_active ? 'Hide' : 'Show' }}">
                    <i class="fas fa-{{ $item->is_active ? 'eye-slash' : 'eye' }}"></i>
                </button>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a class="dropdown-item" href="#" onclick="duplicateMenuItem({{ $item->id }})">
                            <i class="fas fa-copy mr-2"></i>Duplicate
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item text-danger" href="#" onclick="deleteMenuItem({{ $item->id }})">
                            <i class="fas fa-trash mr-2"></i>Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        @if($item->children->count() > 0)
            <div class="menu-item-children">
                @include('admin.menus.partials.menu-items', ['items' => $item->children, 'level' => $level + 1])
            </div>
        @endif
    </div>
@endforeach
