<?php

namespace Database\Seeders;

use App\Models\Page;
use App\Models\Service;
use App\Models\Event;
use App\Models\Team;
use App\Models\Project;
use App\Models\Partner;
use App\Models\Testimonial;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\Tag;
use App\Models\Faq;
use App\Models\Gallery;
use App\Models\GalleryImage;
use App\Models\MenuItem;
use App\Models\Setting;
use Illuminate\Database\Seeder;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedPages();
        $this->seedServices();
        $this->seedEvents();
        $this->seedTeam();
        $this->seedProjects();
        $this->seedPartners();
        $this->seedTestimonials();
        $this->seedBlog();
        $this->seedFaqs();
        $this->seedGalleries();
        $this->seedMenus();
        $this->seedSettings();
    }

    private function seedPages(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about-us',
                'content' => '<h2>Welcome to GreenWeb</h2><p>GreenWeb is a comprehensive SaaS platform designed to help businesses create powerful web applications with advanced customization capabilities. Our platform combines cutting-edge technology with user-friendly interfaces to deliver exceptional results.</p><h3>Our Mission</h3><p>To empower businesses with the tools they need to succeed in the digital world through innovative, scalable, and customizable web solutions.</p>',
                'meta_title' => 'About Us - GreenWeb',
                'meta_description' => 'Learn about GreenWeb, a comprehensive SaaS platform for building powerful web applications.',
                'is_published' => true,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => '<h2>Privacy Policy</h2><p>This privacy policy explains how we collect, use, and protect your personal information when you use our services.</p><h3>Information We Collect</h3><p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>',
                'meta_title' => 'Privacy Policy - GreenWeb',
                'meta_description' => 'Read our privacy policy to understand how we protect your personal information.',
                'is_published' => true,
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => '<h2>Terms of Service</h2><p>By using our services, you agree to these terms. Please read them carefully.</p><h3>Acceptance of Terms</h3><p>By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.</p>',
                'meta_title' => 'Terms of Service - GreenWeb',
                'meta_description' => 'Read our terms of service to understand the rules and regulations for using our platform.',
                'is_published' => true,
            ],
        ];

        foreach ($pages as $page) {
            Page::create($page);
        }
    }

    private function seedServices(): void
    {
        $services = [
            [
                'title' => 'Web Development',
                'slug' => 'web-development',
                'description' => 'Custom web development solutions tailored to your business needs.',
                'content' => '<p>Our web development services include custom website creation, e-commerce solutions, and web application development using the latest technologies and best practices.</p>',
                'featured_image' => 'demo-data/services/web-development.jpg',
                'icon' => 'fas fa-code',
                'is_active' => true,
                'meta_title' => 'Web Development Services',
                'meta_description' => 'Professional web development services for businesses of all sizes.',
            ],
            [
                'title' => 'Mobile App Development',
                'slug' => 'mobile-app-development',
                'description' => 'Native and cross-platform mobile applications for iOS and Android.',
                'content' => '<p>We create stunning mobile applications that provide excellent user experiences across all devices and platforms.</p>',
                'featured_image' => 'demo-data/services/mobile-development.jpg',
                'icon' => 'fas fa-mobile-alt',
                'is_active' => true,
                'meta_title' => 'Mobile App Development Services',
                'meta_description' => 'Professional mobile app development for iOS and Android platforms.',
            ],
            [
                'title' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'description' => 'Comprehensive digital marketing strategies to grow your online presence.',
                'content' => '<p>Our digital marketing services help you reach your target audience and achieve your business goals through SEO, social media marketing, and paid advertising.</p>',
                'featured_image' => 'demo-data/services/digital-marketing.jpg',
                'icon' => 'fas fa-chart-line',
                'is_active' => true,
                'meta_title' => 'Digital Marketing Services',
                'meta_description' => 'Comprehensive digital marketing solutions to grow your business online.',
            ],
        ];

        foreach ($services as $service) {
            Service::create($service);
        }
    }

    private function seedEvents(): void
    {
        $events = [
            [
                'title' => 'Tech Conference 2025',
                'slug' => 'tech-conference-2025',
                'description' => 'Annual technology conference featuring the latest trends and innovations.',
                'content' => '<p>Join us for the biggest tech conference of the year, featuring keynote speakers, workshops, and networking opportunities.</p>',
                'event_date' => now()->addDays(30),
                'event_end_date' => now()->addDays(32),
                'location' => 'Convention Center, New York',
                'featured_image' => 'demo-data/events/tech-conference.jpg',
                'status' => 'published',
                'is_featured' => true,
            ],
            [
                'title' => 'Web Development Workshop',
                'slug' => 'web-development-workshop',
                'description' => 'Hands-on workshop for learning modern web development techniques.',
                'content' => '<p>Learn the latest web development technologies and best practices in this intensive workshop.</p>',
                'event_date' => now()->addDays(15),
                'event_end_date' => now()->addDays(15),
                'location' => 'Online',
                'featured_image' => 'demo-data/events/workshop.jpg',
                'status' => 'published',
                'is_featured' => false,
            ],
        ];

        foreach ($events as $event) {
            Event::create($event);
        }
    }

    private function seedTeam(): void
    {
        $teams = [
            [
                'name' => 'John Smith',
                'slug' => 'john-smith',
                'position' => 'CEO & Founder',
                'bio' => 'John is the visionary behind GreenWeb, with over 15 years of experience in technology and business development.',
                'avatar' => 'demo-data/team/john-smith.jpg',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'social_links' => [
                    'twitter' => 'https://twitter.com/johnsmith',
                    'linkedin' => 'https://linkedin.com/in/johnsmith',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Sarah Johnson',
                'slug' => 'sarah-johnson',
                'position' => 'CTO',
                'bio' => 'Sarah leads our technical team with expertise in software architecture and system design.',
                'avatar' => 'demo-data/team/sarah-johnson.jpg',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'social_links' => [
                    'linkedin' => 'https://linkedin.com/in/sarahjohnson',
                ],
                'is_active' => true,
            ],
        ];

        foreach ($teams as $team) {
            Team::create($team);
        }
    }

    private function seedProjects(): void
    {
        $projects = [
            [
                'title' => 'E-commerce Platform',
                'slug' => 'ecommerce-platform',
                'description' => 'A comprehensive e-commerce solution for online retailers.',
                'content' => '<p>We developed a full-featured e-commerce platform with advanced inventory management, payment processing, and analytics.</p>',
                'featured_image' => 'demo-data/projects/ecommerce.jpg',
                'client' => 'RetailCorp',
                'project_url' => 'https://example.com',
                'technologies' => ['Laravel', 'Vue.js', 'MySQL', 'Redis'],
                'start_date' => now()->subDays(120),
                'end_date' => now()->subDays(30),
                'status' => 'completed',
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'title' => 'Healthcare Management System',
                'slug' => 'healthcare-management-system',
                'description' => 'Digital transformation solution for healthcare providers.',
                'content' => '<p>A comprehensive healthcare management system that streamlines patient care and administrative processes.</p>',
                'featured_image' => 'demo-data/projects/healthcare.jpg',
                'client' => 'MedCenter',
                'technologies' => ['Laravel', 'React', 'PostgreSQL'],
                'start_date' => now()->subDays(90),
                'end_date' => now()->subDays(10),
                'status' => 'completed',
                'is_active' => true,
                'is_featured' => true,
            ],
        ];

        foreach ($projects as $project) {
            Project::create($project);
        }
    }

    private function seedPartners(): void
    {
        $partners = [
            [
                'name' => 'TechCorp Solutions',
                'type' => 'partner',
                'description' => 'Strategic technology partner providing cloud infrastructure solutions.',
                'logo' => 'demo-data/partners/techcorp.jpg',
                'website_url' => 'https://techcorp.com',
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Global Enterprises',
                'type' => 'client',
                'description' => 'Fortune 500 company leveraging our SaaS solutions.',
                'logo' => 'demo-data/partners/global-enterprises.jpg',
                'website_url' => 'https://globalenterprises.com',
                'is_active' => true,
                'is_featured' => true,
            ],
        ];

        foreach ($partners as $partner) {
            Partner::create($partner);
        }
    }

    private function seedTestimonials(): void
    {
        $testimonials = [
            [
                'name' => 'Michael Brown',
                'position' => 'CEO, StartupXYZ',
                'company' => 'StartupXYZ',
                'testimonial' => 'GreenWeb has transformed our business operations. The platform is incredibly powerful and easy to use.',
                'rating' => 5,
                'image' => 'demo-data/testimonials/michael-brown.jpg',
                'status' => 'approved',
                'is_featured' => true,
            ],
            [
                'name' => 'Lisa Davis',
                'position' => 'Marketing Director',
                'company' => 'MarketingPro',
                'testimonial' => 'The multi-tenant capabilities and customization options are exactly what we needed for our clients.',
                'rating' => 5,
                'image' => 'demo-data/testimonials/lisa-davis.jpg',
                'status' => 'approved',
                'is_featured' => true,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }

    private function seedBlog(): void
    {
        // Create categories
        $categories = [
            ['name' => 'Technology', 'slug' => 'technology'],
            ['name' => 'Business', 'slug' => 'business'],
            ['name' => 'Tutorials', 'slug' => 'tutorials'],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }

        // Create tags
        $tags = [
            ['name' => 'Laravel', 'slug' => 'laravel'],
            ['name' => 'SaaS', 'slug' => 'saas'],
            ['name' => 'Web Development', 'slug' => 'web-development'],
        ];

        foreach ($tags as $tag) {
            Tag::create($tag);
        }

        // Create blog posts
        $posts = [
            [
                'title' => 'Building Scalable SaaS Applications with Laravel',
                'slug' => 'building-scalable-saas-applications-laravel',
                'excerpt' => 'Learn how to build scalable SaaS applications using Laravel framework and best practices.',
                'content' => '<p>In this comprehensive guide, we\'ll explore how to build scalable SaaS applications using Laravel...</p>',
                'featured_image' => 'demo-data/blog/laravel-saas.jpg',
                'category_id' => 1,
                'status' => 'published',
                'published_at' => now()->subDays(5),
                'meta_title' => 'Building Scalable SaaS Applications with Laravel',
                'meta_description' => 'Complete guide to building scalable SaaS applications using Laravel framework.',
            ],
            [
                'title' => 'The Future of Multi-Tenant Architecture',
                'slug' => 'future-multi-tenant-architecture',
                'excerpt' => 'Exploring the latest trends and technologies in multi-tenant application architecture.',
                'content' => '<p>Multi-tenant architecture is evolving rapidly. In this article, we discuss the latest trends...</p>',
                'featured_image' => 'demo-data/blog/multi-tenant.jpg',
                'category_id' => 1,
                'status' => 'published',
                'published_at' => now()->subDays(10),
                'meta_title' => 'The Future of Multi-Tenant Architecture',
                'meta_description' => 'Latest trends and technologies in multi-tenant application architecture.',
            ],
        ];

        foreach ($posts as $post) {
            $blogPost = BlogPost::create($post);
            // Attach tags
            $blogPost->tags()->attach([1, 2]);
        }
    }

    private function seedFaqs(): void
    {
        $faqs = [
            [
                'question' => 'What is GreenWeb?',
                'answer' => 'GreenWeb is a comprehensive SaaS platform that enables businesses to create powerful web applications with advanced customization capabilities.',
                'category' => 'General',
                'sort_order' => 1,
                'status' => 'published',
            ],
            [
                'question' => 'How does the multi-tenant system work?',
                'answer' => 'Our multi-tenant system provides complete isolation between tenants with separate databases and storage, ensuring data security and performance.',
                'category' => 'Technical',
                'sort_order' => 2,
                'status' => 'published',
            ],
            [
                'question' => 'Can I customize the platform for my brand?',
                'answer' => 'Yes, GreenWeb is white-label ready with complete branding customization options including themes, logos, and custom domains.',
                'category' => 'Customization',
                'sort_order' => 3,
                'status' => 'published',
            ],
        ];

        foreach ($faqs as $faq) {
            Faq::create($faq);
        }
    }

    private function seedGalleries(): void
    {
        $galleries = [
            [
                'title' => 'Office Gallery',
                'slug' => 'office-gallery',
                'description' => 'Photos from our modern office space and work environment.',
                'cover_image' => 'demo-data/galleries/office/cover.jpg',
                'status' => 'published',
            ],
            [
                'title' => 'Team Events',
                'slug' => 'team-events',
                'description' => 'Memorable moments from our team building events and celebrations.',
                'cover_image' => 'demo-data/galleries/events/cover.jpg',
                'status' => 'published',
            ],
        ];

        foreach ($galleries as $gallery) {
            $createdGallery = Gallery::create($gallery);

            // Add sample images to each gallery
            for ($i = 1; $i <= 5; $i++) {
                GalleryImage::create([
                    'gallery_id' => $createdGallery->id,
                    'image_path' => "demo-data/galleries/{$createdGallery->slug}/image-{$i}.jpg",
                    'title' => "Image {$i}",
                    'description' => "Sample image {$i} for {$createdGallery->title}",
                    'sort_order' => $i,
                ]);
            }
        }
    }

    private function seedMenus(): void
    {
        $menuItems = [
            [
                'title' => 'Home',
                'url' => '/',
                'type' => 'url',
                'parent_id' => null,
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'About',
                'url' => '/about-us',
                'type' => 'page',
                'parent_id' => null,
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'Services',
                'url' => '/services',
                'type' => 'url',
                'parent_id' => null,
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'title' => 'Projects',
                'url' => '/projects',
                'type' => 'url',
                'parent_id' => null,
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'title' => 'Blog',
                'url' => '/blog',
                'type' => 'url',
                'parent_id' => null,
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'title' => 'Contact',
                'url' => '/contact',
                'type' => 'url',
                'parent_id' => null,
                'sort_order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($menuItems as $item) {
            MenuItem::create($item);
        }
    }

    private function seedSettings(): void
    {
        $settings = [
            ['key' => 'site_name', 'value' => 'GreenWeb', 'type' => 'string', 'group' => 'general'],
            ['key' => 'site_description', 'value' => 'Professional SaaS Platform for Modern Businesses', 'type' => 'string', 'group' => 'general'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'contact'],
            ['key' => 'contact_phone', 'value' => '+****************', 'type' => 'string', 'group' => 'contact'],
            ['key' => 'contact_address', 'value' => '123 Business Street, Suite 100, New York, NY 10001', 'type' => 'string', 'group' => 'contact'],
            ['key' => 'facebook_url', 'value' => 'https://facebook.com/greenweb', 'type' => 'string', 'group' => 'social'],
            ['key' => 'twitter_url', 'value' => 'https://twitter.com/greenweb', 'type' => 'string', 'group' => 'social'],
            ['key' => 'linkedin_url', 'value' => 'https://linkedin.com/company/greenweb', 'type' => 'string', 'group' => 'social'],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
