@extends('layouts.public')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div data-aos="fade-right">
                <div class="flex items-center mb-4">
                    <a href="{{ route('projects.index') }}" class="text-green-600 hover:text-green-700 font-medium">
                        ← Back to Projects
                    </a>
                    <div class="ml-4">
                        {!! $project->status_badge !!}
                    </div>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">{{ $project->title }}</h1>
                
                @if($project->client)
                    <p class="text-xl text-green-600 font-semibold mb-4">Client: {{ $project->client }}</p>
                @endif
                
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">{{ $project->description }}</p>
                
                <div class="flex flex-wrap gap-4">
                    @if($project->project_url)
                        <a href="{{ $project->project_url }}" target="_blank" class="bg-green-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-green-700 transition-all duration-300">
                            View Live Project →
                        </a>
                    @endif
                    
                    @if($project->github_url)
                        <a href="{{ $project->github_url }}" target="_blank" class="bg-gray-800 text-white px-6 py-3 rounded-full font-semibold hover:bg-gray-900 transition-all duration-300">
                            <i class="fab fa-github mr-2"></i>View Code
                        </a>
                    @endif
                </div>
            </div>
            
            <div data-aos="fade-left">
                @if($project->featured_image_url)
                    <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" class="w-full rounded-lg shadow-xl">
                @else
                    <div class="w-full h-96 bg-gradient-to-br from-green-400 to-green-600 rounded-lg shadow-xl flex items-center justify-center">
                        <div class="text-white text-center">
                            <i class="fas fa-project-diagram text-6xl mb-4"></i>
                            <h3 class="text-2xl font-bold">{{ $project->title }}</h3>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Project Details -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                @if($project->content)
                    <div class="prose prose-lg max-w-none mb-12" data-aos="fade-up">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Project Overview</h2>
                        {!! $project->content !!}
                    </div>
                @endif

                <!-- Project Gallery -->
                @if($project->gallery && count($project->gallery) > 0)
                    <div class="mb-12" data-aos="fade-up">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Project Gallery</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($project->gallery as $index => $image)
                                <div class="cursor-pointer" onclick="openModal('{{ asset('storage/' . $image) }}', '{{ $project->title }} - Image {{ $index + 1 }}')">
                                    <img src="{{ asset('storage/' . $image) }}" alt="{{ $project->title }} - Image {{ $index + 1 }}" class="w-full h-64 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-lg p-6 sticky top-8" data-aos="fade-left">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Project Details</h3>
                    
                    <div class="space-y-4">
                        @if($project->start_date)
                            <div>
                                <span class="font-semibold text-gray-900">Start Date:</span>
                                <span class="text-gray-600">{{ $project->start_date->format('F j, Y') }}</span>
                            </div>
                        @endif
                        
                        @if($project->end_date)
                            <div>
                                <span class="font-semibold text-gray-900">End Date:</span>
                                <span class="text-gray-600">{{ $project->end_date->format('F j, Y') }}</span>
                            </div>
                        @endif
                        
                        @if($project->budget)
                            <div>
                                <span class="font-semibold text-gray-900">Budget:</span>
                                <span class="text-gray-600">${{ number_format($project->budget) }}</span>
                            </div>
                        @endif
                        
                        <div>
                            <span class="font-semibold text-gray-900">Status:</span>
                            {!! $project->status_badge !!}
                        </div>
                    </div>
                    
                    @if($project->technologies && count($project->technologies) > 0)
                        <div class="mt-6">
                            <h4 class="font-semibold text-gray-900 mb-3">Technologies Used</h4>
                            <div class="flex flex-wrap gap-2">
                                @foreach($project->technologies as $tech)
                                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                                        {{ $tech }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                    
                    <div class="mt-8 pt-6 border-t">
                        <h4 class="font-semibold text-gray-900 mb-4">Interested in a Similar Project?</h4>
                        <a href="{{ route('contact') }}" class="block w-full bg-green-600 text-white text-center px-4 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-300">
                            Get in Touch
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
@if($relatedProjects->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Related Projects</h2>
            <p class="text-xl text-gray-600">Explore more of our successful project implementations</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            @foreach($relatedProjects as $index => $relatedProject)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    @if($relatedProject->featured_image_url)
                        <img src="{{ $relatedProject->featured_image_url }}" alt="{{ $relatedProject->title }}" class="w-full h-48 object-cover">
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                            <div class="text-white text-center">
                                <i class="fas fa-project-diagram text-4xl mb-2"></i>
                                <h4 class="text-lg font-bold">{{ $relatedProject->title }}</h4>
                            </div>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $relatedProject->title }}</h3>
                        @if($relatedProject->client)
                            <p class="text-green-600 font-semibold mb-2">{{ $relatedProject->client }}</p>
                        @endif
                        <p class="text-gray-600 mb-4">{{ Str::limit($relatedProject->description, 100) }}</p>
                        <a href="{{ route('projects.show', $relatedProject) }}" class="text-green-600 font-semibold hover:text-green-700 transition-colors duration-300">
                            View Project →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="max-w-4xl max-h-full p-4">
        <div class="relative">
            <button onclick="closeModal()" class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
            <img id="modalImage" src="" alt="" class="max-w-full max-h-screen object-contain">
            <p id="modalCaption" class="text-white text-center mt-4"></p>
        </div>
    </div>
</div>
@endsection

@push('styles')
<!-- AOS Animation Styles -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
@endpush

@push('scripts')
<!-- AOS Animation Script -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });

    function openModal(imageSrc, caption) {
        document.getElementById('modalImage').src = imageSrc;
        document.getElementById('modalCaption').textContent = caption;
        document.getElementById('imageModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closeModal() {
        document.getElementById('imageModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    // Close modal when clicking outside the image
    document.getElementById('imageModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
</script>
@endpush
