<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class GeneralSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        // Basic Site Information
        'application_name',
        'tagline',
        'catchphrase',
        'vision',
        'mission',
        'about_us',
        'copyright',

        // Images and Branding
        'favicon',
        'logo',
        'header_image',
        'banner_image',

        // Contact Information
        'phone1',
        'phone2',
        'email1',
        'email2',
        'address',
        'map',

        // System Configuration
        'timezone',
        'currency',
        'default_language',

        // Email Configuration
        'email_from',
        'smtp_host',
        'smtp_port',
        'smtp_user',
        'smtp_pass',

        // Social Media Links
        'facebook_link',
        'twitter_link',
        'google_link',
        'youtube_link',
        'linkedin_link',
        'instagram_link',
        'whatsapp_link',

        // Security & Integrations
        'recaptcha_site_key',
        'recaptcha_secret_key',
        'recaptcha_lang',

        // SMS Configuration
        'sms_senderid',
        'sms_username',
        'sms_password',

        // Push Notifications
        'push_public_key',
        'push_private_key',

        // SEO
        'seo_keywords',

        // Newsletter Popup Settings
        'newsletter_popup_enabled',
        'newsletter_popup_delay',
        'newsletter_popup_title',
        'newsletter_popup_message',
    ];

    protected $casts = [
        'smtp_port' => 'integer',
        'newsletter_popup_enabled' => 'boolean',
        'newsletter_popup_delay' => 'integer',
    ];

    /**
     * Get the singleton settings instance
     */
    public static function getInstance()
    {
        return Cache::remember('general_settings', 3600, function () {
            return static::first() ?: static::create([
                'application_name' => config('app.name', 'Fair Price Ventures'),
                'timezone' => config('app.timezone', 'UTC'),
                'currency' => 'USD',
                'default_language' => 'en',
                'copyright' => '© ' . date('Y') . ' Fair Price Ventures. All rights reserved.',
            ]);
        });
    }

    /**
     * Update settings and clear cache
     */
    public static function updateSettings(array $data)
    {
        $settings = static::getInstance();
        $settings->update($data);
        Cache::forget('general_settings');
        return $settings;
    }

    /**
     * Get a specific setting value
     */
    public static function get($key, $default = null)
    {
        $settings = static::getInstance();
        return $settings->$key ?? $default;
    }

    /**
     * Get logo URL
     */
    public function getLogoUrlAttribute()
    {
        return $this->logo ? Storage::url($this->logo) : null;
    }

    /**
     * Get favicon URL
     */
    public function getFaviconUrlAttribute()
    {
        return $this->favicon ? Storage::url($this->favicon) : null;
    }

    /**
     * Get header image URL
     */
    public function getHeaderImageUrlAttribute()
    {
        return $this->header_image ? Storage::url($this->header_image) : null;
    }

    /**
     * Get banner image URL
     */
    public function getBannerImageUrlAttribute()
    {
        return $this->banner_image ? Storage::url($this->banner_image) : null;
    }

    /**
     * Get social media links as array
     */
    public function getSocialLinksAttribute()
    {
        return [
            'facebook' => $this->facebook_link,
            'twitter' => $this->twitter_link,
            'google' => $this->google_link,
            'youtube' => $this->youtube_link,
            'linkedin' => $this->linkedin_link,
            'instagram' => $this->instagram_link,
            'whatsapp' => $this->whatsapp_link,
        ];
    }

    /**
     * Get contact information as array
     */
    public function getContactInfoAttribute()
    {
        return [
            'phones' => array_filter([$this->phone1, $this->phone2]),
            'emails' => array_filter([$this->email1, $this->email2]),
            'address' => $this->address,
            'map' => $this->map,
        ];
    }

    /**
     * Check if SMTP is configured
     */
    public function isSmtpConfigured()
    {
        return !empty($this->smtp_host) && !empty($this->smtp_port) && !empty($this->smtp_user);
    }

    /**
     * Check if reCAPTCHA is configured
     */
    public function isRecaptchaConfigured()
    {
        return !empty($this->recaptcha_site_key) && !empty($this->recaptcha_secret_key);
    }

    /**
     * Check if SMS is configured
     */
    public function isSmsConfigured()
    {
        return !empty($this->sms_senderid) && !empty($this->sms_username) && !empty($this->sms_password);
    }

    /**
     * Check if push notifications are configured
     */
    public function isPushConfigured()
    {
        return !empty($this->push_public_key) && !empty($this->push_private_key);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('general_settings');
        });

        static::deleted(function () {
            Cache::forget('general_settings');
        });
    }
}
