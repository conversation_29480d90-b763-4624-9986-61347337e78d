<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GeneralSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display the settings form
     */
    public function index()
    {
        $settings = GeneralSetting::getInstance();
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the settings
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            // Basic Site Information
            'application_name' => 'nullable|string|max:255',
            'tagline' => 'nullable|string|max:255',
            'catchphrase' => 'nullable|string|max:3000',
            'vision' => 'nullable|string|max:3000',
            'mission' => 'nullable|string',
            'about_us' => 'nullable|string|max:3000',
            'copyright' => 'nullable|string|max:500',
            
            // Images and Branding
            'favicon' => 'nullable|image|mimes:ico,png,jpg,jpeg|max:1024',
            'logo' => 'nullable|image|mimes:png,jpg,jpeg,svg|max:2048',
            'header_image' => 'nullable|image|mimes:png,jpg,jpeg|max:5120',
            'banner_image' => 'nullable|image|mimes:png,jpg,jpeg|max:5120',
            
            // Contact Information
            'phone1' => 'nullable|string|max:50',
            'phone2' => 'nullable|string|max:50',
            'email1' => 'nullable|email|max:100',
            'email2' => 'nullable|email|max:100',
            'address' => 'nullable|string|max:1000',
            'map' => 'nullable|string',
            
            // System Configuration
            'timezone' => 'nullable|string|max:255',
            'currency' => 'nullable|string|max:10',
            'default_language' => 'nullable|string|max:10',
            
            // Email Configuration
            'email_from' => 'nullable|email|max:100',
            'smtp_host' => 'nullable|string|max:255',
            'smtp_port' => 'nullable|integer|min:1|max:65535',
            'smtp_user' => 'nullable|string|max:255',
            'smtp_pass' => 'nullable|string|max:255',
            
            // Social Media Links
            'facebook_link' => 'nullable|url|max:1000',
            'twitter_link' => 'nullable|url|max:1000',
            'google_link' => 'nullable|url|max:1000',
            'youtube_link' => 'nullable|url|max:1000',
            'linkedin_link' => 'nullable|url|max:1000',
            'instagram_link' => 'nullable|url|max:1000',
            'whatsapp_link' => 'nullable|string|max:1000',
            
            // Security & Integrations
            'recaptcha_site_key' => 'nullable|string|max:255',
            'recaptcha_secret_key' => 'nullable|string|max:255',
            'recaptcha_lang' => 'nullable|string|max:10',
            
            // SMS Configuration
            'sms_senderid' => 'nullable|string|max:20',
            'sms_username' => 'nullable|string|max:100',
            'sms_password' => 'nullable|string|max:100',
            
            // Push Notifications
            'push_public_key' => 'nullable|string',
            'push_private_key' => 'nullable|string',
            
            // SEO
            'seo_keywords' => 'nullable|string',
        ]);

        $settings = GeneralSetting::getInstance();

        // Handle file uploads
        $this->handleFileUploads($request, $validated, $settings);

        // Update settings
        GeneralSetting::updateSettings($validated);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully.');
    }

    /**
     * Handle file uploads for settings
     */
    private function handleFileUploads(Request $request, array &$validated, GeneralSetting $settings)
    {
        $fileFields = ['favicon', 'logo', 'header_image', 'banner_image'];

        foreach ($fileFields as $field) {
            if ($request->hasFile($field)) {
                // Delete old file if exists
                if ($settings->$field && Storage::exists($settings->$field)) {
                    Storage::delete($settings->$field);
                }

                // Store new file
                $path = $request->file($field)->store('settings', 'public');
                $validated[$field] = $path;
            } else {
                // Remove from validated array if no new file uploaded
                unset($validated[$field]);
            }
        }
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        $settings = GeneralSetting::getInstance();

        if (!$settings->isSmtpConfigured()) {
            return response()->json([
                'success' => false,
                'message' => 'SMTP configuration is incomplete.'
            ]);
        }

        try {
            // Configure mail settings temporarily
            config([
                'mail.mailers.smtp.host' => $settings->smtp_host,
                'mail.mailers.smtp.port' => $settings->smtp_port,
                'mail.mailers.smtp.username' => $settings->smtp_user,
                'mail.mailers.smtp.password' => $settings->smtp_pass,
                'mail.from.address' => $settings->email_from,
                'mail.from.name' => $settings->application_name,
            ]);

            // Send test email
            \Mail::raw('This is a test email from ' . $settings->application_name, function ($message) use ($request, $settings) {
                $message->to($request->test_email)
                    ->subject('Test Email from ' . $settings->application_name);
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Clear settings cache
     */
    public function clearCache()
    {
        \Cache::forget('general_settings');
        
        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings cache cleared successfully.');
    }

    /**
     * Export settings as JSON
     */
    public function export()
    {
        $settings = GeneralSetting::getInstance();
        
        // Remove sensitive data from export
        $exportData = $settings->toArray();
        unset($exportData['smtp_pass'], $exportData['recaptcha_secret_key'], 
              $exportData['sms_password'], $exportData['push_private_key']);

        $filename = 'settings_export_' . date('Y-m-d_H-i-s') . '.json';
        
        return response()->json($exportData)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Get timezones list
     */
    public function getTimezones()
    {
        return timezone_identifiers_list();
    }

    /**
     * Get currencies list
     */
    public function getCurrencies()
    {
        return [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'JPY' => 'Japanese Yen (¥)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
            'CHF' => 'Swiss Franc (CHF)',
            'CNY' => 'Chinese Yuan (¥)',
            'INR' => 'Indian Rupee (₹)',
            'KES' => 'Kenyan Shilling (KSh)',
            'NGN' => 'Nigerian Naira (₦)',
            'ZAR' => 'South African Rand (R)',
        ];
    }

    /**
     * Get languages list
     */
    public function getLanguages()
    {
        return [
            'en' => 'English',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
            'ja' => 'Japanese',
            'ko' => 'Korean',
            'zh' => 'Chinese',
            'ar' => 'Arabic',
            'hi' => 'Hindi',
            'sw' => 'Swahili',
        ];
    }
}
