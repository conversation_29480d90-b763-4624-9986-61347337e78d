<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use Illuminate\Http\Request;

class GalleryController extends Controller
{
    /**
     * Display a listing of galleries.
     */
    public function index(Request $request)
    {
        $query = Gallery::published()->with(['images' => function ($query) {
            $query->orderBy('sort_order')->limit(4); // Preview images
        }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Filter by featured
        if ($request->filled('featured') && $request->featured == '1') {
            $query->featured();
        }

        // Get featured galleries for hero section
        $featuredGalleries = Gallery::published()
            ->featured()
            ->with(['images' => function ($query) {
                $query->orderBy('sort_order')->limit(1);
            }])
            ->ordered()
            ->limit(3)
            ->get();

        // Get all galleries with pagination
        $galleries = $query->ordered()->paginate(12);

        // Get available categories
        $categories = Gallery::published()
            ->distinct('category')
            ->whereNotNull('category')
            ->where('category', '!=', '')
            ->pluck('category')
            ->sort()
            ->values();

        return view('gallery.index', compact('galleries', 'featuredGalleries', 'categories'));
    }

    /**
     * Display the specified gallery.
     */
    public function show(Gallery $gallery)
    {
        // Check if gallery is published
        if (!$gallery->is_published) {
            abort(404);
        }

        // Load all images for this gallery
        $gallery->load(['images' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        // Get related galleries
        $relatedGalleries = Gallery::published()
            ->where('id', '!=', $gallery->id)
            ->when($gallery->category, function ($query) use ($gallery) {
                $query->where('category', $gallery->category);
            })
            ->with(['images' => function ($query) {
                $query->orderBy('sort_order')->limit(1);
            }])
            ->ordered()
            ->limit(6)
            ->get();

        // Get other galleries for sidebar
        $otherGalleries = Gallery::published()
            ->where('id', '!=', $gallery->id)
            ->with(['images' => function ($query) {
                $query->orderBy('sort_order')->limit(1);
            }])
            ->ordered()
            ->limit(5)
            ->get();

        return view('gallery.show', compact('gallery', 'relatedGalleries', 'otherGalleries'));
    }

    /**
     * Display galleries by category.
     */
    public function category(Request $request, $category)
    {
        $galleries = Gallery::published()
            ->byCategory($category)
            ->with(['images' => function ($query) {
                $query->orderBy('sort_order')->limit(4);
            }])
            ->ordered()
            ->paginate(12);

        $categories = Gallery::published()
            ->distinct('category')
            ->whereNotNull('category')
            ->where('category', '!=', '')
            ->pluck('category')
            ->sort()
            ->values();

        return view('gallery.category', compact('galleries', 'category', 'categories'));
    }

    /**
     * Display featured galleries.
     */
    public function featured()
    {
        $galleries = Gallery::published()
            ->featured()
            ->with(['images' => function ($query) {
                $query->orderBy('sort_order')->limit(4);
            }])
            ->ordered()
            ->paginate(12);

        return view('gallery.featured', compact('galleries'));
    }

    /**
     * Search galleries (AJAX endpoint).
     */
    public function search(Request $request)
    {
        $query = Gallery::published();

        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        $galleries = $query->with(['images' => function ($query) {
            $query->orderBy('sort_order')->limit(1);
        }])->ordered()->limit(10)->get();

        return response()->json([
            'galleries' => $galleries->map(function ($gallery) {
                return [
                    'id' => $gallery->id,
                    'title' => $gallery->title,
                    'slug' => $gallery->slug,
                    'description' => $gallery->description,
                    'category' => $gallery->category,
                    'image_count' => $gallery->images_count,
                    'cover_image_url' => $gallery->cover_image_url,
                    'url' => route('gallery.show', $gallery),
                ];
            })
        ]);
    }

    /**
     * Get gallery images for lightbox (AJAX endpoint).
     */
    public function images(Gallery $gallery)
    {
        if (!$gallery->is_published) {
            abort(404);
        }

        $images = $gallery->images()->orderBy('sort_order')->get();

        return response()->json([
            'images' => $images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'title' => $image->title,
                    'caption' => $image->caption,
                    'alt_text' => $image->alt_text,
                    'image_url' => $image->image_url,
                    'thumbnail_url' => $image->thumbnail_url,
                    'dimensions' => $image->dimensions,
                    'file_size_human' => $image->file_size_human,
                ];
            })
        ]);
    }
}
