<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\License;
use App\Services\LicenseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LicenseController extends Controller
{
    protected $licenseService;

    public function __construct(LicenseService $licenseService)
    {
        $this->licenseService = $licenseService;
    }

    /**
     * Show license management dashboard
     */
    public function index()
    {
        $license = License::current();
        $licenseInfo = $this->licenseService->getLicenseInfo();

        return view('admin.license.index', compact('license', 'licenseInfo'));
    }

    /**
     * Show license activation form
     */
    public function activate()
    {
        $license = License::current();

        // If already activated, redirect to management
        if ($license && $license->isValid()) {
            return redirect()->route('admin.license.index');
        }

        return view('admin.license.activate', compact('license'));
    }

    /**
     * Process license activation
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'license_key' => 'required|string|min:10',
            'purchase_code' => 'required|string|min:10',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $result = $this->licenseService->activateLicense(
                $request->license_key,
                $request->purchase_code
            );

            if ($result['success']) {
                return redirect()->route('admin.license.index')
                    ->with('success', 'License activated successfully!');
            } else {
                return back()->withErrors(['license_key' => $result['message']])->withInput();
            }
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'License activation failed: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Verify license manually
     */
    public function verify(Request $request)
    {
        $license = License::current();

        if (!$license) {
            return response()->json([
                'success' => false,
                'message' => 'No license found for this installation.'
            ], 404);
        }

        try {
            $isValid = $license->verify();

            return response()->json([
                'success' => true,
                'valid' => $isValid,
                'status' => $license->status,
                'message' => $isValid ? 'License verified successfully.' : 'License verification failed.',
                'license_info' => [
                    'status' => $license->status,
                    'expires_at' => $license->expires_at?->format('Y-m-d H:i:s'),
                    'days_remaining' => $license->getDaysRemaining(),
                    'grace_period_active' => $license->is_grace_period_active,
                    'grace_period_days_remaining' => $license->getGracePeriodDaysRemaining(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'License verification failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate license
     */
    public function deactivate(Request $request)
    {
        $license = License::current();

        if (!$license) {
            return response()->json([
                'success' => false,
                'message' => 'No license found for this installation.'
            ], 404);
        }

        try {
            $result = $this->licenseService->deactivateLicense($license);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'License deactivation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show license renewal page
     */
    public function renew()
    {
        $license = License::current();

        return view('admin.license.renew', compact('license'));
    }

    /**
     * Process license renewal
     */
    public function processRenewal(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'renewal_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $result = $this->licenseService->renewLicense($request->renewal_code);

            if ($result['success']) {
                return redirect()->route('admin.license.index')
                    ->with('success', 'License renewed successfully!');
            } else {
                return back()->withErrors(['renewal_code' => $result['message']])->withInput();
            }
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'License renewal failed: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Download license certificate
     */
    public function downloadCertificate()
    {
        $license = License::current();

        if (!$license || !$license->isValid()) {
            return back()->with('error', 'No valid license found.');
        }

        try {
            $certificate = $this->licenseService->generateCertificate($license);

            return response()->streamDownload(function () use ($certificate) {
                echo $certificate;
            }, 'license-certificate.txt', [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate certificate: ' . $e->getMessage());
        }
    }
}
