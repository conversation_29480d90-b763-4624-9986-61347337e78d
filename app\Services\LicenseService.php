<?php

namespace App\Services;

use App\Models\License;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class LicenseService
{
    protected $verificationUrl;
    protected $timeout;

    public function __construct()
    {
        $this->verificationUrl = config('license.verification_url');
        $this->timeout = config('license.server.timeout', 30);
    }

    /**
     * Activate a license
     */
    public function activateLicense(string $licenseKey, string $purchaseCode): array
    {
        try {
            // Check if license already exists for this domain
            $existingLicense = License::where('domain', request()->getHost())->first();
            
            if ($existingLicense && $existingLicense->isValid()) {
                return [
                    'success' => false,
                    'message' => 'A valid license is already activated for this domain.'
                ];
            }

            // Verify with remote server
            $response = Http::timeout($this->timeout)->post($this->verificationUrl, [
                'action' => 'activate',
                'license_key' => $licenseKey,
                'purchase_code' => $purchaseCode,
                'domain' => request()->getHost(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'message' => 'Unable to verify license with server. Please try again later.'
                ];
            }

            $data = $response->json();

            if (!($data['valid'] ?? false)) {
                return [
                    'success' => false,
                    'message' => $data['message'] ?? 'Invalid license key or purchase code.'
                ];
            }

            // Create or update license record
            $license = License::updateOrCreate(
                ['domain' => request()->getHost()],
                [
                    'license_key' => $licenseKey,
                    'purchase_code' => $purchaseCode,
                    'ip_address' => request()->ip(),
                    'status' => $data['status'] ?? 'active',
                    'license_type' => $data['license_type'] ?? 'regular',
                    'activated_at' => now(),
                    'expires_at' => isset($data['expires_at']) ? Carbon::parse($data['expires_at']) : null,
                    'last_verified_at' => now(),
                    'verification_data' => $data,
                    'grace_period_days' => $data['grace_period_days'] ?? config('license.grace_period_days', 7),
                ]
            );

            Log::info('License activated successfully', [
                'domain' => request()->getHost(),
                'license_key' => substr($licenseKey, 0, 8) . '...',
                'status' => $license->status,
            ]);

            return [
                'success' => true,
                'message' => 'License activated successfully!',
                'license' => $license
            ];

        } catch (Exception $e) {
            Log::error('License activation failed', [
                'error' => $e->getMessage(),
                'domain' => request()->getHost(),
            ]);

            return [
                'success' => false,
                'message' => 'License activation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Deactivate a license
     */
    public function deactivateLicense(License $license): array
    {
        try {
            // Notify remote server
            $response = Http::timeout($this->timeout)->post($this->verificationUrl, [
                'action' => 'deactivate',
                'license_key' => $license->license_key,
                'purchase_code' => $license->purchase_code,
                'domain' => $license->domain,
            ]);

            // Update local status regardless of remote response
            $license->update([
                'status' => 'suspended',
                'last_verified_at' => now(),
            ]);

            Log::info('License deactivated', [
                'domain' => $license->domain,
                'license_key' => substr($license->license_key, 0, 8) . '...',
            ]);

            return [
                'success' => true,
                'message' => 'License deactivated successfully.'
            ];

        } catch (Exception $e) {
            Log::error('License deactivation failed', [
                'error' => $e->getMessage(),
                'license_id' => $license->id,
            ]);

            return [
                'success' => false,
                'message' => 'License deactivation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Renew a license
     */
    public function renewLicense(string $renewalCode): array
    {
        $license = License::current();
        
        if (!$license) {
            return [
                'success' => false,
                'message' => 'No license found for this installation.'
            ];
        }

        try {
            $response = Http::timeout($this->timeout)->post($this->verificationUrl, [
                'action' => 'renew',
                'license_key' => $license->license_key,
                'purchase_code' => $license->purchase_code,
                'renewal_code' => $renewalCode,
                'domain' => $license->domain,
            ]);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'message' => 'Unable to verify renewal with server. Please try again later.'
                ];
            }

            $data = $response->json();

            if (!($data['valid'] ?? false)) {
                return [
                    'success' => false,
                    'message' => $data['message'] ?? 'Invalid renewal code.'
                ];
            }

            // Update license
            $license->update([
                'status' => 'active',
                'expires_at' => isset($data['expires_at']) ? Carbon::parse($data['expires_at']) : null,
                'last_verified_at' => now(),
                'verification_data' => array_merge($license->verification_data ?? [], $data),
                'is_grace_period_active' => false,
                'grace_period_started_at' => null,
            ]);

            Log::info('License renewed successfully', [
                'domain' => $license->domain,
                'license_key' => substr($license->license_key, 0, 8) . '...',
                'new_expiry' => $license->expires_at,
            ]);

            return [
                'success' => true,
                'message' => 'License renewed successfully!',
                'license' => $license
            ];

        } catch (Exception $e) {
            Log::error('License renewal failed', [
                'error' => $e->getMessage(),
                'license_id' => $license->id,
            ]);

            return [
                'success' => false,
                'message' => 'License renewal failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get license information
     */
    public function getLicenseInfo(): array
    {
        $license = License::current();
        
        if (!$license) {
            return [
                'status' => 'not_activated',
                'message' => 'No license found for this installation.',
            ];
        }

        return [
            'status' => $license->status,
            'license_type' => $license->license_type,
            'domain' => $license->domain,
            'activated_at' => $license->activated_at,
            'expires_at' => $license->expires_at,
            'days_remaining' => $license->getDaysRemaining(),
            'is_valid' => $license->isValid(),
            'grace_period_active' => $license->is_grace_period_active,
            'grace_period_days_remaining' => $license->getGracePeriodDaysRemaining(),
            'last_verified_at' => $license->last_verified_at,
            'needs_verification' => $license->needsVerification(),
        ];
    }

    /**
     * Generate license certificate
     */
    public function generateCertificate(License $license): string
    {
        $certificate = "LICENSE CERTIFICATE\n";
        $certificate .= "==================\n\n";
        $certificate .= "Product: " . config('app.name') . "\n";
        $certificate .= "License Key: " . $license->license_key . "\n";
        $certificate .= "Purchase Code: " . $license->purchase_code . "\n";
        $certificate .= "License Type: " . ucfirst($license->license_type) . "\n";
        $certificate .= "Domain: " . $license->domain . "\n";
        $certificate .= "Status: " . ucfirst($license->status) . "\n";
        $certificate .= "Activated: " . $license->activated_at->format('Y-m-d H:i:s') . "\n";
        
        if ($license->expires_at) {
            $certificate .= "Expires: " . $license->expires_at->format('Y-m-d H:i:s') . "\n";
        } else {
            $certificate .= "Expires: Never\n";
        }
        
        $certificate .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        $certificate .= "This certificate confirms that the above license is valid and properly activated.\n";
        $certificate .= "Please keep this certificate for your records.\n";

        return $certificate;
    }

    /**
     * Check if license is about to expire
     */
    public function checkExpiryWarnings(): array
    {
        $license = License::current();
        $warnings = [];

        if (!$license || !$license->expires_at) {
            return $warnings;
        }

        $daysRemaining = $license->getDaysRemaining();
        $warningDays = config('license.notifications.expiry_warning_days', [30, 14, 7, 3, 1]);

        foreach ($warningDays as $warningDay) {
            if ($daysRemaining <= $warningDay && $daysRemaining > 0) {
                $warnings[] = [
                    'type' => 'expiry_warning',
                    'days_remaining' => $daysRemaining,
                    'message' => "Your license will expire in {$daysRemaining} day(s). Please renew to avoid service interruption.",
                ];
                break;
            }
        }

        if ($license->is_grace_period_active) {
            $graceDays = $license->getGracePeriodDaysRemaining();
            $warnings[] = [
                'type' => 'grace_period',
                'days_remaining' => $graceDays,
                'message' => "Your license has expired. You have {$graceDays} day(s) remaining in the grace period.",
            ];
        }

        return $warnings;
    }
}
