<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use App\Livewire\Pages\Auth\Login;
use App\Livewire\Pages\Auth\Register;

class TestLivewireAuth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:livewire-auth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Livewire authentication system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Livewire Authentication System...');
        $this->newLine();

        // Test 1: Check if routes exist
        $this->info('1. Checking authentication routes...');
        
        $authRoutes = [
            'login' => Login::class,
            'register' => Register::class,
            'password.request' => \App\Livewire\Pages\Auth\ForgotPassword::class,
            'password.reset' => \App\Livewire\Pages\Auth\ResetPassword::class,
            'verification.notice' => \App\Livewire\Pages\Auth\VerifyEmail::class,
            'password.confirm' => \App\Livewire\Pages\Auth\ConfirmPassword::class,
        ];

        foreach ($authRoutes as $routeName => $componentClass) {
            if (Route::has($routeName)) {
                $this->info("   ✓ Route '{$routeName}' exists");
            } else {
                $this->error("   ✗ Route '{$routeName}' missing");
            }
        }

        $this->newLine();

        // Test 2: Check if Livewire components exist
        $this->info('2. Checking Livewire component classes...');
        
        foreach ($authRoutes as $routeName => $componentClass) {
            if (class_exists($componentClass)) {
                $this->info("   ✓ Component '{$componentClass}' exists");
                
                // Check if __invoke method exists
                if (method_exists($componentClass, '__invoke')) {
                    $this->info("     ✓ __invoke method exists");
                } else {
                    $this->error("     ✗ __invoke method missing");
                }
            } else {
                $this->error("   ✗ Component '{$componentClass}' missing");
            }
        }

        $this->newLine();

        // Test 3: Check if views exist
        $this->info('3. Checking Livewire view files...');
        
        $viewFiles = [
            'livewire.pages.auth.login',
            'livewire.pages.auth.register',
            'livewire.pages.auth.forgot-password',
            'livewire.pages.auth.reset-password',
            'livewire.pages.auth.verify-email',
            'livewire.pages.auth.confirm-password',
        ];

        foreach ($viewFiles as $viewFile) {
            $viewPath = resource_path('views/' . str_replace('.', '/', $viewFile) . '.blade.php');
            if (file_exists($viewPath)) {
                $this->info("   ✓ View '{$viewFile}' exists");
            } else {
                $this->error("   ✗ View '{$viewFile}' missing");
            }
        }

        $this->newLine();

        // Test 4: Check if LoginForm exists
        $this->info('4. Checking LoginForm class...');
        
        if (class_exists(\App\Livewire\Forms\LoginForm::class)) {
            $this->info("   ✓ LoginForm class exists");
        } else {
            $this->error("   ✗ LoginForm class missing");
        }

        $this->newLine();

        // Test 5: Check if Logout action exists
        $this->info('5. Checking Logout action...');
        
        if (class_exists(\App\Livewire\Actions\Logout::class)) {
            $this->info("   ✓ Logout action exists");
        } else {
            $this->error("   ✗ Logout action missing");
        }

        $this->newLine();

        // Test 6: Check if auth layout exists
        $this->info('6. Checking auth layout...');
        
        $authLayoutPath = resource_path('views/layouts/auth.blade.php');
        if (file_exists($authLayoutPath)) {
            $this->info("   ✓ Auth layout exists");
        } else {
            $this->error("   ✗ Auth layout missing");
        }

        $this->newLine();

        // Test 7: Check Livewire configuration
        $this->info('7. Checking Livewire configuration...');
        
        $livewireConfig = config('livewire');
        if ($livewireConfig) {
            $this->info("   ✓ Livewire config loaded");
            $this->info("   ✓ Progress bar color: " . $livewireConfig['navigate']['progress_bar_color']);
        } else {
            $this->error("   ✗ Livewire config missing");
        }

        $this->newLine();
        $this->info('✅ Livewire Authentication System Test Complete!');
        
        return Command::SUCCESS;
    }
}
