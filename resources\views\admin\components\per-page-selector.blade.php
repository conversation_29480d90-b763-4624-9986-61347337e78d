{{-- Per Page Selector Component --}}
@php
    $currentPerPage = $currentPerPage ?? request('per_page', 10);
    $options = $options ?? [10, 25, 50, 100];
    $totalRecords = $totalRecords ?? 0;
@endphp

<div class="flex items-center justify-between mb-4">
    <div class="flex items-center space-x-4">
        <!-- Records per page selector -->
        <div class="flex items-center space-x-2">
            <label for="per-page" class="text-sm text-gray-700">Show:</label>
            <select id="per-page" name="per_page" class="per-page-select border-gray-300 rounded-md text-sm focus:ring-green-500 focus:border-green-500">
                @foreach($options as $option)
                    <option value="{{ $option }}" {{ $currentPerPage == $option ? 'selected' : '' }}>
                        {{ $option }}
                    </option>
                @endforeach
            </select>
            <span class="text-sm text-gray-700">records per page</span>
        </div>

        <!-- Total records info -->
        @if($totalRecords > 0)
            <div class="text-sm text-gray-500">
                Total: {{ number_format($totalRecords) }} records
            </div>
        @endif
    </div>

    <!-- Additional controls -->
    <div class="flex items-center space-x-2">
        @if(isset($additionalControls))
            {!! $additionalControls !!}
        @endif
    </div>
</div>
