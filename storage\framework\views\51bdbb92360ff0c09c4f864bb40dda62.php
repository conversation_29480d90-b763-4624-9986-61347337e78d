<div>
    <!--[if BLOCK]><![endif]--><?php if($subscribed): ?>
        <div class="text-center py-4">
            <div class="text-green-400 text-4xl mb-2">✓</div>
            <p class="text-green-300">Thank you for subscribing!</p>
        </div>
    <?php else: ?>
        <form wire:submit="subscribe" class="space-y-4">
            <!--[if BLOCK]><![endif]--><?php if($source !== 'popup'): ?>
                <div>
                    <input type="text" wire:model="name" placeholder="Your Name (Optional)" class="w-full px-4 py-2 bg-gray-700 text-white placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-green-400">
                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-400 text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <div class="flex space-x-2">
                <input type="email" wire:model="email" placeholder="Enter your email" class="flex-1 px-4 py-2 <?php echo e($source === 'popup' ? 'border border-gray-300' : 'bg-gray-700 text-white placeholder-gray-400'); ?> rounded-md focus:outline-none focus:ring-2 focus:ring-green-400">
                <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-green-700 transition duration-300 disabled:opacity-50" wire:loading.attr="disabled">
                    <span wire:loading.remove">Subscribe</span>
                    <span wire:loading>...</span>
                </button>
            </div>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-400 text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </form>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/livewire/newsletter-signup.blade.php ENDPATH**/ ?>