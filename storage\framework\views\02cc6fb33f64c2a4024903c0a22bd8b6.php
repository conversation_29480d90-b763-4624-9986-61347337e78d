<?php $__env->startSection('content'); ?>
<div class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Service Header -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <?php if($service->featured_image): ?>
                <img src="<?php echo e(asset('storage/' . $service->featured_image)); ?>" alt="<?php echo e($service->title); ?>" class="w-full h-64 object-cover">
            <?php endif; ?>

            <div class="p-8">
                <div class="flex items-center mb-4">
                    <?php if($service->icon): ?>
                        <div class="text-5xl mr-4"><?php echo e($service->icon); ?></div>
                    <?php endif; ?>
                    <div>
                        <h1 class="text-4xl font-bold text-gray-900 mb-2"><?php echo e($service->title); ?></h1>
                        <?php if($service->is_featured): ?>
                            <span class="bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded">Featured Service</span>
                        <?php endif; ?>
                    </div>
                </div>

                <p class="text-xl text-gray-600 mb-6"><?php echo e($service->description); ?></p>

                <?php if($service->price): ?>
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Investment</h3>
                                <p class="text-3xl font-bold text-green-600"><?php echo e($service->formatted_price); ?></p>
                            </div>
                            <a href="<?php echo e(route('contact')); ?>" class="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition duration-300">
                                Get Started
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Service Content -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <div class="prose prose-lg max-w-none">
                <?php echo $service->content; ?>

            </div>
        </div>

        <!-- Contact Section -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Interested in This Service?</h2>
            <p class="text-gray-600 mb-6">
                Contact us to learn more about how this service can benefit your business. We'll be happy to discuss your specific needs and provide a customized solution.
            </p>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('contact-form');

$__html = app('livewire')->mount($__name, $__params, 'lw-1170543601-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>

        <!-- Service FAQs -->
        <?php if($serviceFaqs->count() > 0): ?>
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <div class="space-y-4">
                <?php $__currentLoopData = $serviceFaqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-green-500 faq-toggle"
                            data-target="faq-<?php echo e($faq->id); ?>">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900"><?php echo e($faq->question); ?></h3>
                            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 faq-icon"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </button>
                    <div id="faq-<?php echo e($faq->id); ?>" class="faq-content hidden px-6 pb-4">
                        <div class="text-gray-700 prose prose-sm max-w-none">
                            <?php echo $faq->answer; ?>

                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="mt-6 text-center">
                <a href="<?php echo e(route('faqs.index')); ?>" class="text-green-600 hover:text-green-800 font-medium">
                    View All FAQs →
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Related Services -->
        <?php if($relatedServices->count() > 0): ?>
        <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-300">
                    <?php if($relatedService->icon): ?>
                        <div class="text-3xl mb-3"><?php echo e($relatedService->icon); ?></div>
                    <?php endif; ?>

                    <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($relatedService->title); ?></h3>
                    <p class="text-gray-600 text-sm mb-3"><?php echo e(Str::limit($relatedService->description, 100)); ?></p>

                    <?php if($relatedService->price): ?>
                        <p class="text-green-600 font-semibold text-sm mb-3"><?php echo e($relatedService->formatted_price); ?></p>
                    <?php endif; ?>

                    <a href="<?php echo e(route('services.show', $relatedService)); ?>" class="text-green-600 font-semibold text-sm hover:text-green-800">
                        Learn More →
                    </a>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');

    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\greenweb\resources\views/services/show.blade.php ENDPATH**/ ?>