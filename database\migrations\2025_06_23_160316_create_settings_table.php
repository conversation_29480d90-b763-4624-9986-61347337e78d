<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, integer, boolean, json, array
            $table->string('group')->default('general'); // Group settings by category
            $table->string('label')->nullable(); // Human readable label
            $table->text('description')->nullable(); // Setting description
            $table->json('validation_rules')->nullable(); // Validation rules
            $table->json('options')->nullable(); // For select/radio options
            $table->boolean('is_public')->default(false); // Can be accessed publicly
            $table->boolean('is_encrypted')->default(false); // Should be encrypted
            $table->integer('sort_order')->default(0); // Display order
            $table->timestamps();

            $table->index(['group', 'sort_order']);
            $table->index(['is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
