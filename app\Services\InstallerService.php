<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Exception;

class InstallerService
{
    protected $installationFile = 'installed.lock';
    protected $licenseServerUrl = 'https://license.yourcompany.com/api/validate';

    /**
     * Check if the application is already installed
     */
    public function isInstalled(): bool
    {
        return File::exists(storage_path($this->installationFile));
    }

    /**
     * Mark the application as installed
     */
    public function markAsInstalled(): void
    {
        File::put(storage_path($this->installationFile), json_encode([
            'installed_at' => now()->toISOString(),
            'version' => config('app.version', '1.0.0'),
            'domain' => request()->getHost(),
        ]));
    }

    /**
     * Check system requirements
     */
    public function checkRequirements(): array
    {
        $requirements = [
            'php_version' => [
                'name' => 'PHP Version (>= 8.1)',
                'required' => true,
                'current' => PHP_VERSION,
                'status' => version_compare(PHP_VERSION, '8.1.0', '>='),
            ],
            'openssl' => [
                'name' => 'OpenSSL Extension',
                'required' => true,
                'status' => extension_loaded('openssl'),
            ],
            'pdo' => [
                'name' => 'PDO Extension',
                'required' => true,
                'status' => extension_loaded('pdo'),
            ],
            'mbstring' => [
                'name' => 'Mbstring Extension',
                'required' => true,
                'status' => extension_loaded('mbstring'),
            ],
            'tokenizer' => [
                'name' => 'Tokenizer Extension',
                'required' => true,
                'status' => extension_loaded('tokenizer'),
            ],
            'xml' => [
                'name' => 'XML Extension',
                'required' => true,
                'status' => extension_loaded('xml'),
            ],
            'ctype' => [
                'name' => 'Ctype Extension',
                'required' => true,
                'status' => extension_loaded('ctype'),
            ],
            'json' => [
                'name' => 'JSON Extension',
                'required' => true,
                'status' => extension_loaded('json'),
            ],
            'bcmath' => [
                'name' => 'BCMath Extension',
                'required' => true,
                'status' => extension_loaded('bcmath'),
            ],
            'curl' => [
                'name' => 'cURL Extension',
                'required' => true,
                'status' => extension_loaded('curl'),
            ],
            'fileinfo' => [
                'name' => 'Fileinfo Extension',
                'required' => true,
                'status' => extension_loaded('fileinfo'),
            ],
            'gd' => [
                'name' => 'GD Extension',
                'required' => false,
                'status' => extension_loaded('gd'),
                'note' => 'Required for image processing',
            ],
            'zip' => [
                'name' => 'ZIP Extension',
                'required' => false,
                'status' => extension_loaded('zip'),
                'note' => 'Required for theme/plugin installation',
            ],
        ];

        return $requirements;
    }

    /**
     * Check file permissions
     */
    public function checkPermissions(): array
    {
        $permissions = [
            'storage' => [
                'path' => storage_path(),
                'required' => '755',
                'status' => $this->checkDirectoryPermission(storage_path()),
            ],
            'storage_app' => [
                'path' => storage_path('app'),
                'required' => '755',
                'status' => $this->checkDirectoryPermission(storage_path('app')),
            ],
            'storage_logs' => [
                'path' => storage_path('logs'),
                'required' => '755',
                'status' => $this->checkDirectoryPermission(storage_path('logs')),
            ],
            'bootstrap_cache' => [
                'path' => base_path('bootstrap/cache'),
                'required' => '755',
                'status' => $this->checkDirectoryPermission(base_path('bootstrap/cache')),
            ],
            'public_storage' => [
                'path' => public_path('storage'),
                'required' => '755',
                'status' => $this->checkDirectoryPermission(public_path('storage')),
            ],
            'env_file' => [
                'path' => base_path('.env'),
                'required' => '644',
                'status' => $this->checkFilePermission(base_path('.env')),
            ],
        ];

        return $permissions;
    }

    /**
     * Check directory permission
     */
    private function checkDirectoryPermission(string $path): bool
    {
        if (!File::exists($path)) {
            try {
                File::makeDirectory($path, 0755, true);
                return true;
            } catch (Exception $e) {
                return false;
            }
        }

        return File::isWritable($path);
    }

    /**
     * Check file permission
     */
    private function checkFilePermission(string $path): bool
    {
        if (!File::exists($path)) {
            try {
                File::put($path, '');
                return true;
            } catch (Exception $e) {
                return false;
            }
        }

        return File::isWritable($path);
    }

    /**
     * Save environment configuration to .env file
     */
    public function saveEnvironmentFile(array $config): void
    {
        $envContent = $this->generateEnvContent($config);
        
        if (!File::put(base_path('.env'), $envContent)) {
            throw new Exception('Could not write to .env file');
        }

        // Clear config cache to reload new environment
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }

    /**
     * Generate .env file content
     */
    private function generateEnvContent(array $config): string
    {
        $appKey = 'base64:' . base64_encode(random_bytes(32));
        
        return "APP_NAME=\"{$config['app_name']}\"
APP_ENV=production
APP_KEY={$appKey}
APP_DEBUG=false
APP_URL={$config['app_url']}

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST={$config['db_host']}
DB_PORT={$config['db_port']}
DB_DATABASE={$config['db_database']}
DB_USERNAME={$config['db_username']}
DB_PASSWORD=\"{$config['db_password']}\"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER={$config['mail_driver']}
MAIL_HOST={$config['mail_host']}
MAIL_PORT={$config['mail_port']}
MAIL_USERNAME={$config['mail_username']}
MAIL_PASSWORD=\"{$config['mail_password']}\"
MAIL_ENCRYPTION={$config['mail_encryption']}
MAIL_FROM_ADDRESS=\"{$config['mail_from_address']}\"
MAIL_FROM_NAME=\"{$config['mail_from_name']}\"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY=\"\${PUSHER_APP_KEY}\"
VITE_PUSHER_HOST=\"\${PUSHER_HOST}\"
VITE_PUSHER_PORT=\"\${PUSHER_PORT}\"
VITE_PUSHER_SCHEME=\"\${PUSHER_SCHEME}\"
VITE_PUSHER_APP_CLUSTER=\"\${PUSHER_APP_CLUSTER}\"

# License Configuration
LICENSE_KEY=
PURCHASE_CODE=
LICENSE_DOMAIN=
";
    }

    /**
     * Validate license with remote server
     */
    public function validateLicense(string $licenseKey, string $purchaseCode): bool
    {
        try {
            $response = Http::timeout(30)->post($this->licenseServerUrl, [
                'license_key' => $licenseKey,
                'purchase_code' => $purchaseCode,
                'domain' => request()->getHost(),
                'ip' => request()->ip(),
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['valid'] ?? false) {
                    // Save license information
                    $this->saveLicenseInfo($licenseKey, $purchaseCode, $data);
                    return true;
                }
            }

            return false;
        } catch (Exception $e) {
            // In case of network issues, allow installation but mark for later verification
            $this->saveLicenseInfo($licenseKey, $purchaseCode, ['status' => 'pending_verification']);
            return true;
        }
    }

    /**
     * Save license information
     */
    private function saveLicenseInfo(string $licenseKey, string $purchaseCode, array $data): void
    {
        $licenseData = [
            'license_key' => $licenseKey,
            'purchase_code' => $purchaseCode,
            'domain' => request()->getHost(),
            'activated_at' => now()->toISOString(),
            'status' => $data['status'] ?? 'active',
            'expires_at' => $data['expires_at'] ?? null,
        ];

        File::put(storage_path('license.json'), json_encode($licenseData, JSON_PRETTY_PRINT));
    }
}
