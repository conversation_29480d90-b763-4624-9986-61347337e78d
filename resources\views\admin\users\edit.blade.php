@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Edit User: {{ $user->name }}</h1>
            <div class="flex space-x-2">
                <a href="{{ route('admin.users.show', $user) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    View User
                </a>
                <a href="{{ route('admin.users.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Users
                </a>
            </div>
        </div>

        <form action="{{ route('admin.users.update', $user) }}" method="POST" class="max-w-2xl">
            @csrf
            @method('PUT')

            <div class="space-y-6">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $user->name) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input type="email" name="email" id="email" value="{{ old('email', $user->email) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                    <input type="password" name="password" id="password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           minlength="8">
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Leave empty to keep current password. Minimum 8 characters if changing.</p>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           minlength="8">
                    <p class="mt-1 text-xs text-gray-500">Required only if changing password</p>
                </div>

                <!-- Role -->
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role *</label>
                    <select name="role" id="role" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">Select Role</option>
                        <option value="user" {{ old('role', $user->role) == 'user' ? 'selected' : '' }}>User</option>
                        <option value="editor" {{ old('role', $user->role) == 'editor' ? 'selected' : '' }}>Editor</option>
                        <option value="admin" {{ old('role', $user->role) == 'admin' ? 'selected' : '' }}>Admin</option>
                    </select>
                    @error('role')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <div class="mt-2 text-xs text-gray-500">
                        <div><strong>User:</strong> Basic access, can view content</div>
                        <div><strong>Editor:</strong> Can create and edit blog posts</div>
                        <div><strong>Admin:</strong> Full access to all admin features</div>
                    </div>
                </div>

                <!-- Status -->
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', $user->is_active) ? 'checked' : '' }}
                               class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Active User</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500">Uncheck to deactivate this user account</p>
                </div>

                <!-- Preview -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">User Preview</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                <span id="initial-preview">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900" id="name-preview">{{ $user->name }}</div>
                                <div class="text-sm text-gray-500" id="email-preview">{{ $user->email }}</div>
                                <div class="text-xs text-gray-400">
                                    <span id="role-preview">{{ ucfirst($user->role) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Info -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Joined:</span>
                                <div class="text-gray-900 font-medium">{{ $user->created_at->format('M d, Y g:i A') }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Last Updated:</span>
                                <div class="text-gray-900 font-medium">{{ $user->updated_at->format('M d, Y g:i A') }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Email Verified:</span>
                                <div class="text-gray-900 font-medium">
                                    {{ $user->email_verified_at ? $user->email_verified_at->format('M d, Y') : 'Not verified' }}
                                </div>
                            </div>
                            @if($user->role === 'admin' || $user->role === 'editor')
                            <div>
                                <span class="text-gray-600">Blog Posts:</span>
                                <div class="text-gray-900 font-medium">{{ $user->blogPosts()->count() }} posts</div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-4 pt-6">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                        Update User
                    </button>
                    <a href="{{ route('admin.users.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Update preview
function updatePreview() {
    const name = document.getElementById('name').value || '{{ $user->name }}';
    const email = document.getElementById('email').value || '{{ $user->email }}';
    const role = document.getElementById('role').value || '{{ $user->role }}';
    
    document.getElementById('name-preview').textContent = name;
    document.getElementById('email-preview').textContent = email;
    document.getElementById('role-preview').textContent = role.charAt(0).toUpperCase() + role.slice(1);
    
    // Update initial
    const initial = name.charAt(0).toUpperCase() || '?';
    document.getElementById('initial-preview').textContent = initial;
}

document.getElementById('name').addEventListener('input', updatePreview);
document.getElementById('email').addEventListener('input', updatePreview);
document.getElementById('role').addEventListener('change', updatePreview);

// Password confirmation validation
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmation = this.value;
    
    if (password && password !== confirmation) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const confirmation = document.getElementById('password_confirmation');
    if (confirmation.value) {
        confirmation.dispatchEvent(new Event('input'));
    }
});
</script>
@endsection
