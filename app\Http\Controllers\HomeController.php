<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\Page;
use App\Models\Popup;
use App\Models\Service;
use App\Models\Project;
use App\Models\Team;
use App\Models\Partner;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $page = Page::where('slug', 'home')->first();
        $featuredServices = Service::published()->featured()->ordered()->limit(6)->get();
        $latestBlogPosts = BlogPost::published()->latest()->limit(3)->get();
        $activePopups = Popup::active()->get();

        // New sections data
        $featuredProjects = Project::getFeaturedProjects(6);
        $executiveTeam = Team::getExecutiveTeam();
        $projectTeam = Team::getProjectTeam();
        $featuredPartners = Partner::getFeaturedPartners(8);
        $featuredClients = Partner::getFeaturedClients(8);
        $featuredTestimonials = Testimonial::getFeaturedTestimonials(3);

        return view('home', compact(
            'page',
            'featuredServices',
            'latestBlogPosts',
            'activePopups',
            'featuredProjects',
            'executiveTeam',
            'projectTeam',
            'featuredPartners',
            'featuredClients',
            'featuredTestimonials'
        ));
    }
}
