<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('testimonials', function (Blueprint $table) {
            $table->string('client_name', 500)->change();
            $table->string('client_position', 500)->nullable()->change();
            $table->string('client_company', 500)->nullable()->change();
            $table->string('project_title', 500)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('testimonials', function (Blueprint $table) {
            $table->string('client_name', 255)->change();
            $table->string('client_position', 255)->nullable()->change();
            $table->string('client_company', 255)->nullable()->change();
            $table->string('project_title', 255)->nullable()->change();
        });
    }
};
