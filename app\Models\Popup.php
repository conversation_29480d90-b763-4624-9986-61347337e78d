<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Popup extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'image',
        'cta_text',
        'cta_url',
        'trigger_type',
        'trigger_value',
        'is_active',
        'start_date',
        'end_date',
        'display_pages',
        'display_frequency',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'display_pages' => 'array',
    ];

    /**
     * Scope for active popups
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    /**
     * Check if popup should be displayed on given page
     */
    public function shouldDisplayOnPage(string $page): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Check date range
        if ($this->start_date && $this->start_date > now()) {
            return false;
        }

        if ($this->end_date && $this->end_date < now()) {
            return false;
        }

        // Check page restrictions
        if ($this->display_pages && !empty($this->display_pages)) {
            return in_array($page, $this->display_pages);
        }

        return true;
    }

    /**
     * Get trigger configuration for frontend
     */
    public function getTriggerConfigAttribute(): array
    {
        return [
            'type' => $this->trigger_type,
            'value' => $this->trigger_value,
        ];
    }

    /**
     * Get available trigger types
     */
    public static function getTriggerTypes(): array
    {
        return [
            'on_load' => 'On Page Load',
            'on_scroll' => 'On Scroll (%)',
            'on_delay' => 'After Delay (seconds)',
        ];
    }

    /**
     * Get available pages for display
     */
    public static function getAvailablePages(): array
    {
        return [
            'home' => 'Home Page',
            'about' => 'About Page',
            'services' => 'Services Page',
            'blog' => 'Blog Pages',
            'contact' => 'Contact Page',
            'all' => 'All Pages',
        ];
    }
}
