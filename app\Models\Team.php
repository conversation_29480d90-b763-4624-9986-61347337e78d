<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Team extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'department',
        'bio',
        'email',
        'phone',
        'linkedin',
        'twitter',
        'image',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeExecutive($query)
    {
        return $query->where('department', 'executive');
    }

    public function scopeProject($query)
    {
        return $query->where('department', 'project');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if ($this->image && Storage::disk('public')->exists($this->image)) {
            return asset('storage/' . $this->image);
        }
        return null;
    }

    public function getDepartmentNameAttribute()
    {
        return match($this->department) {
            'executive' => 'Executive Management',
            'project' => 'Project Management',
            default => ucfirst($this->department),
        };
    }

    // Static methods
    public static function getExecutiveTeam()
    {
        return static::active()->executive()->ordered()->get();
    }

    public static function getProjectTeam()
    {
        return static::active()->project()->ordered()->get();
    }
}
