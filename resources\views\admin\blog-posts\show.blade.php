@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">{{ $blogPost->title }}</h1>
            <div class="flex space-x-2">
                @if($blogPost->is_published && $blogPost->published_at <= now())
                    <a href="{{ route('blog.show', $blogPost) }}" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        View Live Post
                    </a>
                @endif
                <a href="{{ route('admin.blog-posts.edit', $blogPost) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Post
                </a>
                <a href="{{ route('admin.blog-posts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Posts
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Featured Image -->
                @if($blogPost->featured_image)
                    <div class="mb-6">
                        <img src="{{ asset('storage/' . $blogPost->featured_image) }}" alt="{{ $blogPost->title }}" class="w-full h-64 object-cover rounded-lg">
                    </div>
                @endif

                <!-- Excerpt -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Excerpt</h3>
                    <p class="text-gray-700 italic">{{ $blogPost->excerpt }}</p>
                </div>

                <!-- Content -->
                <div class="prose prose-lg max-w-none">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Content</h3>
                    {!! $blogPost->content !!}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Post Status -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Post Status</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Status:</span>
                            @if($blogPost->is_published && $blogPost->published_at <= now())
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Published
                                </span>
                            @elseif($blogPost->is_published && $blogPost->published_at > now())
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Scheduled
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    Draft
                                </span>
                            @endif
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Featured:</span>
                            @if($blogPost->is_featured)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Featured
                                </span>
                            @else
                                <span class="text-sm text-gray-500">No</span>
                            @endif
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Views:</span>
                            <span class="text-sm text-gray-900">{{ number_format($blogPost->views_count) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Slug:</span>
                            <span class="text-sm text-gray-900 font-mono">{{ $blogPost->slug }}</span>
                        </div>
                    </div>
                </div>

                <!-- Author & Category -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Author & Category</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-gray-600">Author:</span>
                            <div class="flex items-center space-x-2 mt-1">
                                <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    {{ substr($blogPost->user->name, 0, 1) }}
                                </div>
                                <span class="text-sm text-gray-900">{{ $blogPost->user->name }}</span>
                            </div>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">Category:</span>
                            <div class="mt-1">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" style="background-color: {{ $blogPost->category->color }}20; color: {{ $blogPost->category->color }};">
                                    {{ $blogPost->category->name }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tags -->
                @if($blogPost->tags->count() > 0)
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach($blogPost->tags as $tag)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white" style="background-color: {{ $tag->color }};">
                                {{ $tag->name }}
                            </span>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Post Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Post Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Created:</span>
                            <div class="text-gray-900">{{ $blogPost->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $blogPost->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @if($blogPost->published_at)
                            <div>
                                <span class="text-gray-600">Published:</span>
                                <div class="text-gray-900">{{ $blogPost->published_at->format('M d, Y g:i A') }}</div>
                            </div>
                        @endif
                        @if($blogPost->is_published)
                            <div>
                                <span class="text-gray-600">URL:</span>
                                <div class="text-blue-600 hover:text-blue-800">
                                    <a href="{{ route('blog.show', $blogPost) }}" target="_blank">/blog/{{ $blogPost->slug }}</a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- SEO Information -->
                @if($blogPost->meta_title || $blogPost->meta_description || $blogPost->meta_keywords)
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Information</h3>
                    <div class="space-y-3 text-sm">
                        @if($blogPost->meta_title)
                        <div>
                            <span class="text-gray-600">Meta Title:</span>
                            <div class="text-gray-900">{{ $blogPost->meta_title }}</div>
                        </div>
                        @endif
                        
                        @if($blogPost->meta_description)
                        <div>
                            <span class="text-gray-600">Meta Description:</span>
                            <div class="text-gray-900">{{ $blogPost->meta_description }}</div>
                        </div>
                        @endif
                        
                        @if($blogPost->meta_keywords)
                        <div>
                            <span class="text-gray-600">Meta Keywords:</span>
                            <div class="text-gray-900">{{ $blogPost->meta_keywords }}</div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.blog-posts.edit', $blogPost) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Post
                        </a>
                        
                        <form action="{{ route('admin.blog-posts.destroy', $blogPost) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this blog post? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Delete Post
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
