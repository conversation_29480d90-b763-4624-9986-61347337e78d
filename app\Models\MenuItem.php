<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'menu_location',
        'title',
        'url',
        'route_name',
        'route_params',
        'target',
        'css_class',
        'icon',
        'parent_id',
        'sort_order',
        'is_active',
        'description',
    ];

    protected $casts = [
        'route_params' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function parent()
    {
        return $this->belongsTo(MenuItem::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(MenuItem::class, 'parent_id')->orderBy('sort_order');
    }

    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeMainMenu($query)
    {
        return $query->where('menu_location', 'main');
    }

    public function scopeFooterMenu($query)
    {
        return $query->where('menu_location', 'footer');
    }

    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function scopeByLocation($query, $location)
    {
        return $query->where('menu_location', $location);
    }

    // Accessors
    public function getFullUrlAttribute()
    {
        if ($this->route_name) {
            try {
                return route($this->route_name, $this->route_params ?? []);
            } catch (\Exception $e) {
                return $this->url ?? '#';
            }
        }

        return $this->url ?? '#';
    }

    public function getIsCurrentAttribute()
    {
        $currentUrl = request()->url();
        $currentRoute = request()->route() ? request()->route()->getName() : null;

        // Check if current route matches
        if ($this->route_name && $currentRoute === $this->route_name) {
            return true;
        }

        // Check if current URL matches
        if ($this->url && $currentUrl === $this->full_url) {
            return true;
        }

        return false;
    }

    public function getIsActiveInPathAttribute()
    {
        if ($this->is_current) {
            return true;
        }

        // Check if any child is current
        foreach ($this->children as $child) {
            if ($child->is_current || $child->is_active_in_path) {
                return true;
            }
        }

        return false;
    }

    public function getHasChildrenAttribute()
    {
        return $this->children()->count() > 0;
    }

    public function getDepthAttribute()
    {
        $depth = 0;
        $parent = $this->parent;

        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }

        return $depth;
    }

    // Static methods
    public static function getMenuTree($location = 'main', $activeOnly = true)
    {
        $query = static::byLocation($location)->topLevel()->ordered()->with(['allChildren' => function ($query) use ($activeOnly) {
            if ($activeOnly) {
                $query->active();
            }
            $query->ordered();
        }]);

        if ($activeOnly) {
            $query->active();
        }

        return $query->get();
    }

    public static function getAvailableLocations()
    {
        return [
            'main' => 'Main Navigation',
            'footer' => 'Footer Menu',
            'sidebar' => 'Sidebar Menu',
            'mobile' => 'Mobile Menu',
        ];
    }

    public static function getAvailableTargets()
    {
        return [
            '_self' => 'Same Window',
            '_blank' => 'New Window',
            '_parent' => 'Parent Frame',
            '_top' => 'Top Frame',
        ];
    }

    // Helper methods
    public function moveToPosition($newPosition, $newParentId = null)
    {
        $oldParentId = $this->parent_id;

        // Update parent if changed
        if ($newParentId !== $oldParentId) {
            $this->parent_id = $newParentId;
        }

        // Get siblings (items with same parent)
        $siblings = static::where('parent_id', $newParentId)
            ->where('id', '!=', $this->id)
            ->ordered()
            ->get();

        // Reorder siblings
        $order = 0;
        foreach ($siblings as $sibling) {
            if ($order == $newPosition) {
                $order++; // Skip position for current item
            }
            $sibling->update(['sort_order' => $order]);
            $order++;
        }

        // Set position for current item
        $this->sort_order = $newPosition;
        $this->save();
    }
}
