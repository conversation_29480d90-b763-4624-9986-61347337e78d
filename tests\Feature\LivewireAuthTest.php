<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;
use Livewire\Livewire;

class LivewireAuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role' => 'editor',
        ]);
    }

    /** @test */
    public function login_page_can_be_rendered()
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        $response->assertSee('Welcome Back!');
        $response->assertSee('Please sign in to your account');
    }

    /** @test */
    public function register_page_can_be_rendered()
    {
        $response = $this->get('/register');

        $response->assertStatus(200);
        $response->assertSee('Create Account');
        $response->assertSee('Join us and start your journey');
    }

    /** @test */
    public function forgot_password_page_can_be_rendered()
    {
        $response = $this->get('/forgot-password');

        $response->assertStatus(200);
        $response->assertSee('Reset Password');
        $response->assertSee('Enter your email to receive a reset link');
    }

    /** @test */
    public function user_can_login_with_correct_credentials()
    {
        $component = Livewire::test('livewire.pages.auth.login')
            ->set('form.email', '<EMAIL>')
            ->set('form.password', 'password')
            ->call('login');

        $component->assertRedirect('/dashboard');
        $this->assertAuthenticated();
    }

    /** @test */
    public function user_cannot_login_with_incorrect_credentials()
    {
        $component = Livewire::test('livewire.pages.auth.login')
            ->set('form.email', '<EMAIL>')
            ->set('form.password', 'wrong-password')
            ->call('login');

        $component->assertHasErrors(['form.email']);
        $this->assertGuest();
    }

    /** @test */
    public function user_can_register_with_valid_data()
    {
        $component = Livewire::test('livewire.pages.auth.register')
            ->set('name', 'New User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password123')
            ->set('password_confirmation', 'password123')
            ->call('register');

        $component->assertRedirect('/dashboard');
        $this->assertAuthenticated();
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'New User',
        ]);
    }

    /** @test */
    public function user_cannot_register_with_invalid_email()
    {
        $component = Livewire::test('livewire.pages.auth.register')
            ->set('name', 'New User')
            ->set('email', 'invalid-email')
            ->set('password', 'password123')
            ->set('password_confirmation', 'password123')
            ->call('register');

        $component->assertHasErrors(['email']);
        $this->assertGuest();
    }

    /** @test */
    public function user_cannot_register_with_mismatched_passwords()
    {
        $component = Livewire::test('livewire.pages.auth.register')
            ->set('name', 'New User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password123')
            ->set('password_confirmation', 'different-password')
            ->call('register');

        $component->assertHasErrors(['password']);
        $this->assertGuest();
    }

    /** @test */
    public function authenticated_user_can_access_dashboard()
    {
        $user = User::where('email', '<EMAIL>')->first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function guest_cannot_access_dashboard()
    {
        $response = $this->get('/dashboard');
        
        $response->assertRedirect('/login');
    }

    /** @test */
    public function user_can_logout()
    {
        $user = User::where('email', '<EMAIL>')->first();
        
        $response = $this->actingAs($user)->post('/logout');
        
        $response->assertRedirect('/');
        $this->assertGuest();
    }
}
