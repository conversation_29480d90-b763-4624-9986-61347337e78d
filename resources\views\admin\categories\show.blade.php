@extends('layouts.admin')

@section('content')
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 rounded-full" style="background-color: {{ $category->color }};"></div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $category->name }}</h1>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('admin.categories.edit', $category) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Edit Category
                </a>
                <a href="{{ route('admin.categories.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Categories
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Category Description -->
                @if($category->description)
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Description</h3>
                    <p class="text-gray-700">{{ $category->description }}</p>
                </div>
                @endif

                <!-- Category Preview -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Category Preview</h3>
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full" style="background-color: {{ $category->color }}20; color: {{ $category->color }};">
                        {{ $category->name }}
                    </span>
                </div>

                <!-- Recent Blog Posts -->
                @if($recentPosts->count() > 0)
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Blog Posts ({{ $category->blog_posts_count }} total)</h3>
                    <div class="space-y-4">
                        @foreach($recentPosts as $post)
                        <div class="border-l-4 {{ $post->is_published ? 'border-green-400' : 'border-gray-300' }} pl-4">
                            <div class="flex items-center justify-between">
                                <h4 class="font-semibold text-gray-900">
                                    <a href="{{ route('admin.blog-posts.show', $post) }}" class="hover:text-green-600">
                                        {{ Str::limit($post->title, 50) }}
                                    </a>
                                </h4>
                                <span class="text-sm text-gray-500">{{ $post->created_at->format('M d, Y') }}</span>
                            </div>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="text-xs text-gray-500">By {{ $post->user->name }}</span>
                                @if($post->is_published)
                                    <span class="text-xs text-green-600">Published</span>
                                @else
                                    <span class="text-xs text-gray-500">Draft</span>
                                @endif
                                @if($post->is_featured)
                                    <span class="text-xs text-yellow-600">Featured</span>
                                @endif
                                <span class="text-xs text-gray-400">{{ number_format($post->views_count) }} views</span>
                            </div>
                            @if($post->tags->count() > 0)
                                <div class="flex flex-wrap gap-1 mt-2">
                                    @foreach($post->tags as $tag)
                                        <span class="inline-flex px-1 py-0.5 text-xs font-semibold rounded text-white" style="background-color: {{ $tag->color }};">
                                            {{ $tag->name }}
                                        </span>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        @endforeach
                    </div>
                    
                    @if($category->blog_posts_count > 10)
                        <div class="mt-4 text-center">
                            <a href="{{ route('admin.blog-posts.index', ['category' => $category->id]) }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                View all {{ $category->blog_posts_count }} posts in this category →
                            </a>
                        </div>
                    @endif
                </div>
                @else
                <div class="bg-gray-50 p-6 rounded-lg text-center">
                    <div class="text-gray-400 text-4xl mb-3">📝</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No blog posts yet</h3>
                    <p class="text-gray-500 mb-4">This category doesn't have any blog posts yet.</p>
                    <a href="{{ route('admin.blog-posts.create') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Create New Post
                    </a>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Category Details -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Category Details</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Name:</span>
                            <div class="text-gray-900 font-medium">{{ $category->name }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Slug:</span>
                            <div class="text-gray-900 font-mono">{{ $category->slug }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Color:</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-4 h-4 rounded-full" style="background-color: {{ $category->color }};"></div>
                                <span class="text-gray-900 font-mono">{{ $category->color }}</span>
                            </div>
                        </div>
                        <div>
                            <span class="text-gray-600">Sort Order:</span>
                            <div class="text-gray-900 font-medium">{{ $category->sort_order }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Blog Posts:</span>
                            <div class="text-gray-900 font-medium">{{ $category->blog_posts_count }} posts</div>
                        </div>
                    </div>
                </div>

                <!-- Category Statistics -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Created:</span>
                            <div class="text-gray-900">{{ $category->created_at->format('M d, Y g:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <div class="text-gray-900">{{ $category->updated_at->format('M d, Y g:i A') }}</div>
                        </div>
                        @if($category->updated_at != $category->created_at)
                        <div>
                            <span class="text-gray-600">Last Modified:</span>
                            <div class="text-gray-900">{{ $category->updated_at->diffForHumans() }}</div>
                        </div>
                        @endif
                        @if($recentPosts->count() > 0)
                        <div>
                            <span class="text-gray-600">Latest Post:</span>
                            <div class="text-gray-900">{{ $recentPosts->first()->created_at->format('M d, Y') }}</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.categories.edit', $category) }}" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                            Edit Category
                        </a>
                        
                        @if($category->blog_posts_count == 0)
                            <form action="{{ route('admin.categories.destroy', $category) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    Delete Category
                                </button>
                            </form>
                        @else
                            <div class="w-full bg-gray-400 text-white font-bold py-2 px-4 rounded text-center cursor-not-allowed">
                                Cannot Delete (Has Posts)
                            </div>
                            <p class="text-xs text-gray-500 text-center">Move or delete all blog posts first</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
