@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Project Details</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.projects.edit', $project) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Edit
            </a>
            <a href="{{ route('admin.projects.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Projects
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Featured Image -->
                <div class="lg:col-span-1">
                    @if($project->featured_image_url)
                        <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" class="w-full rounded-lg shadow-md">
                    @else
                        <div class="w-full bg-gray-200 rounded-lg shadow-md flex items-center justify-center h-64">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-gray-400 rounded-lg mx-auto mb-4 flex items-center justify-center">
                                    <span class="text-2xl font-bold text-white">{{ substr($project->title, 0, 1) }}</span>
                                </div>
                                <p class="text-gray-500">No featured image</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Project Info -->
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <!-- Basic Info -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $project->title }}</h2>
                            <p class="text-lg text-gray-600 mb-4">{{ $project->description }}</p>
                            
                            <div class="flex flex-wrap gap-4 mb-4">
                                {!! $project->status_badge !!}
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $project->is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $project->is_featured ? 'Featured' : 'Regular' }}
                                </span>
                                <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                    {{ $project->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $project->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <!-- Project Details -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if($project->client)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Client</h3>
                                <p class="text-gray-700">{{ $project->client }}</p>
                            </div>
                            @endif

                            @if($project->category)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Category</h3>
                                <p class="text-gray-700">{{ $project->category }}</p>
                            </div>
                            @endif

                            @if($project->start_date)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Start Date</h3>
                                <p class="text-gray-700">{{ $project->start_date->format('M j, Y') }}</p>
                            </div>
                            @endif

                            @if($project->end_date)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">End Date</h3>
                                <p class="text-gray-700">{{ $project->end_date->format('M j, Y') }}</p>
                            </div>
                            @endif

                            @if($project->duration)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Duration</h3>
                                <p class="text-gray-700">{{ $project->duration }} days</p>
                            </div>
                            @endif

                            @if($project->project_url)
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-1">Project URL</h3>
                                <a href="{{ $project->project_url }}" target="_blank" class="text-green-600 hover:text-green-800 break-all">
                                    {{ $project->project_url }}
                                </a>
                            </div>
                            @endif
                        </div>

                        <!-- Technologies -->
                        @if($project->technologies && count($project->technologies) > 0)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Technologies Used</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($project->technologies as $tech)
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                                        {{ $tech }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Content -->
                        @if($project->content)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Project Details</h3>
                            <div class="prose max-w-none">
                                <p class="text-gray-700 leading-relaxed whitespace-pre-line">{{ $project->content }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Metadata -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                    <span class="font-medium">Slug:</span> {{ $project->slug }}
                                </div>
                                <div>
                                    <span class="font-medium">Sort Order:</span> {{ $project->sort_order }}
                                </div>
                                <div>
                                    <span class="font-medium">Created:</span> {{ $project->created_at->format('M j, Y g:i A') }}
                                </div>
                                <div>
                                    <span class="font-medium">Updated:</span> {{ $project->updated_at->format('M j, Y g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery -->
            @if($project->gallery_urls && count($project->gallery_urls) > 0)
            <div class="mt-8 border-t pt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Gallery</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($project->gallery_urls as $image)
                        <img src="{{ $image }}" alt="Gallery image" class="w-full h-32 object-cover rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                             onclick="openImageModal('{{ $image }}')">
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Action Buttons -->
        <div class="bg-gray-50 px-6 py-4 border-t">
            <div class="flex justify-between items-center">
                <div class="flex space-x-2">
                    <form action="{{ route('admin.projects.toggle-featured', $project) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                            {{ $project->is_featured ? 'Unfeature' : 'Feature' }}
                        </button>
                    </form>
                    
                    <form action="{{ route('admin.projects.toggle-status', $project) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            {{ $project->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>
                </div>
                
                <form action="{{ route('admin.projects.destroy', $project) }}" method="POST" class="inline" 
                      onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Delete Project
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="max-w-4xl max-h-full p-4">
        <img id="modalImage" src="" alt="Gallery image" class="max-w-full max-h-full object-contain">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300">×</button>
    </div>
</div>

<script>
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}

// Close modal when clicking outside the image
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>
@endsection
