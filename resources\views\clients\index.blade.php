@extends('layouts.public')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">Our Clients</h1>
                <p class="text-xl text-green-100 max-w-3xl mx-auto">
                    We're proud to work with amazing organizations that trust us to deliver exceptional results.
                </p>
            </div>
        </div>
    </div>

    <!-- Featured Clients Section -->
    @if($featuredClients->count() > 0)
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Clients</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Showcasing some of our most valued client relationships and successful collaborations.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                @foreach($featuredClients as $client)
                <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 border border-gray-200">
                    <div class="text-center">
                        @if($client->logo_url)
                            <div class="mb-4">
                                <img src="{{ $client->logo_url }}" alt="{{ $client->name }}" class="h-16 w-auto mx-auto object-contain">
                            </div>
                        @endif
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $client->name }}</h3>
                        @if($client->industry)
                            <p class="text-sm text-green-600 font-medium mb-3">{{ $client->industry }}</p>
                        @endif
                        @if($client->description)
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit($client->description, 120) }}</p>
                        @endif
                        <div class="flex justify-center space-x-3">
                            @if($client->website)
                                <a href="{{ $client->website }}" target="_blank" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            @endif
                            <a href="{{ route('clients.show', $client) }}" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                <i class="fas fa-info-circle"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- All Clients Section -->
    @if($clients->count() > 0)
    <div class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">All Clients</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    A comprehensive view of the organizations we've had the privilege to serve.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($clients as $client)
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-200">
                    <div class="text-center">
                        @if($client->logo_url)
                            <div class="mb-4">
                                <img src="{{ $client->logo_url }}" alt="{{ $client->name }}" class="h-12 w-auto mx-auto object-contain">
                            </div>
                        @endif
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $client->name }}</h3>
                        @if($client->industry)
                            <p class="text-sm text-green-600 font-medium mb-3">{{ $client->industry }}</p>
                        @endif
                        @if($client->description)
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ Str::limit($client->description, 100) }}</p>
                        @endif
                        <div class="flex justify-center space-x-3">
                            @if($client->website)
                                <a href="{{ $client->website }}" target="_blank" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            @endif
                            <a href="{{ route('clients.show', $client) }}" class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                <i class="fas fa-info-circle"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Empty State -->
    @if($clients->count() === 0 && $featuredClients->count() === 0)
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="max-w-md mx-auto">
                <i class="fas fa-users text-6xl text-gray-300 mb-6"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Clients Yet</h3>
                <p class="text-gray-600">We're building our client portfolio. Check back soon for updates!</p>
            </div>
        </div>
    </div>
    @endif

    <!-- Statistics Section -->
    <div class="bg-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-4xl font-bold text-green-600 mb-2">
                        <i class="fas fa-chart-line mr-2"></i>
                        {{ $clients->count() }}+
                    </div>
                    <p class="text-lg text-gray-600">Satisfied Clients</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-green-600 mb-2">
                        <i class="fas fa-globe mr-2"></i>
                        {{ $clients->pluck('industry')->unique()->count() }}+
                    </div>
                    <p class="text-lg text-gray-600">Industries Served</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-green-600 mb-2">
                        <i class="fas fa-award mr-2"></i>
                        100%
                    </div>
                    <p class="text-lg text-gray-600">Client Satisfaction</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="bg-green-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">Ready to Join Our Client Family?</h2>
            <p class="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
                Let's discuss how we can help your organization achieve its goals and drive success.
            </p>
            <a href="{{ route('contact') }}" class="inline-flex items-center px-8 py-3 bg-white text-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-300">
                <i class="fas fa-envelope mr-2"></i>
                Start a Conversation
            </a>
        </div>
    </div>
</div>
@endsection
