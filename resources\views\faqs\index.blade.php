@extends('layouts.public')

@section('title', 'Frequently Asked Questions')
@section('meta_description', 'Find answers to frequently asked questions about our services and company.')

@section('content')
<div class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Find answers to common questions about our services and how we can help your business grow.
            </p>
        </div>

        <!-- General FAQs -->
        @if($generalFaqs->count() > 0)
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">General Questions</h2>
            <div class="space-y-4">
                @foreach($generalFaqs as $faq)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-green-500 faq-toggle" 
                            data-target="general-{{ $faq->id }}">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">{{ $faq->question }}</h3>
                            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 faq-icon" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </button>
                    <div id="general-{{ $faq->id }}" class="faq-content hidden px-6 pb-4">
                        <div class="text-gray-700 prose prose-sm max-w-none">
                            {!! $faq->answer !!}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Service-Specific FAQs -->
        @if($serviceFaqs->count() > 0)
        @foreach($serviceFaqs as $serviceTitle => $faqs)
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">{{ $serviceTitle }} - Questions</h2>
            <div class="space-y-4">
                @foreach($faqs as $faq)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-green-500 faq-toggle" 
                            data-target="service-{{ $faq->id }}">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">{{ $faq->question }}</h3>
                            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 faq-icon" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </button>
                    <div id="service-{{ $faq->id }}" class="faq-content hidden px-6 pb-4">
                        <div class="text-gray-700 prose prose-sm max-w-none">
                            {!! $faq->answer !!}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endforeach
        @endif

        <!-- No FAQs Message -->
        @if($generalFaqs->count() == 0 && $serviceFaqs->count() == 0)
        <div class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">❓</div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No FAQs Available</h3>
            <p class="text-gray-500 mb-6">
                We're working on adding frequently asked questions. Please check back soon or contact us directly.
            </p>
            <a href="{{ route('contact') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                Contact Us
            </a>
        </div>
        @endif

        <!-- Contact CTA -->
        @if($generalFaqs->count() > 0 || $serviceFaqs->count() > 0)
        <div class="bg-green-50 rounded-lg p-8 text-center">
            <h3 class="text-xl font-bold text-gray-900 mb-2">Still have questions?</h3>
            <p class="text-gray-600 mb-4">
                Can't find the answer you're looking for? Our team is here to help.
            </p>
            <a href="{{ route('contact') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300">
                Get in Touch
            </a>
        </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');
    
    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
});
</script>
@endsection
